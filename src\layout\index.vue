<template>
  <div :class="classObj" class="app-wrapper">
    <template v-if="!upperComputerMode">
      <sidebar v-if="!sidebar.hide" class="sidebar-container" />
    </template>
    <div :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }" class="main-container">
      <div v-if="!upperComputerMode" :class="{ 'fixed-header': fixedHeader }">
        <navbar @setLayout="setLayout" />
      </div>
      <app-main />
    </div>
    <!-- <div class="nav-bar">
      <div class="flex items-center w-[400px] flex-none">
        <div class="w-auto font-bold text-[22px] ml-5" style="font-family: Source Han Sans CN">
          {{ title }}
        </div>
      </div>
      <div
        class="flex-1 h-[54px] items-center overflow-x-hidden overflow-y-hidden whitespace-nowrap"
      >
        <breadcrumb />
      </div>
      <PersonCenter class="text-[16px]" />
    </div>
    <div class="flex">
      <sidebar class="sidebar-container" />
      <app-main />
    </div> -->
  </div>
</template>

<script setup name="MainView">
  import Sidebar from './components/Sidebar/index.vue'
  import { AppMain, Navbar, TagsView } from './components'
  import useAppStore from '@/store/modules/app'
  import Breadcrumb from '@/components/Breadcrumb/index.vue'
  import PersonCenter from './components/PersonCenter/index.vue'
  import useSettingsStore from '@/store/modules/settings'
  import { settings as globalSettings } from '@/utils/settings'

  defineComponent([Sidebar, AppMain, TagsView, Breadcrumb, PersonCenter])

  const settingsStore = useSettingsStore()
  const sidebar = computed(() => useAppStore().sidebar)
  const needTagsView = computed(() => settingsStore.tagsView)
  const upperComputerMode = computed(() =>
    Boolean(globalSettings.VITE_GLOB_UPPER_COMPUTER_MODE * 1)
  )
  const fixedHeader = computed(() => settingsStore.fixedHeader)
  const classObj = computed(() => ({
    hideSidebar: !sidebar.value.opened,
    openSidebar: sidebar.value.opened,
    withoutAnimation: sidebar.value.withoutAnimation
  }))
  const title = ref(null)

  const settingRef = ref(null)

  function setLayout() {
    settingRef.value.openSetting()
  }

  // 页面挂载时启动定时器
  onMounted(() => {
    fetch('/config.json')
      .then(response => response.json())
      .then(data => {
        // 根据配置文件中的title字段更新页面标题
        title.value = data.title
      })
      .catch(error => {
        console.error('读取配置文件出错:', error)
      })
  })

  // 页面卸载时清理定时器
  onUnmounted(() => {})
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/mixin.scss';
  @import '@/assets/styles/variables.module.scss';

  .app-wrapper {
    @include clearfix;
    position: relative;
    // height: 100%;
    // width: 100%;
    width: 1000px;
    height: 900px;
    margin: 0 auto;
  }

  .drawer-bg {
    background: #000;
    opacity: 0.3;
    width: 100%;
    top: 0;
    height: 100%;
    position: absolute;
    z-index: 999;
  }

  .fixed-header {
    position: fixed;
    top: 0;
    right: 0;
    z-index: 9;
    width: calc(100% - #{$base-sidebar-width});
    transition: width 0.28s;
  }

  .hideSidebar .fixed-header {
    width: calc(100% - 54px);
  }

  .sidebarHide .fixed-header {
    width: 100%;
  }

  .mobile .fixed-header {
    width: 100%;
  }

  .app-top-views {
    display: flex;
    background-color: var(--el-bg-color);
    align-items: center;
    border-bottom: 1px solid #d8dce5;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
  }
</style>
