<script setup>
  import { selectDictLabel } from '@/utils/utils.js'
  import { activatePlan } from '@/api/pgmx/freqResolution.js'
  import { ElMessage } from 'element-plus'

  const props = defineProps({
    taskId: {
      type: String,
      default: ''
    },
    planId: {
      type: String,
      default: ''
    },
    conflictionAnalyzeId: {
      type: String,
      default: ''
    },
    resolutionType: {
      type: String,
      default: ''
    },
    taskOptions: {
      type: Array,
      default: () => []
    },
    planOptions: {
      type: Array,
      default: () => []
    }
  })
  // 对话框可见性
  const dialogTableVisible = defineModel({ type: Boolean, default: false })

  const confirm = async () => {
    await activatePlan({
      taskName: props.taskId,
      planId: props.planId,
      conflictionAnalyzeId: props.conflictionAnalyzeId,
      resolutionType: props.resolutionType
    }).then(res => {
      if (res.code === 200) {
        ElMessage({
          type: 'success',
          message: res.msg
        })
      } else {
        ElMessage({
          type: 'error',
          message: res.msg
        })
      }
    })
    dialogTableVisible.value = false
  }

  const cancel = () => {
    dialogTableVisible.value = false
  }
</script>

<template>
  <cu-dialog
    v-model="dialogTableVisible"
    title="启用用频方案"
    width="400"
    @confirm="confirm"
    @cancel="cancel"
  >
    <div class="mx-4">{{ selectDictLabel(props.taskOptions, props.taskId) }}</div>
    <div class="m-4">
      <div class="my-2">启用以下用频方案</div>
      <div class="mx-7">{{ selectDictLabel(props.planOptions, props.planId) }}</div>
    </div>
  </cu-dialog>
</template>

<style scoped lang="scss">
  :deep(.vxe-radio) {
    margin: 10px;
  }
</style>
