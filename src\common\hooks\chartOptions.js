import useThemeStyle from './themeStyle'
import useChartsStore from '@/store/modules/charts'
import { numToPlot } from '@/utils/utils'
import { CustomMarker } from '@/common'
import { round } from 'lodash'
import { computed } from 'vue'

export default function (type, showNum = 1024) {
  const { themeStyle } = useThemeStyle()
  const { global, viewNum } = useChartsStore()

  const generateyAxis = (colorSettings, type) => {
    const axis = {
      title: {
        enabled: false
      },
      gridLineColor: colorSettings.gridLineColor,
      gridLineDashStyle: 'dash',
      lineColor: colorSettings.axisLineColor,
      tickAmount: 11,
      labels: {
        style: {
          color: colorSettings.labelColor
        }
      }
    }
    // 根据不同类型增加单位
    if (type === 'sequential') {
      axis.unit = 'dBm'
    } else if (type === 'ccdf') {
      axis.type = 'logarithmic'
      axis.max = 1
      axis.min = 1 / 1e6
      axis.tickAmount = undefined
      axis.formatter = function () {
        const value = multiply(this.value, 100) + '%'
        if (value.length > 10) {
          return ceil(multiply(this.value, 100), 4) + '%'
        }
        return value
      }
    }
    return axis
  }

  const generateChartEvents = (colorSettings, type) => {
    if (type === 'harmonic') {
      return null
    }
    return {
      load() {
        if (this.customMarker) {
          return
        }
        new CustomMarker(this, this.userOptions.xAxis.labels)
        if (this.userOptions.yAxis.unit) {
          this.renderer
            .text(this.userOptions.yAxis.unit, 50, 20)
            .attr({
              zIndex: 5,
              fill: colorSettings.labelColor
            })
            .add()
        }
      },
      click(event) {
        const { chartX, chartY } = event
        const caliX = chartX
        const caliY = chartY
        const point = this.pointer.normalize(event)
        const paramPoint = this.series[0].searchPoint(point)
        if (paramPoint && this.series.length === 0) {
          this.customMarker?.markerPoint(paramPoint, [0])
        } else {
          this.customMarker?.markerPoint([caliX, caliY])
        }
      }
    }
  }

  const generatexAxis = (colorSettings, type) => {
    const axis = {
      labels: {
        style: {
          color: colorSettings.labelColor
        }
      },
      tickAmount: 11,
      gridLineWidth: 1,
      gridLineColor: colorSettings.gridLineColor,
      gridLineDashStyle: 'dash',
      lineColor: colorSettings.axisLineColor,
      zoomEnabled: false
    }
    if (type === 'sequential') {
      axis.max = showNum
      axis.min = 0
      axis.tickInterval = showNum / 10
      axis.labels.formatter = function () {
        const val = (this.value / showNum) * global.swpTime
        return round(val, 2) + 'us'
      }
    } else if (type === 'ccdf') {
      axis.min = 0
      axis.max = 20
      axis.labels.renderFormatter = function () {
        return this.value + 'dBm'
      }
      axis.labels.formatter = function () {
        if (this.value === 0 || this.value === 20) {
          return this.value + 'dbm'
        }
        return ''
      }
    } else if (type === 'harmonic') {
      axis.min = 0
      axis.max = 10000
      axis.tickAmount = 11
      axis.labels.enabled = false
      axis.tickPosition = 'inside'
    } else if (type === 'spectrum') {
      const left = global.centerFreqIn - global.intermediateFrequencyBandwidth / 2
      axis.max = showNum - 1
      axis.min = 0
      axis.tickAmount = 11
      axis.tickInterval = (showNum - 1) / 10
      axis.labels.formatter = function () {
        const val = (this.value / (showNum - 1)) * global.intermediateFrequencyBandwidth + left
        return numToPlot(val, '', 3)
      }
    }
    return axis
  }

  const generateChartOptions = (colorSettings, type) => {
    const chart = {
      backgroundColor: colorSettings.chartBgColor,
      type: 'line',
      height: 500,
      spacingTop: 30,
      spacingBottom: 20,
      spacingLeft: 10,
      spacingRight: 20,
      events: generateChartEvents(colorSettings, type)
    }
    const xAxis = generatexAxis(colorSettings, type)
    const yAxis = generateyAxis(colorSettings, type) // 自动计算 y 轴范围

    return {
      chart,
      exporting: {
        enabled: false
      },
      reflow: true,
      credits: {
        //版权
        enabled: false
      },
      xAxis,
      yAxis,
      title: {
        enabled: false,
        text: '',
        style: {
          color: '#f3f3f3'
        }
      },
      boost: {
        enabled: true,
        useGPUTranslations: true,
        usePreAllocated: true,
        seriesThreshold: 1
      },
      accessibility: {
        enabled: false
      },
      legend: {
        enabled: false
      },
      series: [
        {
          color: colorSettings.lineColor,
          marker: {
            enabled: false
          },
          animation: false,
          enableMouseTracking: false,
          type: 'line',
          data: [],
          lineWidth: 0.5
        }
      ],
      plotOptions: {
        series: {
          turboThreshold: 5000, // 启用 Turbo 模式，当数据量大于 1000 时启用
          boostThreshold: 4000,
          marker: { enabled: false },
          animation: false,
          enableMouseTracking: false, // 如果不需要交互，禁用鼠标跟踪
          states: {
            hover: {
              enabled: false
            }
          }
        },
        column: {
          borderRadius: '25%'
        }
      },
    }
  }

  const chartOptions = computed(() => generateChartOptions(themeStyle.value, type))
  return { chartOptions, themeStyle }
}
