import { mergeChunks } from './upload'

let fileChunkList = {
  value: []
}

let fileHash = null

const setFileChunkList = (val) => {
  fileChunkList = val
}

const setFileHash = (val) => {
  fileHash = val
}

/**
 * 清除文件哈希值，如果上传失败则进行文件合并
 * @param {boolean} uploadSuccess - 上传是否成功
 * @param {File} file - 文件对象
 * @param {Object} params - 附加参数
 */
const clearFileHash = (uploadSuccess, file, params) => {
  if (!uploadSuccess && fileHash) {
    const formData = new FormData()
    formData.append('fileName', file.name)
    formData.append('md5', fileHash)
    mergeChunks(formData, params)
    fileChunkList.value = []  // 保持一致性，使用.value
  }
  fileHash = null
}

export { fileChunkList, setFileChunkList, fileHash, setFileHash, clearFileHash }
