<script setup>
  import { ref, computed, reactive } from 'vue'
  import useList from '@/api/tool/filemanage/tableFunctionPro'
  import { readAllRecords, deleteRecord } from '@/api/pgmx/freqReqAnalysis.js'

  const filterOption = ref({})
  const xTable = ref(null)
  const {
    list,
    loading,
    curPage,
    size,
    total,
    timeSearch,
    deleteCurrentLine,
    deleteAll,
    selectChangeEvent
  } = useList(readAllRecords, deleteRecord, filterOption.value, xTable)

  const onlyShowError = ref(false)
  const taskName = ref('')

  const demandMatch = () => {
    // 这里编写需求匹配的逻辑
  }

  const handleEdit = row => {
    // 这里编写编辑的逻辑
  }

  // 异常行判定逻辑
  function isErrorRow(row) {
    // 某一项为2即为异常（可自行扩展规则）
    return row.freqMatchResult === 2 || row.timeMatchResult === 2 || row.areaMatchResult === 2
  }

  // 用于表格的数据（只看异常时过滤）
  const tableData = computed(() => {
    if (!onlyShowError.value) return list.value
    return list.value.length > 0 ? list.value.filter(isErrorRow) : []
  })

  // 配置tooltip
  const tooltipConfig = reactive({
    showAll: true,
    enterable: true,
    contentMethod: ({ column, row }) => {
      const { field } = column
      if (
        (field === 'freqMatchResult' ||
          field === 'timeMatchResult' ||
          field === 'areaMatchResult') &&
        row[field] === 2
      ) {
        return '异常行提示信息'
      } else {
        return ''
      }
    }
  })
</script>

<template>
  <div class="flex items-center mb-4">
    <div class="w-[80px] flex items-center">作战任务</div>
    <vxe-input v-model="taskName" placeholder="请输入任务名称" />
    <cu-button
      width="98px"
      type="primary"
      class="ml-5"
      content="需求匹配"
      @click="demandMatch"
    ></cu-button>
  </div>

  <vxe-checkbox class="mb-2" v-model="onlyShowError">只看异常</vxe-checkbox>
  <vxe-table
    ref="xTable"
    border
    stripe
    size="medium"
    height="700"
    :data="tableData"
    :loading="loading"
    :row-config="{ isCurrent: true, isHover: true }"
    :tooltip-config="tooltipConfig"
  >
    <vxe-column
      field="frequencyUsingUnit"
      title="用频单位"
      fixed="left"
      align="center"
      min-width="120"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column width="100" field="equipmentModel" title="装备型号" align="center" />
    <vxe-column width="100" field="freqUsageEquipment" title="配装平台" align="center" />
    <vxe-column
      width="120"
      field="freqRange"
      title="工作频段"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column width="100" field="freqPointNum" title="频点数量" align="center" />
    <vxe-column
      width="180"
      field="usageTime"
      title="用频时间"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column width="100" field="usageArea" title="用频地区" align="center" />
    <vxe-column width="100" field="freqMatchResult" title="频点匹配" align="center">
      <template #default="{ row }">
        <span :class="{ 'text-red-500': row.freqMatchResult === 2 }">{{
          row.freqMatchResult === 1 ? '正常' : '异常'
        }}</span>
      </template>
    </vxe-column>
    <vxe-column width="100" field="timeMatchResult" title="时间匹配" align="center">
      <template #default="{ row }">
        <span :class="{ 'text-red-500': row.timeMatchResult === 2 }">{{
          row.timeMatchResult === 1 ? '正常' : '异常'
        }}</span>
      </template>
    </vxe-column>
    <vxe-column width="100" field="areaMatchResult" title="地区匹配" align="center">
      <template #default="{ row }">
        <span :class="{ 'text-red-500': row.areaMatchResult === 2 }">{{
          row.areaMatchResult === 1 ? '正常' : '异常'
        }}</span>
      </template>
    </vxe-column>
    <vxe-column field="操作" title="操作" width="120" fixed="right" align="center">
      <template #default="{ row }">
        <cu-button type="info" content="编辑" @click="handleEdit(row)" />
        <cu-button type="info" content="删除" @click="deleteCurrentLine(row)" />
      </template>
    </vxe-column>
  </vxe-table>
  <!-- 分页 -->
  <p>
    <vxe-pager
      v-model:current-page="curPage"
      v-model:page-size="size"
      class="vxe-page"
      perfect
      :total="total"
      :page-sizes="[10, 20, 50, 100, 200, 500]"
      :layouts="[
        'PrevJump',
        'PrevPage',
        'Number',
        'NextPage',
        'NextJump',
        'Sizes',
        'FullJump',
        'Total'
      ]"
    />
  </p>
</template>
