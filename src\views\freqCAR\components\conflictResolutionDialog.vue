<script setup>
  import { processDictionary } from '@/utils/index.js'

  // 对话框可见性
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const resolutionType = defineModel('resolutionType', {
    type: String,
    default: '1'
  })
  const emits = defineEmits(['confirmConflictResolution'])
  const resolutionOptions = ref([])

  const getDictionaryData = async () => {
    processDictionary('pgmx_resolution', resolutionOptions)
  }

  const confirm = () => {
    dialogTableVisible.value = false
    // 这里编写保存方案的逻辑
    emits('confirmConflictResolution')
  }

  const cancel = () => {
    dialogTableVisible.value = false
  }

  onMounted(async () => {
    await getDictionaryData()
  })
</script>

<template>
  <cu-dialog
    v-model="dialogTableVisible"
    title="冲突消解"
    width="400"
    @confirm="confirm"
    @cancel="cancel"
  >
    <vxe-form vertical :title-colon="true" :label-config="{ width: '120px', align: 'left' }">
      <vxe-form-item title="冲突消解纬度" span="24">
        <vxe-radio-group v-model="resolutionType">
          <vxe-radio
            style="width: 100%"
            v-for="item in resolutionOptions"
            :label="item.value"
            :content="item.label"
          ></vxe-radio>
        </vxe-radio-group>
      </vxe-form-item>
    </vxe-form>
  </cu-dialog>
</template>

<style scoped lang="scss">
  :deep(.vxe-radio) {
    margin: 10px;
  }
</style>
