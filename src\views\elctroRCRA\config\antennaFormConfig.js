
export const antennaFormConfig = [
  {
    title: '天线名称',
    field: 'name',
    span: 12,
    itemRender: { name: 'VxeInput' },
  },
  {
    title: '天线编码',
    field: 'code',
    span: 12,
    itemRender: { name: 'VxeInput' },
  },
  {
    title: '天线类型',
    field: 'antennaType',
    span: 12,
    itemRender: {
      name: 'VxeSelect', options: [{ label: '是', value: true }, { label: '否', value: false }]
    },
  },
  {
    title: '主瓣增益(dBi)',
    field: 'mainBeamGain',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '频率上限(MHz)',
    field: 'freqUpperLimit',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '频率下限(MHz)',
    field: 'freqLowerLimit',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '天线高度(m)',
    field: 'antennaHeight',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '极化方式',
    field: 'polarizationType',
    span: 12,
    itemRender: {
      name: 'VxeSelect', options: [{ label: '是', value: true }, { label: '否', value: false }]
    },
  },
  {
    title: '垂直波束宽度(°)',
    field: 'verticalBeamwidth',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '水平波束宽度(°)',
    field: 'horizontalBeamwidth',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '平均副瓣电平(dB)',
    field: 'averageSideLobeLevel',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '第一旁瓣电平(dB)',
    field: 'firstSideLobeLevel',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '主瓣旁瓣夹角(°)',
    field: 'mainSideLobeAngle',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '交叉极化隔离(dB)',
    field: 'crossPolarizationIsolation',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '扫描方式',
    field: 'scanningMode',
    span: 12,
    itemRender: {
      name: 'VxeSelect', options: [{ label: '是', value: true }, { label: '否', value: false }]
    },
  },
  {
    title: '垂直扫描速率(°/s)',
    field: 'verticalScanRate',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '水平扫描速率(°/s)',
    field: 'horizontalScanRate',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '垂直最小俯仰角(°/s)',
    field: 'minVerticalElevation',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '垂直最大俯仰角(°/s)',
    field: 'maxVerticalElevation',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '水平最小方位角(°/s)',
    field: 'minHorizontalAzimuth',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '水平最大方位角(°/s)',
    field: 'maxHorizontalAzimuth',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
]
