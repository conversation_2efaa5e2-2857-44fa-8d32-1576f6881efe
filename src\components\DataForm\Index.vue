<template>
  <!-- 渲染表单的占位符组件 -->
  <render-form />
</template>

<script setup>
  // 引入 Vue 的核心 API 和 Element Plus 组件
  import { h } from 'vue'
  import {
    ElInput,
    ElForm,
    ElFormItem,
    ElCheckbox,
    ElButton,
    ElSelect,
    ElOption,
    ElRadio,
    ElRadioGroup
  } from 'element-plus'
  // 定义组件的 props，接收外部传入的字段配置、模型数据和验证规则
  const props = defineProps({
    fields: {
      type: Array,
      default: () => [] // 默认是一个空数组
    },
    model: {
      type: Object,
      default: () => ({}) // 默认是一个空对象
    },
    rules: {
      type: Array,
      default: () => [] // 默认是一个空数组
    }
  })

  // 当表单项发生变化时触发，更新模型数据并调用自定义变化事件
  const changeEvent = item => {
    props.model.setVal(item.target, getValue(props.model, item.target))
    item.change && item.change(props.model[item.target])
  }

  // 设置模型中的值，如果目标字段存在则更新其值
  const setValue = (obj, target, val) => {
    if (obj[target] === undefined) {
      return // 如果目标字段不存在，直接返回
    }
    if (obj[target].value === undefined) {
      obj[target] = val // 如果字段不是 ref 对象，直接赋值
    } else {
      obj[target].value = val // 如果是 ref 对象，更新其 value 属性
    }
  }

  // 获取模型中的值，支持简单值和 ref 对象
  const getValue = (obj, target) => {
    if (obj[target] === undefined) {
      return // 如果目标字段不存在，直接返回 undefined
    }
    if (obj[target].value === undefined) {
      return obj[target] // 如果字段不是 ref 对象，返回值本身
    } else {
      return obj[target].value // 如果是 ref 对象，返回其 value 属性
    }
  }

  // 根据字段类型动态生成表单项组件
  const generateCurrent = field => {
    // 定义组件的样式，特别是宽度
    const componentStyle = field.width ? { width: field.width } : { width: '85%' } // 如果没有设置宽度，则设置为 100%

    // 创建主组件，根据字段类型
    let component

    if (field.isCheckbox) {
      component = h(ElCheckbox, {
        modelValue: getValue(props.model, field.target),
        disabled: field.disabled,
        style: componentStyle, // 设置宽度
        'onUpdate:modelValue'(val) {
          setValue(props.model, field.target, val)
        },
        onChange: () => changeEvent(field),
        label: '启用'
      })
    } else if (field.isSelect) {
      component = h(
        ElSelect,
        {
          modelValue: getValue(props.model, field.target),
          disabled: field.disabled,
          style: componentStyle, // 设置宽度
          size: 'default', // 设置尺寸
          'onUpdate:modelValue'(val) {
            setValue(props.model, field.target, val)
          },
          onChange: () => changeEvent(field)
        },
        {
          default: () =>
            field.options.map(op =>
              h(ElOption, {
                key: op.value || op,
                value: op.value || op,
                label: op.label || op
              })
            ),
          append: () =>
            field.appendBtnText
              ? h(
                  ElButton,
                  {
                    onClick: () => field.clickEvent(),
                    size: 'default'
                  },
                  { default: () => [field.appendBtnText] }
                )
              : ''
        }
      )
    } else if (field.isRadio) {
      component = h(
        ElRadioGroup,
        {
          modelValue: getValue(props.model, field.target),
          disabled: field.disabled,
          style: componentStyle, // 设置宽度
          'onUpdate:modelValue'(val) {
            setValue(props.model, field.target, val)
          },
          onChange: () => changeEvent(field)
        },
        {
          default: () =>
            field.options.map(op =>
              h(
                ElRadio,
                {
                  key: op.value || op,
                  label: op.value || op
                },
                {
                  default: () => [op.label || op]
                }
              )
            )
        }
      )
    } else {
      component = h(
        ElInput,
        {
          modelValue: getValue(props.model, field.target),
          disabled: field.disabled,
          style: componentStyle, // 设置宽度
          'onUpdate:modelValue'(val) {
            setValue(props.model, field.target, val)
          },
          onChange: () => changeEvent(field),
          type: 'number',
          size: 'default',
          class: 'input-with-select'
        },
        field.appendSelect || field.appendBtnText || field.appendText
          ? {
              append: () => {
                if (field.appendSelect) {
                  return h(
                    ElSelect,
                    {
                      style: { width: '80px' },
                      disabled: field.disabled,
                      modelValue: props.model[field.target]?.unit,
                      size: 'default',
                      'onUpdate:modelValue'(val) {
                        const item = props.model[field.target]
                        if (!item) return
                        item.unit = val
                      },
                      onChange: () => changeEvent(field)
                    },
                    {
                      default: () =>
                        field.options.map(op =>
                          h(ElOption, {
                            key: op.value || op,
                            value: op.value || op,
                            label: op.label || op,
                            size: 'default'
                          })
                        )
                    }
                  )
                } else if (field.appendBtnText) {
                  return h(
                    ElButton,
                    {
                      onClick: () => field.clickEvent()
                    },
                    { default: () => [field.appendBtnText] }
                  )
                } else if (field.appendText) {
                  return h('span', {}, { default: () => [field.appendText] })
                }
              }
            }
          : null
      )
    }

    // 如果存在 prefix 或 suffix，则包装组件
    if (field.prefix || field.suffix) {
      return h(
        'div',
        {
          class: 'field-with-prefix-suffix',
          style: { display: 'flex', alignItems: 'center' }
        },
        [
          field.prefix
            ? h(
                'span',
                {
                  class: 'prefix',
                  style: { marginRight: '8px' } // 可根据需要调整样式
                },
                field.prefix
              )
            : null,
          component,
          field.suffix
            ? h(
                'span',
                {
                  class: 'suffix',
                  style: { marginLeft: '8px' } // 可根据需要调整样式
                },
                field.suffix
              )
            : null
        ]
      )
    }

    return component
  }

  // 定义一个渲染函数对象，用于渲染表单
  const renderForm = {
    render: () => {
      return h(
        ElForm,
        {
          rules: props.rules // 绑定表单验证规则
        },
        {
          default: () =>
            props.fields.map(field =>
              h(
                ElFormItem,
                {
                  key: field.name, // 表单项的 key
                  label: field.name // 表单项的标签
                },
                {
                  default: () => [generateCurrent(field)] // 渲染具体的表单项
                }
              )
            )
        }
      )
    }
  }
</script>

<style scoped lang="scss">
  :deep(.el-form-item__label) {
    min-width: 100px;
    justify-content: flex-start;
    font-weight: 400;
    height: var(--cu-select-height);
    line-height: var(--cu-select-height);
  }
  :deep(.el-form-item) {
    margin-bottom: 10px !important;
  }

  :deep(.el-input-group__append) {
    padding: 0;

    & > * {
      margin: 0;
    }
    > span {
      padding: 0 12px;
      display: inline-block;
      width: 80px;
    }
  }
</style>
