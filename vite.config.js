import { defineConfig, loadEnv } from 'vite';
import path from 'path';
import { wrapperEnv } from './build/utils';
import { createVitePlugins } from './build/vite/plugin';
import { OUTPUT_DIR } from './build/constant';
import { createProxy } from './build/vite/proxy';

export default defineConfig(({ mode, command }) => {
  const root = process.cwd();
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);
  const isBuild = command === 'build';
  const { VITE_PUBLIC_PATH, VITE_DROP_CONSOLE, VITE_PORT, VITE_PROXY } = viteEnv;


  const fs = require('fs');
  const buildTimeFilePath = path.join(__dirname, './build_time.txt'); // 根据实际文件位置调整路径
  let buildTime;
  try {
    buildTime = fs.readFileSync(buildTimeFilePath, 'utf8');
    console.log('读取打包时间文件成功：', buildTime);
  } catch (err) {
    console.error('读取打包时间文件出错：', err);
  }

  return {
    base: './', // 确保资源路径为相对路径
    plugins: [...createVitePlugins(viteEnv, isBuild)],
    resolve: {
      alias: {
        '~': path.resolve(__dirname, './'),
        '@': path.resolve(__dirname, './src'),
      },
      dedupe: ['vue'],
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json', '.vue']
    },
    server: {
      port: VITE_PORT || 8888,
      host: true,
      proxy: createProxy(VITE_PROXY),
      open: true, // 自动打开浏览器
    },
    css: {
      preprocessorOptions: {
        scss: {
          silenceDeprecations: ['legacy-js-api', 'color-functions'],
        },
      },
    },
    build: {
      target: 'es2015',
      // 可选：压缩设置
      // minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true, // 移除 console

        }
      }
    },
    define: {
      '__VUE_OPTIONS_API__': JSON.stringify(true),
      '__VUE_PROD_DEVTOOLS__': JSON.stringify(false),
      '__VUE_PROD_HYDRATION_MISMATCH_DETAILS__': JSON.stringify(false),
      __TRIAL_FLAG__: JSON.stringify(env.VITE_APP_TRAIL),
      '__BUILD_TIME__': JSON.stringify(buildTime)
    }
  };
});
