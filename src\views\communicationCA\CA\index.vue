<script setup>
  // 在这里编写逻辑
  import CuAreaMapCom from '@/components/CuAreaMapCom/index.vue'
  import { commCap } from '@/api/pgmx/commfac.js'

  const CuAreaMapComRef = ref(null)
  const list = ref([
    {
      label: '通信终端数量',
      field: 'communicationTerminalCount',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '吞吐量',
      field: 'throughput',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '峰值速率',
      field: 'peakRate',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '平均速率',
      field: 'averageRate',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '带宽效率',
      field: 'bandwidthEfficiency',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '通信时延',
      field: 'communicationDelay',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '抗干扰能力',
      field: 'antiInterferenceAbility',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '数据加密',
      field: 'dataEncryption',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '数据保护措施',
      field: 'dataProtectionMeasures',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '面积覆盖率',
      field: 'areaCoverageRate',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '组切换能力',
      field: 'groupSwitchingAbility',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '光缆线路长度',
      field: 'opticalCableLength',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '固定长途电话交换机容量',
      field: 'fixedLongDistanceSwitchCapacity',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '局用交换机容量',
      field: 'localExchangeCapacity',
      wiredCommCap: '',
      wirelessCommCap: ''
    },
    {
      label: '关键通信保障能力',
      field: 'keyCommunicationSupportAbility',
      wiredCommCap: '',
      wirelessCommCap: ''
    }
  ])

  const handleConfirm = async () => {
    try {
      // 调用子组件的校验方法
      await CuAreaMapComRef.value.validateForm()

      const { polygon, circle } = CuAreaMapComRef.value.formData
      const active = CuAreaMapComRef.value.activeName

      const queryData = {
        geoPointList: [
          { latitude: polygon.lat, longitude: polygon.lon },
          { latitude: polygon.latEnd, longitude: polygon.lonEnd }
        ],
        longitude: circle.cLon,
        latitude: circle.cLat,
        radius: circle.radius,
        type: active === 'polygon' ? 3 : active === 'circle' ? 1 : 2
      }

      const res = await commCap(queryData)
      list.value.forEach(item => {
        item.wiredCommCap = res.data[0].wiredCommCap[item.field]
        item.wirelessCommCap = res.data[0].wirelessCommCap[item.field]
      })
    } catch (error) {
      console.error('校验失败:', error.message)
      // 这里可以显示错误提示
    }
  }

  const handleExport = () => {}
</script>

<template>
  <div class="flex items-center gap-x-4 mb-4">
    <CuAreaMapCom ref="CuAreaMapComRef"></CuAreaMapCom>
    <cu-button content="分析" @click="handleConfirm"></cu-button>
  </div>
  <vxe-table border :data="list" :row-config="{ isCurrent: true, isHover: true }">
    <!-- 参数名列 -->
    <vxe-column field="label" title="通信能力项" width="200" align="center"></vxe-column>
    <vxe-column field="wiredCommCap" title="有线通信" align="center"></vxe-column>
    <vxe-column field="wirelessCommCap" title="无线通信" align="center"></vxe-column>
  </vxe-table>
  <div class="text-right mt-4">
    <cu-button content="导出" @click="handleExport"></cu-button>
  </div>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
