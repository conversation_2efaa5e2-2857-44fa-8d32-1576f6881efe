import request from '@/utils/request'


// 添加波形文件
export function addWavePlan(data) {
  return request({
    url: '/model/saveWave',
    method: 'post',
    data
  })
}

// 波形文件列表查询
export function allWavePlan(query) {
  return request({
    url: '/model/waveFileList',
    method: 'get',
    params: query
  })
}

// 波形文件读取
export function readWavePlan(query) {
  return request({
    url: '/model/readWave',
    method: 'get',
    params: query
  })
}



// 添加波形计划模板
export function addWavePlanTemp(data) {
  return request({
    url: '/model/addOrUpdate',
    method: 'post',
    data
  })
}

// 波形计划列表查询
export function allWavePlanTempList(data) {
  return request({
    url: '/model/selectPage',
    method: 'post',
    data
  })
}

// 波形计划详情查询
export function getWavePlanTempDetail(query) {
  return request({
    url: '/model/selectOne',
    method: 'get',
    params: query
  })
}

// 波形计划删除
export function deleteWaveTempPlan(data) {
  return request({
    url: '/model/batchDel',
    method: 'post',
    data
  })
}


// 波形计划导出
export function exportWaveTempPlan(data) {
  return request({
    url: '/model/export',
    method: 'get',
    params: data,
    responseType: 'blob'
  })
}






