
/**
 * 下载文件
 *
 * @param res 文件数据，可以是 Blob 对象、ArrayBuffer 对象、或者 DataURL 字符串
 * @param fileName 文件名
 * @param fileType 文件类型，默认值为 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
 */
export function downloadFile(res, fileName, fileType) {
  // 自动补全扩展名
  if (!fileName.endsWith('.xlsx')) fileName += '.xlsx';

  let blobData;
  // 如果是 DataURL(base64)，自动转换为 Blob
  if (typeof res === 'string' && res.startsWith('data:')) {
    const parts = res.split(',');
    const mime = parts[0].match(/:(.*?);/)[1];
    const raw = window.atob(parts[1]);
    const u8arr = new Uint8Array(raw.length);
    for (let i = 0; i < raw.length; i++) u8arr[i] = raw.charCodeAt(i);
    blobData = new Blob([u8arr], { type: mime });
  } else {
    // 默认按 Blob/ArrayBuffer 处理
    blobData = new Blob([res], {
      type:
        fileType ||
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
  }

  // 创建下载链接
  const url = URL.createObjectURL(blobData);
  const link = document.createElement('a');
  link.href = url;
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
}
