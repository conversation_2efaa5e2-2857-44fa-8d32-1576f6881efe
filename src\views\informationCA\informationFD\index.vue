<script setup>
  // 导入组件
  import mapDistributionCom from '../components/mapDistributionCom.vue'
  import { ElMessage } from 'element-plus'
  import { infoFacilities } from '@/api/pgmx/infofac.js'
  import { VxeUI } from 'vxe-table'

  // 表单数据
  const formData = ref({
    facilityType: null,
    lon: null,
    lonEnd: null,
    name: null,
    unit: null,
    lat: null,
    latEnd: null
  })
  const facilityTypeOptions = reactive([
    { label: '通信网络基础设施', value: 1 },
    { label: '算力基础设施', value: 2 },
    { label: '融合基础设施', value: 3 },
    { label: '新技术基础设施', value: 4 }
  ])

  const unitOptions = reactive([
    { label: '77通信团', value: 1 },
    { label: '12通信团', value: 2 },
    { label: '13通信团', value: 3 },
    { label: '14通信团', value: 4 }
  ])

  function validateCoordinate(value, type) {
    // 将 value 转换为数字类型
    const num = parseFloat(value)

    // 如果转换后的值不是有效数字，返回 false
    if (isNaN(num)) {
      return false
    }

    // 定义经度和纬度的正则表达式
    const regex =
      type === 'longitude'
        ? /^-?(180(\.0+)?|1[0-7]\d(\.\d{1,6})?|[1-9]?\d(\.\d{1,6})?)$/ // 经度范围 -180 到 180
        : /^-?(90(\.0+)?|[1-8]?\d(\.\d{1,6})?)$/ // 纬度范围 -90 到 90

    // 校验正则表达式
    if (!regex.test(value)) {
      return false // 如果不匹配，返回 false
    }

    // 对经度、纬度的具体数值范围进行进一步校验
    if (type === 'longitude' && (num < -180 || num > 180)) {
      return false // 经度超出范围
    } else if (type === 'latitude' && (num < -90 || num > 90)) {
      return false // 纬度超出范围
    }

    return true // 校验通过，返回 true
  }

  const query = async () => {
    if (!validateCoordinate(formData.value.lon, 'longitude')) {
      ElMessage.error('请输入正确的起始经度')
      return
    } else if (!validateCoordinate(formData.value.lat, 'latitude')) {
      ElMessage.error('请输入正确的起始纬度')
      return
    } else if (!validateCoordinate(formData.value.lonEnd, 'longitude')) {
      ElMessage.error('请输入正确的结束经度')
      return
    } else if (!validateCoordinate(formData.value.latEnd, 'latitude')) {
      ElMessage.error('请输入正确的结束纬度')
      return
    }
    if (formData.value.lon >= formData.value.lonEnd) {
      ElMessage.error('起始经度不能大于结束经度')
      return
    } else if (formData.value.lat >= formData.value.latEnd) {
      ElMessage.error('起始纬度不能大于结束纬度')
      return
    }
    let queryData = {
      area: {
        geoPointList: [
          {
            latitude: formData.value.lat,
            longitude: formData.value.lon
          },
          {
            latitude: formData.value.latEnd,
            longitude: formData.value.lonEnd
          }
        ],
        type: 2
      },
      facName: formData.value.name,
      facType: formData.value.facilityType,
      unit: formData.value.unit
    }

    VxeUI.loading.open({
      test: '加载中...'
    })
    await infoFacilities(queryData)
      .then(res => {
        console.log(res)
      })
      .finally(() => {
        VxeUI.loading.close()
      })
  }
</script>

<template>
  <div class="flex mb-5">
    <div class="flex flex-col gap-y-4">
      <div class="flex gap-x-4 items-center">
        <vxe-select v-model="formData.facilityType" placeholder="请选择设施类型">
          <vxe-option
            v-for="item in facilityTypeOptions"
            :value="item.value"
            :label="item.label"
            :key="item.value"
          >
          </vxe-option>
        </vxe-select>
        <vxe-input v-model="formData.lon" style="width: 150px" placeholder="起始经度" clearable />
        <span>至</span>
        <vxe-input
          v-model="formData.lonEnd"
          style="width: 150px"
          placeholder="结束经度"
          clearable
        />
        <vxe-input v-model="formData.name" style="width: 150px" placeholder="通信设备" clearable />
      </div>
      <div class="flex gap-x-4 items-center">
        <vxe-select v-model="formData.unit" placeholder="请选择单位">
          <vxe-option
            v-for="item in unitOptions"
            :value="item.value"
            :label="item.label"
            :key="item.value"
          ></vxe-option>
        </vxe-select>
        <vxe-input v-model="formData.lat" style="width: 150px" placeholder="起始纬度" clearable />
        <span>至</span>
        <vxe-input
          v-model="formData.latEnd"
          style="width: 150px"
          placeholder="结束纬度"
          clearable
        />
        <cu-button style="margin-left: 0" content="查询" @click="query"></cu-button>
      </div>
    </div>
  </div>
  <mapDistributionCom></mapDistributionCom>
</template>

<style scoped lang="scss">
  .region-tabs {
    width: 60%;
    :deep(.el-tabs__nav) {
      display: flex;
      width: 100%;
    }

    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      padding: 0 16px;
    }
  }
  .result-tabs {
    min-height: 550px;
    :deep(.el-tabs__nav) {
      display: flex;
      width: 100%;
    }

    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      padding: 0 16px;
    }
  }

  .polygon-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .point-form {
    margin-bottom: 12px;
  }

  .vxe-form {
    margin-top: 12px;
  }

  .vxe-button {
    margin-left: 8px;
  }
</style>
