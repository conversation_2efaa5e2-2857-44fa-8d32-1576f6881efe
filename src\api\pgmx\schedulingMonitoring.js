import request from '@/utils/request'

/** 查询装备层级树 */
export function readEquipTree(data) {
  return request({
    url: '/pgmx/schedulingMonitoring/readEquipTree',
    method: 'get',
    params: data
  })
}

/** 查询用频计划 */
export function readUseFreqEquipment(data) {
  return request({
    url: '/pgmx/schedulingMonitoring/readUseFreqEquipment',
    method: 'get',
    params: data
  })
}


/** 查询任务启动方案装备列表 */
export function readTaskPlanEquipList(data) {
  return request({
    url: '/pgmx/schedulingMonitoring/readTaskPlanEquipList',
    method: 'post',
    data: data
  })
}


/** 查询装备用频计划 */
export function readPlanUseFreqEquip(data) {
  return request({
    url: '/pgmx/schedulingMonitoring/readPlanUseFreqEquip',
    method: 'get',
    params: data
  })
}

/** 更新装备用频计划 */
export function updateUseFreqPlan(data) {
  return request({
    url: '/pgmx/schedulingMonitoring/updateUseFreqPlan',
    method: 'post',
    data: data
  })
}


/** 删除装备用频计划 */
export function batchDelFreqPlan(data) {
  return request({
    url: '/pgmx/schedulingMonitoring/batchDel',
    method: 'delete',
    data: data
  })
}





