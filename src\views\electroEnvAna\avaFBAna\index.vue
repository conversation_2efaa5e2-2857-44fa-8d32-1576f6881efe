<script setup>
  // 在这里编写逻辑
  import CuAreaMapCom from '@/components/CuAreaMapCom/index.vue'
  import SpecChart from '@/components/SpecChart/index.vue'
  import { queryAvailableFreqAnalyze } from '@/api/pgmx/emeMonitor.js'

  const CuAreaMapComRef = ref(null)
  const categories = ref(['设备A', '设备B', '设备C', '设备D', '设备E'])
  const xAxisMin = ref(30e6)
  const xAxisMax = ref(18000e6)
  const specChartRef = ref(null)
  const availableRanges = ref([])
  const disabledRanges = ref([])
  const conflictRanges = ref([])
  const reservedRanges = ref([])
  const protectedRanges = ref([])
  // 控制是否渲染图表
  const showChart = ref(false)
  const confirmTask = async () => {
    try {
      // 调用子组件的校验方法
      await CuAreaMapComRef.value.validateForm()
      const active = CuAreaMapComRef.value.activeName
      const { polygon, circle } = CuAreaMapComRef.value.formData
      const queryData = {
        area: {
          geoPointList: [
            { latitude: polygon.lat, longitude: polygon.lon },
            { latitude: polygon.latEnd, longitude: polygon.lonEnd }
          ],
          longitude: circle.cLon,
          latitude: circle.cLat,
          radius: circle.radius,
          type: active === 'polygon' ? 3 : active === 'circle' ? 1 : 2
        }
      }
      queryAvailableFreqAnalyze(queryData).then(res => {
        availableRanges.value = [
          [],
          [],
          [
            [30e6, 3e9],
            [12e9, 13e9]
          ],
          [],
          []
        ]
        disabledRanges.value = [[], [], [[3e9, 5e9]], [], []]
        conflictRanges.value = [[], [], [[5e9, 6e9]], [], []]
        reservedRanges.value = [[], [], [[6e9, 7e9]], [], []]
        protectedRanges.value = [
          [],
          [],
          [
            [7e9, 11e9],
            [12e9, 14e9]
          ],
          [],
          []
        ]
      })

      if (!showChart.value) {
        showChart.value = true
      } else {
        // 如果已经挂载了，也可以重新调用 render
        specChartRef.value?.renderChart()
      }
    } catch (error) {}

    // 首次点击时，才挂载组件
  }
</script>

<template>
  <div class="flex items-center w-2/3 gap-4">
    <CuAreaMapCom ref="CuAreaMapComRef"></CuAreaMapCom>
    <cu-button content="分析" @click="confirmTask"></cu-button>
  </div>
  <SpecChart
    v-if="showChart"
    ref="specChartRef"
    :categories="categories"
    :available-ranges="availableRanges"
    :disabled-ranges="disabledRanges"
    :conflict-ranges="conflictRanges"
    :reserved-ranges="reservedRanges"
    :protected-ranges="protectedRanges"
    :x-axis-min="xAxisMin"
    :x-axis-max="xAxisMax"
    :legend-data="['可用频率', '禁用频率', '冲突频率', '预留频率', '保护频率']"
    :show-legend="true"
    :show-YAxis-label="false"
  />
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
