<script setup>
  // 在这里编写逻辑
  import equipBasicInfoCom from './equipBasicInfoCom.vue'
  import equipConstructInfoCom from './equipConstructInfoCom.vue'
  import equipDeployCom from './equipDeployCom.vue'
  import { VxeUI } from 'vxe-table'
  import { readTree, readEquipmentInfo } from '@/api/pgmx/equipmentResource.js'

  const treeList = ref([])
  const equipBasicInfo = ref([
    { label: '装备名称:', field: 'equipName', value: '' },
    { label: '装备编码:', field: 'equipCode', value: '' },
    { label: '工作模式:', field: 'workMode', value: '' },
    { label: '复用方式:', field: 'reuseMode', value: '' },
    { label: '装备内码:', field: 'equipTypeId', value: '' },
    { label: '收发类型:', field: 'transmitReceiveType', value: '' },
    { label: '装备类型:', field: 'equipTypeId', value: '' },
    { label: '工作方式:', field: 'workMethod', value: '' },
    { label: '用频方式:', field: 'frequencyUtilization', value: '' },
    { label: '收发频率间隔:', field: 'freqInterval', value: '' },
    { label: '作用距离（km）:', field: 'rangeKm', value: '' }
  ])

  const equipConstructInfo = ref([
    { field: 'transmitter', label: '发射机:', value: [] },
    { field: 'receiver', label: '接收机:', value: [] },
    { field: 'antenna', label: '天线:', value: [] }
  ])

  const equipDeployInfo = ref([
    { field: 'longitude', label: '经度（°）', value: null },
    { field: 'latitude', label: '纬度（°）', value: null },
    { field: 'high', label: '高度（m）', value: null }
  ])

  const description = ref('')
  const photo = ref()

  // 树的懒加载方法
  const getNodeListApi = async ({ node }) => {
    // 如果节点已标记为不包含子节点，则不需要继续请求
    if (node.isEquipment === true) return
    // 构建请求参数
    const params = {
      id: node.id,
      lastLevelType: node.lastLevelType
    }
    try {
      const res = await readTree(params)
      if (res.code === 200) {
        // 更新节点的子节点数据
        return res.data
      }
    } catch (error) {
      console.error('加载子节点失败', error)
    }
  }

  // 节点点击回调
  const handleNodeClick = async ({ node, event }) => {
    console.log('点击了节点：', node)
    // 如果是叶子节点，加载用频计划和实际用频数据
    if (node.isEquipment) {
      VxeUI.loading.open({
        text: '加载中...'
      })
      try {
        // 这里应该调用获取用频数据的API
        const res = await readEquipmentInfo({
          id: node.id
        })
        if (res.code === 200) {
          equipBasicInfo.value.forEach(item => {
            item.value = res.data[item.field] || ''
          })
          // equipConstructInfo.value.forEach(item => {
          //   item.value = res.data[item.field] || ''
          // })
          equipDeployInfo.value.forEach(item => {
            item.value = res.data[item.field] || ''
          })
          description.value = res.data.description || ''
          photo.value = res.data.photo || 'https://vxeui.com/resource/img/fj577.jpg'
          console.log('加载用频数据成功', res.data)
          console.log('照片：', equipBasicInfo.value)
        }
      } catch (error) {
        console.error('加载用频数据失败', error)
      } finally {
        VxeUI.loading.close()
      }
    }
  }

  const initTreeList = async () => {
    await readTree({}).then(res => {
      if (res.code === 200) {
        treeList.value = res.data
      }
    })
  }

  onMounted(async () => {
    await initTreeList()
  })
</script>

<template>
  <el-row style="min-height: 670px" :gutter="20">
    <el-col :span="5">
      <vxe-tree
        lazy
        :is-current="true"
        :is-hover="true"
        :data="treeList"
        title-field="name"
        :load-method="getNodeListApi"
        @node-click="handleNodeClick"
      >
        <template #icon="{ isExpand }">
          <vxe-icon v-if="isExpand" status="success" name="square-minus" />
          <vxe-icon v-else status="success" name="square-plus" />
        </template>
      </vxe-tree>
    </el-col>
    <el-col :span="19">
      <div v-if="equipBasicInfo.length > 0">
        <equipBasicInfoCom :equipBasicInfo="equipBasicInfo"></equipBasicInfoCom>
        <equipConstructInfoCom v-model:equipConstructInfo="equipConstructInfo">
        </equipConstructInfoCom>

        <div class="my-2 grid grid-cols-2 gap-x-8">
          <div>
            <div class="my-2"> 装备描述信息 </div>
            <vxe-textarea
              style="height: 140px"
              v-model="description"
              placeholder="装备描述信息"
            ></vxe-textarea>
          </div>
          <div>
            <div class="my-2"> 装备图片 </div>
            <vxe-image style="width: 100%" :src="photo" :height="140"></vxe-image>
          </div>
        </div>

        <equipDeployCom :equipDeployInfo="equipDeployInfo"></equipDeployCom>
      </div>
    </el-col>
  </el-row>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
