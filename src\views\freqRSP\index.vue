<script setup>
  import useList from '@/api/tool/filemanage/tableFunctionPro'
  import freqPlanDialog from './components/freqPlanDialog.vue'
  import savePlanDialog from './components/savePlanDialog.vue'
  import { processDictionary } from '@/utils/index.js'
  import { queryTaskOption } from '@/api/pgmx/useFreqTask.js'
  import {
    dispatchPlan,
    dispatchPlanResult,
    deleteEquipFreqUse,
    updateEquipFreqUse,
    saveFreqPlan
  } from '@/api/pgmx/useFreq.js'
  import { ElMessage } from 'element-plus'
  import { VxeUI } from 'vxe-pc-ui'

  // 自定义参数生成器
  const customDeleteParams = (ids, rows) => {
    return rows
  }

  const filterOption = ref({
    immediate: false,
    dispatchId: null //调度规划结果ID
  })
  const xTable = ref(null)
  const { list, loading, curPage, size, total, timeSearch, deleteCurrentLine } = useList(
    dispatchPlanResult,
    deleteEquipFreqUse,
    filterOption.value,
    xTable,
    customDeleteParams
  )

  const taskName = ref()
  const useFreqStrategy = ref()
  const freqPlanDialogVisible = ref(false)
  const savePlanDialogVisible = ref(false)
  const taskOptions = ref([]) //zz任务枚举
  const useFreqStrategyOptions = ref([]) // 规划策略枚举
  const equipmentModelOptions = ref([]) //设备型号枚举
  const unitOptions = ref([]) //用频单位枚举
  const currentRowValue = ref({}) //当前行数据

  const getDictionaryData = async () => {
    processDictionary('pgmx_usefreqstrategy', useFreqStrategyOptions)
    processDictionary('pgmx_equipment', equipmentModelOptions)
    processDictionary('pgmx_unit', unitOptions)
    await queryTaskOption().then(res => {
      if (res.code === 200) {
        taskOptions.value = res.data
      }
    })
  }

  /**
   * 调度规划
   * 先请求调度规划接口，拿到规划Id，然后请求对应的调度规划列表结果
   */
  const plan = () => {
    // 这里编写调度规划的逻辑
    if (!taskName.value || !useFreqStrategy.value) {
      ElMessage.error('请选择zz任务和规划策略')
    } else {
      dispatchPlan({
        taskName: taskName.value,
        useFreqStrategy: useFreqStrategy.value
      })
        .then(res => {
          if (res.code === 200) {
            filterOption.value.dispatchId = res.data.dispatchId
            loading.value = true
            timeSearch(filterOption.value)
          }
        })
        .catch(err => {
          loading.value = false
          ElMessage.error(err)
        })
    }
  }

  /** 保存方案按钮事件 */
  const saveHandle = () => {
    // 这里编写保存方案的逻辑
    savePlanDialogVisible.value = true
  }

  /** 保存方案逻辑 */
  const savePlan = planName => {
    VxeUI.loading.open({
      text: '正在保存...'
    })
    saveFreqPlan({
      dispatchId: filterOption.value.dispatchId,
      planName: planName
    }).then(res => {
      if (res.code === 200) {
        VxeUI.loading.close()
        ElMessage.success('保存成功')
      }
    })
  }

  /** 单元格点击事件 */
  const handleCellClick = ({ row, column, $event }) => {
    if (column.field === 'freqPlan') {
      currentRowValue.value = row
      freqPlanDialogVisible.value = true
    }
  }

  /** 更新用频计划 */
  const updateEquipPlan = queryData => {
    updateEquipFreqUse(queryData).then(res => {
      if (res.code === 200) {
        freqPlanDialogVisible.value = false
        ElMessage.success('保存成功')
        timeSearch(filterOption.value)
      }
    })
  }

  /** 导出 */
  const handleExport = () => {
    // 这里编写导出的逻辑
    console.log('导出')
  }

  onMounted(async () => {
    await getDictionaryData()
  })
</script>

<template>
  <div>
    <div class="flex items-center mb-4">
      <div class="w-[80px] flex items-center">ZZ任务</div>
      <vxe-select style="width: 300px" v-model="taskName" clearable>
        <vxe-option
          v-for="item in taskOptions"
          :value="item.value"
          :label="item.label"
        ></vxe-option>
      </vxe-select>
    </div>
    <div class="flex items-center mb-4">
      <div class="w-[80px] flex items-center">规划策略</div>
      <vxe-select style="width: 300px" v-model="useFreqStrategy" clearable>
        <vxe-option
          v-for="item in useFreqStrategyOptions"
          :value="item.value"
          :label="item.label"
        ></vxe-option>
      </vxe-select>
      <cu-button type="primary" class="ml-5" content="调度规划" @click="plan"></cu-button>
      <cu-button
        type="primary"
        class="ml-5"
        content="保存方案"
        @click="saveHandle"
        :disabled="!filterOption.dispatchId"
      ></cu-button>
    </div>

    <vxe-table
      ref="xTable"
      border
      stripe
      size="medium"
      height="675"
      :data="list"
      :loading="loading"
      :row-config="{ isCurrent: true, isHover: true }"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
      @cell-click="handleCellClick"
    >
      <vxe-column type="seq" width="60" fixed="left" align="center" />
      <vxe-column
        field="frequencyUsingUnit"
        title="用频单位"
        fixed="left"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
        <template #default="{ row }">
          {{ selectDictLabel(unitOptions, row.frequencyUsingUnit) }}
        </template>
      </vxe-column>
      <vxe-column
        field="createBy"
        title="装备型号"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
        <template #default="{ row }">
          {{ selectDictLabel(equipmentModelOptions, row.equipmentModel) }}
        </template>
      </vxe-column>
      <vxe-column field="freqUsageEquipment" title="用频装备" align="center" />
      <vxe-column field="freqPlan" title="用频计划" align="center" :edit-render="{}">
        <template #default="{ row }">
          <div class="cursor-pointer" :class="{ 'text-[#00603b] underline': !row.freqPlan }">
            {{ row.equipmentList.length > 0 ? '修改计划' : '设置计划' }}
          </div>
        </template>
      </vxe-column>
      <vxe-column field="操作" title="操作" width="120" fixed="right" align="center">
        <template #default="{ row }">
          <cu-button type="info" content="导出" @click="handleExport(row)" />
          <cu-button type="info" content="删除" @click="deleteCurrentLine(row)" />
        </template>
      </vxe-column>
    </vxe-table>
    <!-- 分页 -->
    <p>
      <vxe-pager
        v-model:current-page="curPage"
        v-model:page-size="size"
        class="vxe-page"
        perfect
        :total="total"
        :page-sizes="[10, 20, 50, 100, 200, 500]"
        :layouts="[
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'Sizes',
          'FullJump',
          'Total'
        ]"
      />
    </p>
  </div>

  <freq-plan-dialog
    v-if="freqPlanDialogVisible"
    v-model="freqPlanDialogVisible"
    :planObject="currentRowValue"
    @confirmPlan="updateEquipPlan"
  ></freq-plan-dialog>

  <save-plan-dialog
    v-if="savePlanDialogVisible"
    v-model="savePlanDialogVisible"
    @confirm="savePlan"
  ></save-plan-dialog>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
