<script setup>
  const props = defineProps({
    showTitle: {
      type: Boolean,
      default: true
    }
  })

  const activeName = ref('polygon') // 默认选中多边形

  const formRefs = {
    circle: ref(null),
    polygon: ref(null)
  }

  // 表单数据
  const formData = reactive({
    circle: {
      cLon: '',
      cLat: '',
      radius: ''
    },
    polygon: {
      lon: '',
      lonEnd: '',
      lat: '',
      latEnd: ''
    }
  })

  const handleTabChange = () => {
    if (activeName.value === 'circle') {
      formData.polygon.lon = ''
      formData.polygon.lat = ''
      formData.polygon.lonEnd = ''
      formData.polygon.latEnd = ''
      formRefs.polygon.value?.clearValidate()
    } else {
      formData.circle.cLon = ''
      formData.circle.cLat = ''
      formData.circle.radius = ''
      formRefs.circle.value?.clearValidate()
    }
  }

  defineExpose({
    activeName,
    formData
  })
</script>

<template>
  <div>
    <cu-title v-if="showTitle" title="监测区域"> </cu-title>
    <el-tabs
      v-model="activeName"
      type="border-card"
      class="region-tabs"
      @tab-change="handleTabChange"
    >
      <!-- 多边形区域 -->
      <el-tab-pane label="矩形区域" name="polygon">
        <div class="polygon-form">
          <vxe-form
            class="point-form"
            ref="polygonFormRef"
            :data="formData.polygon"
            :label-config="{ width: '80px', colon: true, align: 'left' }"
          >
            <!-- 经度 -->
            <vxe-form-item field="lon" title="经度">
              <div class="flex items-center space-x-2">
                <vxe-input
                  style="width: 140px"
                  v-model="formData.polygon.lon"
                  type="float"
                  placeholder="例如：116.404"
                  clearable
                  min="-180"
                  max="180"
                  step="0.01"
                />
                <span>至</span>
                <vxe-input
                  style="width: 140px"
                  v-model="formData.polygon.lonEnd"
                  type="float"
                  placeholder="例如：116.405"
                  clearable
                  min="-180"
                  max="180"
                  step="0.01"
                />
              </div>
            </vxe-form-item>

            <!-- 纬度 -->
            <vxe-form-item field="lat" title="纬度">
              <div class="flex items-center space-x-2">
                <vxe-input
                  style="width: 140px"
                  v-model="formData.polygon.lat"
                  type="float"
                  placeholder="例如：39.915"
                  clearable
                  min="-90"
                  max="90"
                  step="0.01"
                />
                <span>至</span>
                <vxe-input
                  style="width: 140px"
                  v-model="formData.polygon.latEnd"
                  type="float"
                  placeholder="例如：39.916"
                  clearable
                  min="-90"
                  max="90"
                  step="0.01"
                />
              </div>
            </vxe-form-item>
          </vxe-form>
        </div>
      </el-tab-pane>

      <!-- 圆形区域 -->
      <el-tab-pane label="圆形区域" name="circle">
        <vxe-form
          ref="circleFormRef"
          :data="formData.circle"
          title-align="right"
          title-width="180"
          title-colon
        >
          <vxe-form-item field="cLon" span="24" title="圆心经度（西半球负）">
            <vxe-input
              v-model="formData.circle.cLon"
              type="float"
              placeholder="例如：116.404"
              clearable
              min="-180"
              max="180"
              step="0.01"
            />
          </vxe-form-item>
          <vxe-form-item field="cLat" span="24" title="圆心纬度（南半球负）">
            <vxe-input
              v-model="formData.circle.cLat"
              type="float"
              placeholder="例如：39.915"
              clearable
              min="-90"
              max="90"
              step="0.01"
            />
          </vxe-form-item>
          <vxe-form-item field="radius" span="24" title="半径（千米）">
            <vxe-input
              v-model="formData.circle.radius"
              type="float"
              placeholder="请输入大于0的数值"
              clearable
              min="0"
            />
          </vxe-form-item>
        </vxe-form>
      </el-tab-pane>

      <!-- 图选区域 -->
      <el-tab-pane label="图选区域" name="map"> <div class="w-full">图选区域</div> </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
  .region-tabs {
    margin-bottom: 12px;
    :deep(.el-tabs__nav) {
      display: flex;
      width: 100%;
    }

    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      padding: 0 8px;
    }
  }

  .polygon-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .point-form {
    margin-bottom: 12px;
  }

  .vxe-button {
    margin-left: 8px;
  }
</style>
