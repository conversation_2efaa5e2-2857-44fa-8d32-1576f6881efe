<script setup>
  const props = defineProps({
    row: {
      type: Object,
      default: () => ({})
    }
  })

  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const loading = ref(false)
  const deviceTestInfo = ref([
    { label: '设备型号', value: 'MCH001' },
    { label: '设备序列号', value: 'MCH001201141001' },
    { label: '软件版本', value: 'SW Version：V1.0.2.3' },
    { label: '数字处理模块', value: '' },
    { label: '微博模块', value: '' },
    { label: '射频模块', value: '' },
    { label: '毫米波模块', value: '' }
  ])

  const handleConfirm = () => {
    dialogTableVisible.value = false
  }
  const handleCancel = () => {}
</script>

<template>
  <cu-dialog
    v-model="dialogTableVisible"
    title="设备监测"
    width="600px"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  >
    <vxe-table v-loading="loading" border :show-header="false" :data="deviceTestInfo">
      <!-- 参数名列 -->
      <vxe-column field="label" title="参数名" width="160" align="center"></vxe-column>

      <!-- 参数值列，可编辑 -->
      <vxe-column field="value" title="参数值" align="center">
        <!-- 默认展示：根据类型，做合适的展示 -->
        <template #default="{ row }">
          {{ row.value }}
        </template>
      </vxe-column>
    </vxe-table>
  </cu-dialog>
</template>
