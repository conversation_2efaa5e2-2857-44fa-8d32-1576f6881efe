<script setup>
  const props = defineProps({
    equipBasicInfo: {
      type: Array,
      default: () => []
    }
  })

  // 使用计算属性处理数据
  const processedData = computed(() => {
    // 优先使用props传入的数据
    const sourceData = props.equipBasicInfo.length > 0 ? props.equipBasicInfo : []
    // 每两个数据项为一组
    return groupData(sourceData, 2).map(group => {
      const item1 = group[0] || {}
      const item2 = group[1] || {}
      return {
        label1: item1.label,
        value1: item1.value,
        label2: item2.label,
        value2: item2.value
      }
    })
  })

  // 定义分组数据函数
  const groupData = (arr, size) => {
    const result = []
    for (let i = 0; i < arr.length; i += size) {
      result.push(arr.slice(i, i + size))
    }
    return result
  }
</script>

<template>
  <div class="w-full">
    <div> 装备基本信息 </div>
    <vxe-table border :show-header="false" :data="processedData">
      <!-- 第一组参数名列 -->
      <vxe-column field="label1" title="参数名" width="140" align="center"></vxe-column>

      <!-- 第一组参数值列 -->
      <vxe-column field="value1" title="参数值" align="center"></vxe-column>

      <!-- 第二组参数名列 -->
      <vxe-column field="label2" title="参数名" width="140" align="center"></vxe-column>

      <!-- 第二组参数值列 -->
      <vxe-column field="value2" title="参数值" align="center"></vxe-column>
    </vxe-table>
  </div>
</template>

<style lang="scss" scoped></style>
