<script setup>
  import useList from '@/api/tool/filemanage/tableFunctionPro'
  import { processDictionary } from '@/utils/index.js'
  import PADialog from './components/PADialog.vue'
  import { queryAllList, addUFTask, editUFTask, deleteTask } from '@/api/pgmx/useFreqTask.js'
  import { ElMessage } from 'element-plus'

  const xTable = ref(null)
  const PADialogRef = ref(null)
  const PAType = ref('add')
  const taskId = ref(null)
  const filterOption = ref({
    freqBandStart: '', //起始频率
    freqBandEnd: '', //结束频率
    purpose: '', //用途
    taskName: '', //ZZ任务
    useStartTime: '', //开始时间
    useEndTime: '', //结束时间
    frequencyUsingUnit: '', //用频单位
    equipmentModel: '' //用频型号
  })

  const {
    list,
    loading,
    curPage,
    size,
    total,
    timeSearch,
    deleteCurrentLine,
    deleteAll,
    selectChangeEvent
  } = useList(queryAllList, deleteTask, filterOption.value, xTable)

  const dialogTableVisible = ref(false)
  const equipmentModelOptions = ref([]) //设备型号枚举
  const unitOptions = ref([]) //用频单位枚举
  const purposeOptions = ref([]) //用途枚举
  const equipmentPlatformOptions = ref([]) //配装平台枚举

  const getDictionaryData = async () => {
    processDictionary('pgmx_equipment', equipmentModelOptions)
    processDictionary('pgmx_unit', unitOptions)
    processDictionary('pgmx_purpose', purposeOptions)
    processDictionary('pgmx_equipform', equipmentPlatformOptions)
  }

  /** * 导出 */
  const exportRecord = async () => {
    console.log('导出')
  }

  /** 导入 */
  const importRecord = () => {
    console.log('导入')
  }

  /** 编辑按钮 */
  const handleEdit = row => {
    taskId.value = row.id
    PAType.value = 'edit'
    dialogTableVisible.value = true
  }

  /** 新增按钮 */
  const handleAdd = () => {
    taskId.value = null
    PAType.value = 'add'
    dialogTableVisible.value = true
  }

  /** 任务确认 */
  const taskConfirm = queryData => {
    switch (PAType.value) {
      case 'add':
        addTask(queryData)
        break
      case 'edit':
        editTask(queryData)
        break
    }
  }

  /** 新增用频计划 */
  const addTask = queryData => {
    addUFTask(queryData).then(res => {
      if (res.code === 200) {
        ElMessage.success('新增成功')
        PADialogRef.value?.resetForm()
        dialogTableVisible.value = false
        timeSearch(filterOption.value)
      }
    })
  }

  /** 修改用频计划 */
  const editTask = queryData => {
    queryData.id = taskId.value
    editUFTask(queryData).then(res => {
      if (res.code === 200) {
        ElMessage.success('修改成功')
        PADialogRef.value?.resetForm()
        taskId.value = null
        dialogTableVisible.value = false
        timeSearch(filterOption.value)
      }
    })
  }

  onMounted(async () => {
    await getDictionaryData()
  })
</script>
<template>
  <vxe-toolbar class="vxe-toolbar">
    <template #buttons>
      <div class="flex flex-col gap-4 w-full">
        <!-- 使用网格布局确保上下对齐 -->
        <div class="grid grid-cols-2 gap-x-8 gap-y-4 w-full">
          <!-- 频段范围 -->
          <div class="flex items-center gap-2">
            <div class="text-sm font-medium min-w-[80px]">频段范围</div>
            <vxe-input
              v-model="filterOption.freqBandStart"
              placeholder="起始"
              clearable
              class="flex-1"
              type="number"
              min="0"
            >
              <template #suffix> MHz </template>
            </vxe-input>
            <span class="mx-1">至</span>
            <vxe-input
              v-model="filterOption.freqBandEnd"
              placeholder="结束"
              clearable
              class="flex-1"
              type="number"
              min="0"
            >
              <template #suffix> MHz </template>
            </vxe-input>
          </div>

          <!-- 用途 -->
          <div class="flex items-center gap-2">
            <div class="text-sm font-medium min-w-[80px]">用途</div>
            <vxe-select
              v-model="filterOption.purpose"
              placeholder="请选择用途"
              class="flex-1"
              clearable
            >
              <vxe-option
                v-for="item in purposeOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              >
              </vxe-option>
            </vxe-select>
          </div>

          <!-- ZZ任务 -->
          <div class="flex items-center gap-2">
            <div class="text-sm font-medium min-w-[80px]">ZZ任务</div>
            <vxe-input
              v-model="filterOption.taskName"
              placeholder="任务名称"
              clearable
              class="flex-1"
            />
          </div>

          <!-- 用频单位 -->
          <div class="flex items-center gap-2">
            <div class="text-sm font-medium min-w-[80px]">用频单位</div>
            <vxe-select
              class="flex-1"
              v-model="filterOption.frequencyUsingUnit"
              placeholder="请选择用频单位"
              clearable
            >
              <vxe-option
                v-for="item in unitOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              >
              </vxe-option>
            </vxe-select>
          </div>

          <!-- 保护时间 -->
          <div class="flex items-center gap-2">
            <div class="text-sm font-medium min-w-[80px]">保护时间</div>
            <vxe-input
              v-model="filterOption.useStartTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="开始时间"
              clearable
              class="flex-1"
            />
            <span class="mx-1 whitespace-nowrap">至</span>
            <vxe-input
              v-model="filterOption.useEndTime"
              type="datetime"
              format="yyyy-MM-dd HH:mm:ss"
              placeholder="结束时间"
              clearable
              class="flex-1"
            />
          </div>

          <!-- 装备型号 -->
          <div class="flex items-center gap-2">
            <div class="text-sm font-medium min-w-[80px]">装备型号</div>
            <vxe-select
              class="flex-1"
              v-model="filterOption.equipmentModel"
              placeholder="请选择装备型号"
              clearable
            >
              <vxe-option
                v-for="item in equipmentModelOptions"
                :label="item.label"
                :value="item.value"
                :key="item.value"
              >
              </vxe-option>
            </vxe-select>
          </div>
        </div>

        <!-- 按钮行 -->
        <div class="flex flex-wrap justify-between items-center gap-2 w-full">
          <div class="flex flex-wrap gap-2">
            <cu-button content="查询" @click="timeSearch(filterOption)" />
            <cu-button content="新增" @click="handleAdd" />
          </div>
          <div class="flex flex-wrap gap-2">
            <cu-button content="导出" @click="exportRecord" />
            <cu-button content="导入" @click="importRecord" />
          </div>
        </div>
      </div>
    </template>
  </vxe-toolbar>
  <vxe-table
    ref="xTable"
    border
    stripe
    size="medium"
    height="580"
    :data="list"
    :loading="loading"
    :column-config="{ resizable: true }"
    :row-config="{ isCurrent: true, isHover: true }"
  >
    <vxe-column type="seq" width="50" align="center" />
    <vxe-column
      field="taskName"
      title="ZZ任务"
      min-width="120"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column
      field="frequencyUsingUnit"
      title="用频单位"
      min-width="120"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    >
      <template #default="{ row }">
        {{ selectDictLabel(unitOptions, row.frequencyUsingUnit) }}
      </template>
    </vxe-column>
    <vxe-column field="equipmentModel" title="装备型号" width="120" align="center">
      <template #default="{ row }">
        {{ selectDictLabel(equipmentModelOptions, row.equipmentModel) }}
      </template>
    </vxe-column>
    <vxe-column field="equippedPlatform" title="配装平台" width="180" align="center">
      <template #default="{ row }">
        {{ selectDictLabel(equipmentPlatformOptions, row.equippedPlatform) }}
      </template>
    </vxe-column>
    <vxe-column field="equipmentQuantity" title="装备数量" width="100" align="center" />
    <vxe-column field="purpose" title="用途" width="100" align="center">
      <template #default="{ row }">
        {{ selectDictLabel(purposeOptions, row.purpose) }}
      </template>
    </vxe-column>
    <vxe-column
      field="freqBandStr"
      title="频段范围"
      width="100"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column field="frequencyPointQuantity" title="频点数量" width="100" align="center" />
    <vxe-column field="operatingFrequency" title="工作频率" width="100" align="center" />
    <vxe-column field="emissionBandwidth" title="发射带宽" width="100" align="center" />
    <vxe-column field="transmissionPower" title="发射功率" width="100" align="center" />
    <vxe-column
      field="areaStr"
      title="使用区域"
      width="100"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column
      field="timeStr"
      title="使用时间"
      width="100"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column
      field="remarks"
      title="备注"
      width="100"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column
      field="createTime"
      title="创建时间"
      width="100"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column field="操作" title="操作" width="120" fixed="right" align="center">
      <template #default="{ row }">
        <cu-button type="info" content="编辑" @click="handleEdit(row)" />
        <cu-button type="info" content="删除" @click="deleteCurrentLine(row)" />
      </template>
    </vxe-column>
  </vxe-table>
  <!-- 分页 -->
  <p>
    <vxe-pager
      v-model:current-page="curPage"
      v-model:page-size="size"
      class="vxe-page"
      perfect
      :total="total"
      :page-sizes="[10, 20, 50, 100, 200, 500]"
      :layouts="[
        'PrevJump',
        'PrevPage',
        'Number',
        'NextPage',
        'NextJump',
        'Sizes',
        'FullJump',
        'Total'
      ]"
    />
  </p>

  <PADialog
    ref="PADialogRef"
    v-if="dialogTableVisible"
    v-model="dialogTableVisible"
    @PATaskConfirm="taskConfirm"
    :type="PAType"
    :task-id="taskId"
  ></PADialog>
</template>

<style lang="scss" scoped>
  :deep(.vxe-buttons--wrapper) {
    gap: 10px;
  }
</style>
