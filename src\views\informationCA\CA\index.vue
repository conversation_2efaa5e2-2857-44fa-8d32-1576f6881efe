<script setup>
  import Highcharts from 'highcharts'
  import CuAreaMapCom from '@/components/CuAreaMapCom/index.vue'
  import { infoAnalyzed } from '@/api/pgmx/infofac.js'

  const facilityContainerRef = ref(null)
  const supportContainerRef = ref(null)
  const CuAreaMapComRef = ref(null)

  const initChart = () => {
    const facilityChart = Highcharts.chart('facilityContainer', {
      chart: {
        type: 'column',
        margin: [50, 50, 100, 80],
        options3d: {
          enabled: true,
          alpha: 0,
          beta: 0,
          depth: 20,
          viewDistance: 25
        }
      },
      title: {
        text: '信息设施统计'
      },
      xAxis: {
        categories: ['通信网络基础设施', '算力基础设施', '融合基础设施', '新技术基础设施'],
        title: {
          text: '信息设施类型'
        },
        labels: {
          rotation: 0,
          align: 'center',
          style: {
            fontSize: '13px',
            fontFamily: '<PERSON><PERSON><PERSON>, sans-serif'
          }
        }
      },
      yAxis: {
        title: {
          text: '信息设施数量'
        },
        tickInterval: 100, // 间隔
        min: 0
      },
      tooltip: {
        headerFormat: '<b>{point.key}</b><br>',
        pointFormat: '信息设施数量: {point.y}'
      },
      plotOptions: {
        column: {
          depth: 25,
          dataLabels: {
            enabled: true
          }
        }
      },
      credits: {
        //版权
        enabled: false
      },
      legend: {
        enabled: false
      },
      accessibility: {
        enabled: false
      },
      series: [
        {
          name: '信息设施数量',
          data: [863, 807, 138, 295]
        }
      ]
    })
    const supportChart = Highcharts.chart('supportContainer', {
      chart: {
        type: 'column',
        margin: [50, 50, 100, 80],
        options3d: {
          enabled: true,
          alpha: 0,
          beta: 0,
          depth: 20,
          viewDistance: 25
        }
      },
      title: {
        text: '信息设施支撑度'
      },
      xAxis: {
        categories: ['通信支撑度', 'QB支撑度', '算力支撑度', '舆情支撑度'],
        title: {
          text: '支撑能力'
        },
        labels: {
          rotation: 0,
          align: 'center',
          style: {
            fontSize: '13px',
            fontFamily: 'Verdana, sans-serif'
          }
        }
      },
      yAxis: {
        title: {
          text: '支撑度(%)'
        },
        tickInterval: 100, // 间隔
        min: 0
      },
      tooltip: {
        headerFormat: '<b>{point.key}</b><br>',
        pointFormat: '支撑度: {point.y}'
      },
      plotOptions: {
        column: {
          depth: 25,
          dataLabels: {
            enabled: true
          }
        }
      },
      credits: {
        //版权
        enabled: false
      },
      legend: {
        enabled: false
      },
      accessibility: {
        enabled: false
      },
      series: [
        {
          name: '支撑度',
          data: [110, 120, 133, 150]
        }
      ]
    })
    // 窗口大小变化时重新调整图表大小
    window.addEventListener('resize', () => facilityChart.reflow(), supportChart.reflow())
  }

  const handleConfirm = async () => {
    try {
      // 调用子组件的校验方法
      await CuAreaMapComRef.value.validateForm()
      const active = CuAreaMapComRef.value.activeName

      // 如果是地图模式（无需表单校验），或者表单都通过了，继续下面逻辑
      initChart() // 重新初始化图表

      const { polygon, circle } = CuAreaMapComRef.value.formData
      const queryData = {
        geoPointList: [
          { latitude: polygon.lat, longitude: polygon.lon },
          { latitude: polygon.latEnd, longitude: polygon.lonEnd }
        ],
        longitude: circle.cLon,
        latitude: circle.cLat,
        radius: circle.radius,
        type: active === 'polygon' ? 3 : active === 'circle' ? 1 : 2
      }

      infoAnalyzed(queryData).then(res => {
        console.log(res)
      })
    } catch (error) {
      console.error('校验失败:', error.message)
      // 可以在这里处理校验失败的情况，例如显示错误信息
    }
  }
</script>

<template>
  <div class="flex items-center gap-x-4">
    <CuAreaMapCom ref="CuAreaMapComRef"></CuAreaMapCom>
    <cu-button content="确定" @click="handleConfirm"></cu-button>
  </div>

  <div id="facilityContainer" ref="facilityContainerRef"></div>
  <div id="supportContainer" ref="supportContainerRef"></div>
</template>

<style scoped lang="scss">
  .region-tabs {
    :deep(.el-tabs__nav) {
      display: flex;
      width: 100%;
    }

    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      padding: 0 16px;
    }
  }

  .polygon-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .point-form {
    margin-bottom: 12px;
  }

  .point-list {
    position: relative;

    .point-tip {
      position: absolute;
      bottom: 8px;
      left: 0;
      width: 100%;
      text-align: center;
      color: var(--el-color-warning);
      font-size: 12px;
      padding: 4px 0;
      background-color: rgba(255, 255, 255, 0.8);
    }
  }

  .vxe-button {
    margin-left: 8px;
  }
</style>
