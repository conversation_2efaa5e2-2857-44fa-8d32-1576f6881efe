<script setup>
  import 'ol/ol.css'
  import Map from 'ol/Map'
  import View from 'ol/View'
  import TileLayer from 'ol/layer/Tile'
  import OSM from 'ol/source/OSM'
  import VectorLayer from 'ol/layer/Vector'
  import VectorSource from 'ol/source/Vector'
  import Draw from 'ol/interaction/Draw'
  import { Style, Stroke, Fill, Circle as CircleStyle } from 'ol/style'
  import Feature from 'ol/Feature'
  import { Polygon, Circle, Point } from 'ol/geom'
  import { fromCircle } from 'ol/geom/Polygon'

  // 支持 v-model:value 绑定
  const props = defineProps({
    modelValue: Array
  })
  const emits = defineEmits(['change'])

  const mapRef = ref()
  let map = null
  let draw = null
  let vectorSource = null
  let vectorLayer = null

  const isDrawing = ref(false)
  const coordinates = ref([])
  const selectedTool = ref('polygon') // 当前选择的工具
  const drawType = ref('Polygon') // OpenLayers 绘制类型

  // 工具选项
  const tools = [
    { key: 'polygon', label: '自定义多边形', type: 'Polygon', icon: '🔷' },
    { key: 'rectangle', label: '矩形', type: 'Circle', icon: '⬜' },
    { key: 'circle', label: '圆形', type: 'Circle', icon: '🔵' }
  ]

  // 样式配置
  const getDrawStyle = toolType => {
    const colors = {
      polygon: { stroke: '#ff6f00', fill: 'rgba(255, 111, 0, 0.2)' },
      rectangle: { stroke: '#2196f3', fill: 'rgba(33, 150, 243, 0.2)' },
      circle: { stroke: '#4caf50', fill: 'rgba(76, 175, 80, 0.2)' }
    }
    const color = colors[toolType] || colors.polygon
    return new Style({
      stroke: new Stroke({ color: color.stroke, width: 2 }),
      fill: new Fill({ color: color.fill }),
      image: new CircleStyle({
        radius: 5,
        fill: new Fill({ color: color.stroke }),
        stroke: new Stroke({ color: '#fff', width: 2 })
      })
    })
  }

  // 创建矩形
  const createRectangle = (center, radius) => {
    const halfWidth = radius * 0.8
    const halfHeight = radius * 0.6
    return [
      [center[0] - halfWidth, center[1] - halfHeight],
      [center[0] + halfWidth, center[1] - halfHeight],
      [center[0] + halfWidth, center[1] + halfHeight],
      [center[0] - halfWidth, center[1] + halfHeight],
      [center[0] - halfWidth, center[1] - halfHeight]
    ]
  }

  // 选择工具
  const selectTool = tool => {
    selectedTool.value = tool.key
    drawType.value = tool.type
    if (isDrawing.value) {
      stopDraw()
    }
  }

  // 开始绘制
  const startDraw = () => {
    if (draw) {
      map.removeInteraction(draw)
      draw = null
    }

    isDrawing.value = true

    // 根据选择的工具创建不同的绘制交互
    if (selectedTool.value === 'polygon') {
      // 自定义多边形 - 点击绘制
      draw = new Draw({
        source: new VectorSource(), // 使用临时source，避免直接添加到主source
        type: 'Polygon',
        style: getDrawStyle(selectedTool.value)
      })
    } else {
      // 其他形状 - 拖拽绘制
      draw = new Draw({
        source: new VectorSource(), // 使用临时source
        type: 'Circle',
        style: getDrawStyle(selectedTool.value)
      })
    }

    map.addInteraction(draw)

    draw.on('drawend', e => {
      isDrawing.value = false
      map.removeInteraction(draw)

      let coords = []
      let geometry = e.feature.getGeometry()
      let finalFeature = null

      if (selectedTool.value === 'polygon') {
        // 自定义多边形 - 直接使用绘制的feature
        coords = geometry.getCoordinates()[0].map(([lng, lat]) => ({
          longitude: lng,
          latitude: lat
        }))
        finalFeature = new Feature({
          geometry: geometry
        })
      } else if (selectedTool.value === 'circle') {
        // 圆形 - 转换为多边形
        const polygon = fromCircle(geometry, 32) // 32个点近似圆形
        coords = polygon.getCoordinates()[0].map(([lng, lat]) => ({
          longitude: lng,
          latitude: lat
        }))
        finalFeature = new Feature({
          geometry: polygon
        })
      } else if (selectedTool.value === 'rectangle') {
        // 矩形
        const center = geometry.getCenter()
        const radius = geometry.getRadius()
        const rectangleCoords = createRectangle(center, radius)
        coords = rectangleCoords.map(([lng, lat]) => ({
          longitude: lng,
          latitude: lat
        }))
        finalFeature = new Feature({
          geometry: new Polygon([rectangleCoords])
        })
      }

      // 设置样式并添加到主图层
      if (finalFeature) {
        finalFeature.setStyle(getDrawStyle(selectedTool.value))
        vectorSource.addFeature(finalFeature)
      }

      coordinates.value = coords
      emits('update:modelValue', coordinates.value)
      emits('change', { coordinates: coordinates.value, tool: selectedTool.value })
    })
  }

  // 停止绘制
  const stopDraw = () => {
    if (draw) {
      map.removeInteraction(draw)
      draw = null
    }
    isDrawing.value = false
  }

  // 清空绘制
  const clearDraw = () => {
    // 清空所有绘制的图形
    if (vectorSource) vectorSource.clear()
    coordinates.value = []
    emits('change', { coordinates: [], tool: selectedTool.value })
    stopDraw()
  }

  // 监听外部数据变化
  // watch(
  //   () => props.modelValue,
  //   val => {
  //     if (!val || !Array.isArray(val) || val.length === 0) {
  //       if (vectorSource) vectorSource.clear()
  //       return
  //     }

  //     // 绘制已有多边形
  //     clearDraw()
  //     const coords = val.map(p => [p.longitude, p.latitude])
  //     if (coords.length) {
  //       const feature = new Feature({
  //         geometry: new Polygon([coords])
  //       })
  //       feature.setStyle(getDrawStyle(selectedTool.value))
  //       vectorSource.addFeature(feature)
  //     }
  //   }
  // )

  onMounted(async () => {
    await nextTick()
    vectorSource = new VectorSource()
    vectorLayer = new VectorLayer({
      source: vectorSource
    })

    map = new Map({
      target: mapRef.value,
      layers: [new TileLayer({ source: new OSM() }), vectorLayer],
      view: new View({
        center: [116.397128, 39.90923],
        zoom: 6,
        projection: 'EPSG:4326'
      })
    })
  })

  onBeforeUnmount(() => {
    if (map) map.setTarget(null)
  })
</script>

<template>
  <div class="ol-map-container">
    <!-- 工具选择区域 -->
    <div class="tool-selector mb-3">
      <div class="flex flex-wrap gap-2 mb-2">
        <button
          v-for="tool in tools"
          :key="tool.key"
          @click="selectTool(tool)"
          :class="['tool-btn', selectedTool === tool.key ? 'active' : '']"
        >
          <span class="tool-icon">{{ tool.icon }}</span>
          <span class="tool-label">{{ tool.label }}</span>
        </button>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="map-tools mb-2 flex gap-3 items-center">
      <cu-button
        :content="`开始绘制${tools.find(t => t.key === selectedTool)?.label || ''}`"
        @click="startDraw"
        :disabled="isDrawing"
        :type="isDrawing ? 'default' : 'primary'"
      />
      <cu-button
        v-if="isDrawing && selectedTool === 'polygon'"
        content="完成绘制"
        @click="stopDraw"
        type="success"
      />
      <cu-button type="info" content="清空" @click="clearDraw" />

      <div class="status-info flex gap-4 ml-4">
        <span v-if="coordinates.length" class="text-xs text-green-600">
          已选择 {{ coordinates.length }} 个点
        </span>
        <span v-if="isDrawing" class="text-xs text-orange-600">
          {{ selectedTool === 'polygon' ? '点击地图绘制多边形，双击完成' : '拖拽绘制形状' }}
        </span>
      </div>
    </div>

    <div ref="mapRef" class="map-box"></div>
  </div>
</template>

<style scoped>
  .ol-map-container {
    width: 100%;
    height: 500px;
    position: relative;
  }

  .tool-selector {
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
  }

  .tool-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 14px;
  }

  .tool-btn:hover {
    border-color: #adb5bd;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .tool-btn.active {
    border-color: #007bff;
    background: #e7f3ff;
    color: #007bff;
    font-weight: 500;
  }

  .tool-icon {
    font-size: 16px;
  }

  .tool-label {
    white-space: nowrap;
  }

  .map-tools {
    padding: 8px 12px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
    flex-wrap: wrap;
  }

  .status-info {
    flex-wrap: wrap;
  }

  .map-box {
    width: 100%;
    height: 400px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    overflow: hidden;
  }

  @media (max-width: 768px) {
    .tool-btn {
      padding: 6px 10px;
      font-size: 12px;
    }

    .tool-icon {
      font-size: 14px;
    }

    .map-tools {
      gap: 8px;
    }

    .status-info {
      margin-left: 0;
      margin-top: 8px;
    }
  }
</style>
