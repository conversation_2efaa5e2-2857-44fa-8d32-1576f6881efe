<script name="DAEdit" setup>
  import CuAreaDialog from '@/components/CuAreaDialog/index.vue'
  import { updateTaskAndFreqProtection } from '@/api/pgmx/freqProtection.js'
  import { ElMessage } from 'element-plus'

  const props = defineProps({
    currentRowValue: {
      type: Object,
      default: () => ({})
    }
  })

  const emit = defineEmits(['updateListEvent'])
  const xTable = ref(null)
  const areaDialog = ref(false)
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const taskName = ref(props.currentRowValue.taskName || '') // 任务名称
  const list = ref(props.currentRowValue.demandList || []) // 表格数据
  const currentUsageAreaEntity = ref(null) // 当前使用区域实体
  const currentTaskId = ref(null) // 任务ID
  const validationErrors = ref({}) // 用于存储校验错误信息

  // 校验规则
  const validationRules = {
    freqBandUp: [
      { required: true, message: '频率上限不能为空' },
      {
        validator: value => !isNaN(parseFloat(value)) && parseFloat(value) > 0,
        message: '请输入有效的正数'
      }
    ],
    freqBandDown: [
      { required: true, message: '频率下限不能为空' },
      {
        validator: value => !isNaN(parseFloat(value)) && parseFloat(value) > 0,
        message: '请输入有效的正数'
      },
      {
        validator: (value, row) => {
          const up = parseFloat(row.freqBandUp)
          const down = parseFloat(value)
          return !up || !down || down < up
        },
        message: '下限应小于上限'
      }
    ],
    protectObject: [{ required: true, message: '保护对象不能为空' }],
    startTime: [{ required: true, message: '开始时间不能为空' }],
    endTime: [
      { required: true, message: '结束时间不能为空' },
      {
        validator: (value, row) => {
          if (!value || !row.startTime) return true
          return new Date(value) > new Date(row.startTime)
        },
        message: '结束时间应晚于开始时间'
      }
    ]
  }

  // 添加一个新的空行
  const handleAdd = () => {
    list.value.push({
      id: Date.now(), // 使用时间戳作为唯一ID，避免冲突
      taskName: '',
      freqBandUp: '',
      freqBandDown: '',
      protectObject: '',
      startTime: '',
      endTime: '',
      protectAreaStr: '',
      protectAreaObject: {
        geoPointList: [
          {
            latitude: '',
            longitude: ''
          }
        ],
        latitude: '',
        longitude: '',
        radius: '',
        type: 0
      }
    })
  }

  /**
   * 验证单个字段
   * @param {Object} row 行数据
   * @param {String} field 字段名
   * @returns {Boolean} 验证是否通过
   */
  const validateField = (row, field) => {
    if (!validationRules[field]) return true

    const rowId = row.id
    if (!validationErrors.value[rowId]) {
      validationErrors.value[rowId] = {}
    }

    // 清除该字段的错误信息
    validationErrors.value[rowId][field] = ''

    // 应用验证规则
    for (const rule of validationRules[field]) {
      // 检查必填项
      if (rule.required && !row[field] && row[field] !== 0) {
        validationErrors.value[rowId][field] = rule.message
        return false
      }

      // 检查自定义验证
      if (rule.validator && !rule.validator(row[field], row)) {
        validationErrors.value[rowId][field] = rule.message
        return false
      }
    }

    return true
  }

  /**
   * 验证整个表格
   */
  const validateTable = () => {
    let isValid = true
    validationErrors.value = {}

    list.value.forEach(row => {
      const rowId = row.id
      validationErrors.value[rowId] = {}

      // 验证每个字段
      Object.keys(validationRules).forEach(field => {
        if (!validateField(row, field)) {
          isValid = false
        }
      })
    })

    return isValid
  }

  /**
   * 处理单元格值变更
   */
  const handleCellChange = ({ row, column }) => {
    const field = column.property
    if (validationRules[field]) {
      validateField(row, field)
    }
  }

  /**
   *  处理编辑单元格点击事件
   * @param params 事件参数，包含当前单元格的信息
   */
  const handleEditClick = params => {
    if (!params || !params.row || !params.column) return
    const { row, column } = params
    const field = column.property

    // 如果是点击了保护区域字段，则打开自定义弹窗
    if (field === 'protectAreaStr') {
      // 赋值当前行保护区域和ID
      currentUsageAreaEntity.value = row.protectAreaObject
      currentTaskId.value = row.id
      params.$table.clearEdit()
      // 再打开自定义区域弹窗
      areaDialog.value = true
      return
    }
  }

  /**
   * 处理区域弹窗确认事件，用于处理区域选择后的数据并回显到表格中
   */
  const areaDialogConfirm = ({ data, type }) => {
    const targetIndex = list.value.findIndex(item => item.id === currentTaskId.value)
    if (targetIndex === -1) return
    if (type === 'circle') {
      list.value[targetIndex].protectAreaObject.longitude = data.longitude
      list.value[targetIndex].protectAreaObject.latitude = data.latitude
      list.value[targetIndex].protectAreaObject.radius = data.radius
      list.value[targetIndex].protectAreaObject.type = 1
      list.value[
        targetIndex
      ].protectAreaStr = `[O(${data.longitude}, ${data.latitude});R(${data.radius})km]`
    } else {
      list.value[targetIndex].protectAreaObject.geoPointList = data.map(item => ({
        longitude: item.longitude,
        latitude: item.latitude
      }))
      list.value[targetIndex].protectAreaObject.type = 2
      list.value[targetIndex].protectAreaStr = data
        .map(item => `${item.longitude},${item.latitude}`)
        .join(';')
    }
  }

  /** *删除当前行数据 */
  const removeRow = index => {
    list.value.splice(index, 1)
  }

  /**
   * 确认编辑，提交表单数据到后端接口
   */
  const confirmEdit = () => {
    // 表单验证
    if (!validateTable()) {
      ElMessage.error('请修正表单中的错误')
      return
    }

    if (!taskName.value.trim()) {
      ElMessage.warning('作战任务名称不能为空')
      return
    }

    const queryData = {
      ...props.currentRowValue,
      taskName: taskName.value,
      demandList: list.value
    }

    updateTaskAndFreqProtection(queryData)
      .then(res => {
        if (res.code === 200) {
          ElMessage.success('编辑成功')
          emit('updateListEvent')
          dialogTableVisible.value = false
        }
      })
      .catch(err => {
        console.error('更新失败:', err)
        ElMessage.error('更新失败，请重试')
      })
  }

  // 获取错误信息
  const getErrorMessage = (rowId, field) => {
    if (!validationErrors.value[rowId]) return ''
    return validationErrors.value[rowId][field] || ''
  }

  onMounted(() => {
    // 组件挂载后，初始化验证状态
    validateTable()
  })
</script>
<template>
  <cu-dialog
    v-model="dialogTableVisible"
    title="用频需求编辑"
    width="860px"
    min-height="500px"
    @confirm="confirmEdit"
    @cancel="dialogTableVisible = false"
  >
    <div class="flex items-center mb-4">
      <div class="font-bold text-[20px] mr-4">作战任务</div>
      <vxe-input
        style="min-width: 300px; margin-right: 10px"
        v-model="taskName"
        placeholder="请输入作战任务名称"
      />
    </div>
    <div>
      <div class="font-bold text-[20px] mr-4">保护频段</div>
      <cu-button class="my-2" type="primary" content="新增" @click="handleAdd"></cu-button>
    </div>
    <vxe-table
      ref="xTable"
      border
      stripe
      min-height="280"
      :data="list"
      :row-config="{ isCurrent: true, isHover: true }"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
      @cell-click="handleEditClick"
      @edit-closed="handleCellChange"
    >
      <vxe-column type="seq" width="50" fixed="left" align="center" />
      <vxe-column
        field="freqBandUp"
        title="保护频率上限(MHz)"
        min-width="180"
        align="center"
        :edit-render="{}"
      >
        <template #default="{ row }">
          <div>
            <span>{{ row.freqBandUp }}</span>
            <div v-if="getErrorMessage(row.id, 'freqBandUp')" class="text-red-500 text-xs mt-1">
              {{ getErrorMessage(row.id, 'freqBandUp') }}
            </div>
          </div>
        </template>
        <template #edit="{ row }">
          <vxe-input v-model="row.freqBandUp" placeholder="请输入保护频率上限" />
        </template>
      </vxe-column>
      <vxe-column
        field="freqBandDown"
        title="保护频率下限(MHz)"
        min-width="180"
        align="center"
        :edit-render="{}"
      >
        <template #default="{ row }">
          <div>
            <span>{{ row.freqBandDown }}</span>
            <div v-if="getErrorMessage(row.id, 'freqBandDown')" class="text-red-500 text-xs mt-1">
              {{ getErrorMessage(row.id, 'freqBandDown') }}
            </div>
          </div>
        </template>
        <template #edit="{ row }">
          <vxe-input v-model="row.freqBandDown" placeholder="请输入保护频率下限" />
        </template>
      </vxe-column>
      <vxe-column
        field="protectObject"
        title="保护对象"
        min-width="120"
        align="center"
        :edit-render="{}"
      >
        <template #default="{ row }">
          <div>
            <span>{{ row.protectObject }}</span>
            <div v-if="getErrorMessage(row.id, 'protectObject')" class="text-red-500 text-xs mt-1">
              {{ getErrorMessage(row.id, 'protectObject') }}
            </div>
          </div>
        </template>
        <template #edit="{ row }">
          <vxe-input v-model="row.protectObject" placeholder="请输入保护对象" />
        </template>
      </vxe-column>
      <vxe-column
        field="startTime"
        title="保护开始时间"
        min-width="180"
        align="center"
        :edit-render="{}"
      >
        <template #default="{ row }">
          <div>
            <span>{{ row.startTime }}</span>
            <div v-if="getErrorMessage(row.id, 'startTime')" class="text-red-500 text-xs mt-1">
              {{ getErrorMessage(row.id, 'startTime') }}
            </div>
          </div>
        </template>
        <template #edit="{ row }">
          <vxe-input type="datetime" v-model="row.startTime" />
        </template>
      </vxe-column>
      <vxe-column
        field="endTime"
        title="保护结束时间"
        min-width="180"
        align="center"
        :edit-render="{}"
      >
        <template #default="{ row }">
          <div>
            <span>{{ row.endTime }}</span>
            <div v-if="getErrorMessage(row.id, 'endTime')" class="text-red-500 text-xs mt-1">
              {{ getErrorMessage(row.id, 'endTime') }}
            </div>
          </div>
        </template>
        <template #edit="{ row }">
          <vxe-input type="datetime" v-model="row.endTime" />
        </template>
      </vxe-column>
      <vxe-column
        field="protectAreaStr"
        title="保护区域"
        width="120"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
        :edit-render="{}"
      >
        <template #default="{ row }">
          <div
            class="cursor-pointer"
            :class="{ 'text-[#00603b] underline': !row.protectAreaStr }"
            @click="
              handleEditClick({ row, column: { property: 'protectAreaStr' }, $table: xTable })
            "
          >
            {{ row.protectAreaStr || '设置区域' }}
          </div>
        </template>
      </vxe-column>
      <vxe-column field="操作" title="操作" width="100" fixed="right" align="center">
        <template #default="{ rowIndex }">
          <cu-button type="info" content="删除" @click="removeRow(rowIndex)" />
        </template>
      </vxe-column>
    </vxe-table>
  </cu-dialog>

  <!-- 自定义组件/默认输入框 -->
  <cu-area-dialog
    v-if="areaDialog"
    v-model="areaDialog"
    :usage-area-entity="currentUsageAreaEntity"
    @confirm="areaDialogConfirm"
  />
</template>

<style lang="scss" scoped>
  :deep(.vxe-buttons--wrapper) {
    gap: 10px;
  }

  :deep(.vxe-table--render-default .vxe-cell--valid-error) {
    background-color: rgba(255, 0, 0, 0.05);
  }
</style>
