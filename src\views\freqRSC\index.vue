<script setup>
  import useList from '@/api/tool/filemanage/tableFunctionPro'
  import freqPlanDialog from './components/freqPlanDialog.vue'
  import { processDictionary } from '@/utils/index.js'
  import { queryTaskOption } from '@/api/pgmx/useFreqTask.js'
  import {
    readTaskPlanEquipList,
    readPlanUseFreqEquip,
    updateUseFreqPlan,
    batchDelFreqPlan
  } from '@/api/pgmx/schedulingMonitoring.js'
  import { ElMessage } from 'element-plus'

  const filterOption = ref({
    immediate: false,
    taskName: ''
  })
  const xTable = ref(null)
  const { list, loading, curPage, size, total, timeSearch, deleteCurrentLine } = useList(
    readTaskPlanEquipList,
    batchDelFreqPlan,
    filterOption.value,
    xTable
  )

  const freqDialogTableVisible = ref(false)
  const taskOptions = ref([]) //zz任务枚举
  const useFreqStrategyOptions = ref([]) // 规划策略枚举
  const equipmentModelOptions = ref([]) //设备型号枚举
  const unitOptions = ref([]) //用频单位枚举
  const currentRowValue = ref({}) //当前行数据

  const getDictionaryData = async () => {
    processDictionary('pgmx_usefreqstrategy', useFreqStrategyOptions)
    processDictionary('pgmx_equipment', equipmentModelOptions)
    processDictionary('pgmx_unit', unitOptions)
    await queryTaskOption().then(res => {
      if (res.code === 200) {
        taskOptions.value = res.data
      }
    })
  }

  /** 查询 */
  const queryTask = () => {
    if (!filterOption.value.taskName) ElMessage.error('请选择任务名称!')
    // 这里编写查询任务的逻辑
    timeSearch(filterOption.value)
  }

  const createPlan = () => {
    // TODO 这里编写调度规划的逻辑
  }

  const handleCellClick = ({ row, column, $event }) => {
    if (column.field === 'freqPlan') {
      currentRowValue.value = row
      readPlanUseFreqEquip({
        freqUsageId: row.id
      })
        .then(res => {
          if (res.code === 200) {
            nextTick(() => {
              currentRowValue.value.equipmentList = res.data
            })
          }
        })
        .finally(() => {
          freqDialogTableVisible.value = true
        })
    }
  }

  const handleExport = () => {
    // 这里编写导出的逻辑
  }

  /** 更新用频计划 */
  const updateEquipPlan = queryData => {
    updateUseFreqPlan(queryData).then(res => {
      if (res.code === 200) {
        freqDialogTableVisible.value = false
        ElMessage.success('保存成功')
        timeSearch(filterOption.value)
      }
    })
  }

  onMounted(async () => {
    await getDictionaryData()
  })
</script>

<template>
  <div>
    <div class="flex items-center mb-4">
      <div class="w-[80px] flex items-center">ZZ任务</div>
      <vxe-select style="width: 300px" v-model="filterOption.taskName">
        <vxe-option
          v-for="item in taskOptions"
          :value="item.value"
          :label="item.label"
        ></vxe-option>
      </vxe-select>
      <cu-button
        width="98px"
        type="primary"
        class="ml-5"
        content="查询"
        @click="queryTask"
      ></cu-button>
      <cu-button
        type="primary"
        class="ml-5"
        content="生成调度修正方案"
        @click="createPlan"
      ></cu-button>
    </div>

    <vxe-table
      ref="xTable"
      border
      stripe
      size="medium"
      height="725"
      :data="list"
      :loading="loading"
      :row-config="{ isCurrent: true, isHover: true }"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
      @cell-click="handleCellClick"
    >
      <vxe-column type="seq" width="60" fixed="left" align="center" />
      <vxe-column
        field="frequencyUsingUnit"
        title="用频单位"
        fixed="left"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
        <template #default="{ row }">
          {{ selectDictLabel(unitOptions, row.frequencyUsingUnit) }}
        </template>
      </vxe-column>
      <vxe-column
        field="createBy"
        title="装备型号"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
        <template #default="{ row }">
          {{ selectDictLabel(equipmentModelOptions, row.equipmentModel) }}
        </template>
      </vxe-column>
      <vxe-column field="freqUsageEquipment" title="用频装备" align="center" />
      <vxe-column field="freqPlan" title="用频计划" align="center" :edit-render="{}">
        <template #default="{ row }">
          <div class="cursor-pointer" :class="{ 'text-[#00603b] underline': !row.freqPlan }">
            {{ row.equipmentList?.length > 0 ? '修改计划' : '设置计划' }}
          </div>
        </template>
      </vxe-column>
      <vxe-column field="操作" title="操作" width="120" fixed="right" align="center">
        <template #default="{ row }">
          <cu-button type="info" content="导出" @click="handleExport(row)" />
          <cu-button type="info" content="删除" @click="deleteCurrentLine(row)" />
        </template>
      </vxe-column>
    </vxe-table>
    <!-- 分页 -->
    <p>
      <vxe-pager
        v-model:current-page="curPage"
        v-model:page-size="size"
        class="vxe-page"
        perfect
        :total="total"
        :page-sizes="[10, 20, 50, 100, 200, 500]"
        :layouts="[
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'Sizes',
          'FullJump',
          'Total'
        ]"
      />
    </p>
  </div>

  <freq-plan-dialog
    v-if="freqDialogTableVisible"
    v-model="freqDialogTableVisible"
    :planObject="currentRowValue"
    @confirmPlan="updateEquipPlan"
  ></freq-plan-dialog>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
