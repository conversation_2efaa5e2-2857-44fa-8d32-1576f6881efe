<script setup>
  // 导入组件
  import mapDistributionCom from './components/mapDistributionCom.vue'
  import dataSheetCom from './components/dataSheetCom.vue'
  import facilityTypeStatisticsCom from './components/facilityTypeStatisticsCom.vue'
  import CuAreaMapCom from '@/components/CuAreaMapCom/index.vue'
  import { commFacilities } from '@/api/pgmx/commfac.js'

  const CuAreaMapComRef = ref(null)
  const resultActiveName = ref('mapDistribution') // 默认选中地图分布

  const dataSheetData = ref([])
  const xAxisData = ref(['电信传输设施', '无线通信设施', '交换设施', '终端设备'])
  const seriesData = ref([863, 807, 138, 295])

  const query = async () => {
    try {
      // 调用子组件的校验方法
      await CuAreaMapComRef.value.validateForm()
      const active = CuAreaMapComRef.value.activeName
      // 如果是地图模式（无需表单校验），或者表单都通过了，继续下面逻辑
      const { polygon, circle } = CuAreaMapComRef.value.formData
      const queryData = {
        geoPointList: [
          { latitude: polygon.lat, longitude: polygon.lon },
          { latitude: polygon.latEnd, longitude: polygon.lonEnd }
        ],
        longitude: circle.cLon,
        latitude: circle.cLat,
        radius: circle.radius,
        type: active === 'polygon' ? 3 : active === 'circle' ? 1 : 2
      }

      commFacilities(queryData).then(res => {
        if (res.code === 200) {
          dataSheetData.value = res.data.commFacilities
          // res.data.statistic   // 统计数据，暂时没用到，暂时注释掉
        }
      })
    } catch (error) {
      console.error('校验失败:', error.message)
      // 可以在这里处理校验失败的情况，例如显示错误信息
    }
  }

  const exportData = () => {
    console.log('导出')
  }
</script>

<template>
  <div>
    <div class="flex gap-x-4 mb-5">
      <CuAreaMapCom ref="CuAreaMapComRef"></CuAreaMapCom>
      <div class="flex flex-col justify-center items-center gap-y-4">
        <cu-button content="查询" @click="query"></cu-button>
        <cu-button style="margin-left: 0" content="导出数据表" @click="exportData"></cu-button>
      </div>
    </div>
    <el-tabs v-model="resultActiveName" type="border-card" class="result-tabs">
      <!-- 地图分布 -->
      <el-tab-pane label="地图分布" name="mapDistribution">
        <mapDistributionCom></mapDistributionCom>
      </el-tab-pane>

      <!-- 数据表 -->
      <el-tab-pane label="数据表" name="dataSheet">
        <dataSheetCom :table-data="dataSheetData"></dataSheetCom>
      </el-tab-pane>

      <!-- 设施类型统计 -->
      <el-tab-pane label="设施类型统计" name="facilityTypeStatistics">
        <facilityTypeStatisticsCom
          :xAxisData="xAxisData"
          :seriesData="seriesData"
        ></facilityTypeStatisticsCom>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped lang="scss">
  .result-tabs {
    min-height: 520px;
    :deep(.el-tabs__nav) {
      display: flex;
      width: 100%;
    }

    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      padding: 0 16px;
    }
  }
</style>
