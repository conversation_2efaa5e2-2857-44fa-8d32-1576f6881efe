<script setup>
  import CuAreaMapCom from '@/components/CuAreaMapCom/index.vue'
  import monitorTaskCom from '../components/monitorTaskCom.vue'
  import monitorSSCom from '../components/monitorSSCom.vue'

  import { queryMonitorTask, queryMonitorSignal } from '@/api/pgmx/emeMonitor.js'
  import { ElMessage } from 'element-plus'

  const CuAreaMapComRef = ref(null)
  const monitorTaskRef = ref(null)
  const formData = reactive({
    startTime: '',
    endTime: ''
  })

  const monitorList = ref([])
  const monitorChartList = ref([])
  const chartData = ref({})

  const queryMonitor = async () => {
    try {
      // 调用子组件的校验方法
      await CuAreaMapComRef.value.validateForm()
      const active = CuAreaMapComRef.value.activeName
      const { polygon, circle } = CuAreaMapComRef.value.formData
      const queryData = {
        area: {
          geoPointList: [
            { latitude: polygon.lat, longitude: polygon.lon },
            { latitude: polygon.latEnd, longitude: polygon.lonEnd }
          ],
          longitude: circle.cLon,
          latitude: circle.cLat,
          radius: circle.radius,
          type: active === 'polygon' ? 3 : active === 'circle' ? 1 : 2
        },
        starTime: formData.startTime,
        endTime: formData.endTime
      }
      // 这里编写查询监测任务的逻辑
      console.log('查询监测任务', queryData)
      queryMonitorTask(queryData).then(res => {
        monitorList.value = res.data
      })
    } catch (error) {}
  }

  const monitorStatistic = () => {
    // 这里编写监测统计的逻辑
    const selectedRow = monitorTaskRef.value.getSelectedRow()
    if (!selectedRow) {
      return ElMessage.error('请选择监测任务')
    }
    queryMonitorSignal({ monitorTaskId: selectedRow.id }).then(res => {
      console.log(res)
      monitorChartList.value = [
        {
          id: 1,
          freq: 100e6,
          bd: 20533,
          power: 39.222
        },
        {
          id: 1,
          freq: 200e6,
          bd: 15533,
          power: 39.222
        }
        // 更多数据...
      ]
      chartData.value = {
        xAxisData: [21, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 200, 300, 400, 500, 1000, 1600],
        seriesData: [12, 34, 23, 45, 34, 56, 78, 90, 12, 34, 56, 78, 90, 12, 34, 56, 78]
      }
    })
  }
</script>

<template>
  <el-row>
    <el-col>
      <div class="w-full h-10 flex items-center justify-center bg-[#007575] text-white my-1 mb-2">
        Step1:监测任务选择
      </div>
      <div class="grid grid-cols-2 gap-4 w-full my-4">
        <CuAreaMapCom ref="CuAreaMapComRef"></CuAreaMapCom>
        <div>
          <cu-title title="监测时间"> </cu-title>
          <vxe-form :data="formData">
            <vxe-form-item span="24" field="startTime" title="开始时间">
              <vxe-input
                v-model="formData.startTime"
                type="datetime"
                placeholder="选择开始时间"
                clearable
              />
            </vxe-form-item>
            <vxe-form-item span="24" field="endTime" title="结束时间">
              <vxe-input
                v-model="formData.endTime"
                type="datetime"
                placeholder="选择结束时间"
                clearable
              />
            </vxe-form-item>
            <vxe-form-item span="24">
              <div class="text-right">
                <cu-button type="primary" content="查询监测任务" @click="queryMonitor" />
              </div>
            </vxe-form-item>
          </vxe-form>
        </div>
      </div>
      <monitorTaskCom ref="monitorTaskRef" :list="monitorList" />
    </el-col>
    <el-col>
      <div class="w-full h-10 flex items-center justify-center bg-[#00b7b7] text-white my-5">
        Step2:监测信号分析统计
      </div>
      <monitorSSCom :list="monitorChartList" :chart-data="chartData" @start="monitorStatistic" />
    </el-col>
  </el-row>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
