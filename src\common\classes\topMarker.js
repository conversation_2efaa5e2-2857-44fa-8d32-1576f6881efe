export default class TopMarker {
  constructor(chart, color = '#f3f3f3') {
    this.chart = chart // 图表实例
    this.point = this.getMaxPoint() // 该频标在实时线上的基准点
    this.chart.topMarker = this
    this.index = '峰值'
    this.marker = null // 标记点renderer定义的svg路径
    this.drawCustomShape(color)
    this.visible = true
  }

  getMaxPoint() {
    const points = this.getPoints(this.chart.series[0])
    if (!points.length) {
      return null
    }
    let maxPoint = points[0]
    points.forEach(point => {
      if (point.y > maxPoint.y) {
        maxPoint = point
      }
    })
    return maxPoint
  }

  drawCustomShape(color = '#f3f3f3') {
    if (this.marker) {
      this.marker.destroy()
    }
    if (!this.point) {
      return
    }
    const renderer = this.chart.renderer
    const x = this.chart.xAxis[0].toPixels(this.point.x)
    const y = this.chart.plotTop + this.chart.plotSizeY // 确保 y 轴在 x 轴底部
    const path = [] // 添加路径
    path.push('M', x - 8, y) // 移动到起始点
    path.push('L', x + 8, y) // 画一条线
    path.push('L', x + 8, y - 20) // 画一条线
    path.push('L', x, y - 30) // 画一条线
    path.push('L', x - 8, y - 20) // 画一条线
    path.push('Z') // 关闭路径
    path.push('M', x, y - 30)
    path.push('L', x, this.chart.plotTop)
    const strokeColor = color
    // const strokeColor = '#000';
    // const fillColor = 'rgba(243, 243, 243, 0.3)';
    const fillColor = color
    this.marker = renderer
      .path(path)
      .attr({ stroke: strokeColor, 'stroke-width': 1, fill: fillColor, zIndex: 2 })
      .add()
  }

  getPoints(series) {
    if (series.data && series.data.length > 0) {
      return series.data.map(point => ({
        x: point.x,
        y: point.y,
        plotX: this.chart.xAxis[0].toPixels(point.x),
        plotY: this.chart.yAxis[0].toPixels(point.y)
      }))
    } else if (series.xData && series.yData && series.xData.length > 0) {
      return series.xData.map((x, i) => ({
        x: x,
        y: series.yData[i],
        plotX: this.chart.xAxis[0].toPixels(x),
        plotY: this.chart.yAxis[0].toPixels(series.yData[i])
      }))
    }
    return []
  }

  update(color) {
    this.point = this.getMaxPoint()
    this.visible && this.drawCustomShape(color)
  }

  setVisible(val) {
    this.visible = val
    if (this.marker) {
      if (val) {
        this.marker.show()
      } else {
        this.marker.hide()
      }
    }
  }
}
