<template>
  <div>
    <div class="flex items-center mb-4">
      <div class="w-[80px] flex items-center">ZZ任务</div>
      <vxe-select style="width: 300px" v-model="taskId">
        <vxe-option
          v-for="item in taskOptions"
          :key="item.value"
          :value="item.value"
          :label="item.label"
        />
      </vxe-select>
      <cu-button width="98px" type="primary" class="ml-5" content="确定" @click="confirmTask" />
    </div>

    <!-- 频段图表区域 -->
    <div ref="chartRef" class="freq-chart" />
  </div>
</template>

<script setup>
  import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue'
  import * as echarts from 'echarts/core'
  import { CanvasRenderer } from 'echarts/renderers'
  import { TooltipComponent, GridComponent, LegendComponent } from 'echarts/components'
  import { CustomChart } from 'echarts/charts'
  import { BarChart } from 'echarts/charts'
  echarts.use([BarChart])

  // 注册 ECharts 模块
  echarts.use([CanvasRenderer, TooltipComponent, GridComponent, LegendComponent, CustomChart])

  // 任务下拉数据
  const taskId = ref('option1')
  const taskOptions = ref([{ value: 'option1', label: '电子突防演练3号任务' }])

  // 图表容器和实例
  const chartRef = ref(null)
  let chartInstance = null

  // 组件卸载时销毁实例
  onBeforeUnmount(() => {
    if (chartInstance) {
      chartInstance.dispose()
      chartInstance = null
    }
  })

  // 点击确定时渲染或刷新图表
  const confirmTask = async () => {
    // TODO: 根据 taskId 拉取数据并更新 ranges
    // 这里用示例数据
    const ranges = [
      [30, 2000],
      [2000, 5000],
      [4000, 8000],
      [8000, 12000],
      [12000, 18000]
    ]
    const categories = ['设备A', '设备B', '设备C', '设备D', '设备E']

    await nextTick()
    if (!chartInstance) {
      chartInstance = echarts.init(chartRef.value)
      window.addEventListener('resize', resizeChart)
    }
    chartInstance.setOption(makeOption(categories, ranges))
  }

  // 窗口尺寸变化重新调整
  function resizeChart() {
    chartInstance?.resize()
  }

  // 构建 ECharts 配置函数
  function makeOption(categories, ranges) {
    const starts = ranges.map(r => r[0])
    const widths = ranges.map(r => r[1] - r[0])
    return {
      tooltip: {
        formatter: ({ dataIndex }) => {
          const [s, e] = ranges[dataIndex]
          return `${categories[dataIndex]}<br/>${s}MHz - ${e}MHz`
        }
      },
      legend: { show: false },
      grid: { left: 100, right: 40, top: 40, bottom: 40 },
      xAxis: {
        type: 'value',
        min: 30,
        max: 18000,
        splitLine: { show: true },
        axisLabel: {
          formatter: v => {
            if (v === 30) return '30MHz'
            if (v === 9000) return '9GHz'
            if (v === 18000) return '18GHz'
            return v >= 1000 ? v / 1000 + 'GHz' : v + 'MHz'
          }
        }
      },
      yAxis: {
        type: 'category',
        data: categories,
        axisTick: { show: false },
        splitLine: { show: true }
      },
      series: [
        {
          name: '起点',
          type: 'bar',
          stack: 'freq',
          itemStyle: { color: 'transparent' },
          emphasis: { itemStyle: { color: 'transparent' } },
          data: starts
        },
        {
          name: '频段',
          type: 'bar',
          stack: 'freq',
          barWidth: 20,
          data: widths,
          itemStyle: { color: '#b0d296' }
        }
      ]
    }
  }

  // 页面挂载时渲染一次
  onMounted(confirmTask)
</script>

<style scoped>
  .freq-chart {
    width: 100%;
    height: 360px;
  }
</style>
