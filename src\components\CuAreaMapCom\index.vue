<script setup>
  import CuMapPolygonSelector from '@/components/CuMapPolygonSelector/index.vue'

  const activeName = ref('polygon') // 默认选中多边形
  const validConfig = reactive({
    theme: 'normal'
  })
  const polygonFormRef = ref(null)
  const circleFormRef = ref(null)

  const polygonCoords = ref([]) // 存多边形坐标
  const onPolygonChange = ({ type, coords }) => {
    // 这里能实时拿到选中区域 [{longitude, latitude}, ...]
    console.log('父组件收到坐标:', type, coords)
    if (type === 'Circle') {
      const [lng, lat] = coords.center
      const degreeRadius = coords.radius
      // 1度经度 ≈ 111.32km × cos(纬度)
      // 换算为 km
      const radiusKm = degreeRadius * 111.32 * Math.cos((lat * Math.PI) / 180)
      console.log(`中心点经度: ${lng}`)
      console.log(`中心点纬度: ${lat}`)
      console.log(`半径（km）: ${radiusKm.toFixed(3)}`)
    }
  }

  // 表单数据
  const formData = reactive({
    circle: {
      cLon: '',
      cLat: '',
      radius: ''
    },
    polygon: {
      lon: '',
      lonEnd: '',
      lat: '',
      latEnd: ''
    }
  })

  // 验证规则
  const rules = {
    circle: {
      cLon: [
        { required: true, message: '请输入圆心经度', trigger: 'blur' },
        { pattern: /^-?\d+(\.\d+)?$/, message: '请输入有效数字', trigger: 'blur' }
      ],
      cLat: [
        { required: true, message: '请输入圆心纬度', trigger: 'blur' },
        { pattern: /^-?\d+(\.\d+)?$/, message: '请输入有效数字', trigger: 'blur' }
      ],
      radius: [
        { required: true, message: '请输入半径', trigger: 'blur' },
        {
          validator({ itemValue }) {
            if (itemValue <= 0) {
              return new Error('半径必须大于0')
            } else {
              return true
            }
          }
        }
      ]
    },
    polygon: {
      lon: [
        { required: true, message: '请输入起始经度', trigger: 'blur' },
        { pattern: /^-?\d+(\.\d+)?$/, message: '请输入有效数字', trigger: 'blur' },
        {
          validator({ itemValue }) {
            const v = parseFloat(itemValue)
            if (isNaN(v)) {
              return new Error('请输入有效数字')
            }
            if (v < -180 || v > 180) {
              return new Error('经度必须在 -180 到 180 之间')
            }
            return true
          },
          trigger: 'blur'
        }
      ],
      lonEnd: [
        { required: true, message: '请输入结束经度', trigger: 'blur' },
        { pattern: /^-?\d+(\.\d+)?$/, message: '请输入有效数字', trigger: 'blur' },
        {
          validator({ itemValue }) {
            const v = parseFloat(itemValue)
            if (isNaN(v)) {
              return new Error('请输入有效数字')
            }
            if (v < -180 || v > 180) {
              return new Error('经度必须在 -180 到 180 之间')
            }
            return true
          },
          trigger: 'blur'
        }
      ],
      lat: [
        { required: true, message: '请输入起始纬度', trigger: 'blur' },
        { pattern: /^-?\d+(\.\d+)?$/, message: '请输入有效数字', trigger: 'blur' },
        {
          validator({ itemValue }) {
            const v = parseFloat(itemValue)
            if (isNaN(v)) {
              return new Error('请输入有效数字')
            }
            if (v < -90 || v > 90) {
              return new Error('纬度必须在 -90 到 90 之间')
            }
            return true
          },
          trigger: 'blur'
        }
      ],
      latEnd: [
        { required: true, message: '请输入结束纬度', trigger: 'blur' },
        { pattern: /^-?\d+(\.\d+)?$/, message: '请输入有效数字', trigger: 'blur' },
        {
          validator({ itemValue }) {
            const v = parseFloat(itemValue)
            if (isNaN(v)) {
              return new Error('请输入有效数字')
            }
            if (v < -90 || v > 90) {
              return new Error('纬度必须在 -90 到 90 之间')
            }
            return true
          },
          trigger: 'blur'
        }
      ]
    }
  }

  const handleTabChange = () => {
    if (activeName.value === 'circle') {
      formData.polygon.lon = ''
      formData.polygon.lat = ''
      formData.polygon.lonEnd = ''
      formData.polygon.latEnd = ''
      polygonFormRef.value?.clearValidate()
    } else {
      formData.circle.cLon = ''
      formData.circle.cLat = ''
      formData.circle.radius = ''
      circleFormRef.value?.clearValidate()
    }
  }

  // 修正校验方法
  const validateForm = async () => {
    try {
      if (activeName.value === 'polygon') {
        const valid = await polygonFormRef.value.validate()
        console.log(valid)
        if (valid) {
          throw new Error('多边形校验失败')
        }
        return true
      }
      if (activeName.value === 'circle') {
        const valid = await circleFormRef.value.validate()
        if (valid) {
          throw new Error('圆形校验失败')
        }
        return true
      }

      return true
    } catch (error) {
      console.error('表单校验错误:', error)
      throw error
    }
  }

  // 切换标签时重置表单
  watch(activeName, newTab => {
    if (newTab === 'circle') {
      formData.polygon.lon = ''
      formData.polygon.lat = ''
      formData.polygon.lonEnd = ''
      formData.polygon.latEnd = ''
      polygonFormRef.value?.clearValidate()
    } else {
      formData.circle.cLon = ''
      formData.circle.cLat = ''
      formData.circle.radius = ''
      circleFormRef.value?.clearValidate()
    }
  })

  defineExpose({
    activeName,
    formData,
    validateForm // 只暴露必要的方法，不暴露内部的 ref
  })
</script>

<template>
  <el-tabs
    v-model="activeName"
    type="border-card"
    class="region-tabs"
    @tab-change="handleTabChange"
  >
    <!-- 多边形区域 - 修正表单项配置 -->
    <el-tab-pane label="矩形区域" name="polygon">
      <vxe-form
        ref="polygonFormRef"
        title-colon
        :rules="rules.polygon"
        :valid-config="validConfig"
        :data="formData.polygon"
        :label-config="{ width: '80px', colon: true, align: 'right' }"
      >
        <!-- 经度 - 拆分为两个独立的表单项 -->
        <vxe-form-item field="lon" title="起始经度">
          <vxe-input
            v-model="formData.polygon.lon"
            type="float"
            placeholder="例如：116.404"
            clearable
            min="-180"
            max="180"
            step="0.01"
          />
        </vxe-form-item>

        <vxe-form-item field="lonEnd" title="结束经度">
          <vxe-input
            v-model="formData.polygon.lonEnd"
            type="float"
            placeholder="例如：116.405"
            clearable
            min="-180"
            max="180"
            step="0.01"
          />
        </vxe-form-item>

        <!-- 纬度 - 拆分为两个独立的表单项 -->
        <vxe-form-item field="lat" title="起始纬度">
          <vxe-input
            v-model="formData.polygon.lat"
            type="float"
            placeholder="例如：39.915"
            clearable
            min="-90"
            max="90"
            step="0.01"
          />
        </vxe-form-item>

        <vxe-form-item field="latEnd" title="结束纬度">
          <vxe-input
            v-model="formData.polygon.latEnd"
            type="float"
            placeholder="例如：39.916"
            clearable
            min="-90"
            max="90"
            step="0.01"
          />
        </vxe-form-item>
      </vxe-form>
    </el-tab-pane>

    <!-- 圆形区域 -->
    <el-tab-pane label="圆形区域" name="circle">
      <vxe-form
        ref="circleFormRef"
        :data="formData.circle"
        :rules="rules.circle"
        :valid-config="validConfig"
        title-align="right"
        title-width="180"
        title-colon
      >
        <vxe-form-item field="cLon" span="24" title="圆心经度（西半球负）">
          <vxe-input
            v-model="formData.circle.cLon"
            type="float"
            placeholder="例如：116.404"
            clearable
            min="-180"
            max="180"
            step="0.01"
          />
        </vxe-form-item>
        <vxe-form-item field="cLat" span="24" title="圆心纬度（南半球负）">
          <vxe-input
            v-model="formData.circle.cLat"
            type="float"
            placeholder="例如：39.915"
            clearable
            min="-90"
            max="90"
            step="0.01"
          />
        </vxe-form-item>
        <vxe-form-item field="radius" span="24" title="半径（千米）">
          <vxe-input
            v-model="formData.circle.radius"
            type="float"
            placeholder="请输入大于0的数值"
            clearable
            min="0"
          />
        </vxe-form-item>
      </vxe-form>
    </el-tab-pane>

    <!-- 图选区域 -->
    <el-tab-pane label="图选区域" name="map">
      <CuMapPolygonSelector @change="onPolygonChange" />
    </el-tab-pane>
  </el-tabs>
</template>

<style lang="scss" scoped>
  .region-tabs {
    min-height: 230px;
    width: 100%;
    :deep(.el-tabs__nav) {
      display: flex;
      width: 100%;
    }

    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      padding: 0 16px;
    }
  }
</style>
