<script setup>
  import { queryTaskOption } from '@/api/pgmx/useFreqTask.js'
  import { readEquipTree, readUseFreqEquipment } from '@/api/pgmx/schedulingMonitoring.js'

  const taskId = ref(null)
  const taskOptions = ref([]) //用频方案枚举
  const treeTitle = ref('')
  const loading = ref(false)
  const treeList = ref([])
  const planData = ref([]) // 计划数据
  const actualData = ref([]) // 实际数据
  const errorData = ref([]) // 错误数据

  const getDictionaryData = async () => {
    await queryTaskOption().then(res => {
      if (res.code === 200) {
        taskOptions.value = res.data
        taskId.value = taskOptions.value[0].value
      }
    })
  }

  // 优化后的 diff 函数
  const isDiff = (row, field) => {
    if (!planData.value.length || !actualData.value.length) return false

    // 找到对应的计划数据行（使用时间作为唯一标识）
    const planRow = planData.value.find(item => item.time === row.time)
    if (!planRow) return true // 如果找不到对应的计划行，则认为是不一致的

    return planRow[field] !== row[field]
  }

  // 点击普通按钮
  const startMonitor = () => {
    console.log('startMonitor')
  }
  const showViolationList = () => {
    console.log('showViolationList')
  }

  const handlePlanChange = async ({ value }) => {
    taskId.value = value
    await initTreeList()
    // 清空已选择的数据
    treeTitle.value = ''
    planData.value = []
    actualData.value = []
    errorData.value = []
  }

  function getTreeTitle(record) {
    const path = []
    let node = record
    // 每次都在 treeList 里找 parent
    while (node) {
      path.unshift(node.title)
      if (!node.parentId) break
      node = treeList.value.find(item => item.id === node.parentId)
    }
    return path.join('/')
  }

  // 节点点击回调
  const handleNodeClick = async ({ node, event }) => {
    console.log('点击了节点：', node)

    // 如果有子节点，直接返回，不进行额外数据加载
    if (node.children && node.children.length > 0) return

    // 如果是叶子节点，加载用频计划和实际用频数据
    if (!node.hasChild) {
      treeTitle.value = getTreeTitle(node)
      loading.value = true

      try {
        // 这里应该调用获取用频数据的API
        const res = await readUseFreqEquipment({
          freqUsageId: node.id
        })

        if (res.code === 200) {
          planData.value = res.data || []
          // actualData.value = res.data?.actualData || []
          // errorData.value = res.data?.errorData || []
        }
      } catch (error) {
        console.error('加载用频数据失败', error)
      } finally {
        loading.value = false
      }
    }
  }

  // 获取节点的层级
  const getNodeLevel = node => {
    let level = 1
    let currentNode = node

    while (currentNode.parentId) {
      level++
      currentNode = treeList.value.find(item => item.id === currentNode.parentId)
      if (!currentNode) break
    }

    return level
  }

  const initTreeList = async () => {
    await readEquipTree({
      taskName: taskId.value,
      level: 1
    }).then(res => {
      if (res.code === 200) {
        treeList.value = res.data
      }
    })
  }

  // 树的懒加载方法
  const getNodeListApi = async ({ node }) => {
    const nodeLevel = getNodeLevel(node)

    // 如果节点已标记为不包含子节点，则不需要继续请求
    if (node.hasChild === false) return

    // 构建请求参数
    const params = {
      taskId: taskId.value,
      level: nodeLevel + 1 // 当前节点层级 + 1
    }

    // 根据不同层级添加不同参数
    if (nodeLevel === 1) {
      // 第一层，下一级请求需要添加equipmentModel
      params.frequencyUsingUnit = node.id
    } else if (nodeLevel === 2) {
      // 第二层，下一级请求需要添加equipmentModel和frequencyUsingUnit
      params.equipmentModel = node.id
      // 找到父节点ID作为equipmentModel
      const parentNode = treeList.value.find(item => item.id === node.parentId)
      params.frequencyUsingUnit = parentNode ? parentNode.id : ''
    }

    try {
      const res = await readEquipTree(params)
      if (res.code === 200) {
        // 更新节点的子节点数据
        return res.data
      }
    } catch (error) {
      console.error('加载子节点失败', error)
      node.hasChild = false // 请求失败时标记为没有子节点
    }
  }

  onMounted(async () => {
    await getDictionaryData()
    await initTreeList()
  })
</script>

<template>
  <div class="flex items-center mb-4">
    <div class="w-[80px] flex items-center">ZZ任务</div>
    <vxe-select style="width: 300px" v-model="taskId" @change="handlePlanChange">
      <vxe-option
        v-for="item in taskOptions"
        :key="item.value"
        :value="item.value"
        :label="item.label"
      />
    </vxe-select>
    <cu-button width="98px" type="primary" class="ml-5" content="启动监控" @click="startMonitor" />
    <cu-button type="primary" class="ml-5" content="违规调度清单" @click="showViolationList" />
  </div>

  <el-row :gutter="10">
    <el-col :span="5">
      <vxe-tree
        lazy
        :is-current="true"
        :is-hover="true"
        :data="treeList"
        :load-method="getNodeListApi"
        @node-click="handleNodeClick"
      >
        <template #icon="{ isExpand }">
          <vxe-icon v-if="isExpand" status="success" name="square-minus" />
          <vxe-icon v-else status="success" name="square-plus" />
        </template>
      </vxe-tree>
    </el-col>
    <el-col v-if="planData.length > 0 || actualData.length > 0" :span="19">
      <div class="flex items-center">
        <cu-title :title="treeTitle" />
        <div class="mb-4 text-red-500 font-bold text-lg ml-5" v-if="errorData.length > 0">{{
          treeTitle
        }}</div>
      </div>
      <div class="flex flex-col gap-4" v-loading="loading">
        <!-- 上：用频计划 -->
        <div class="flex-1">
          <div class="bg-cyan-200 text-center font-bold py-2 mb-2">用频计划</div>
          <vxe-table
            :data="planData"
            :row-config="{ isCurrent: true, isHover: true }"
            border
            stripe
            size="small"
            class="mt-0"
          >
            <vxe-column
              field="timeStr"
              min-width="120"
              title="用频时间"
              align="center"
              show-header-overflow
              show-overflow="title"
              show-footer-overflow
            />
            <vxe-column
              field="areaStr"
              min-width="180"
              title="用频区域"
              align="center"
              show-header-overflow
              show-overflow="title"
              show-footer-overflow
            />
            <vxe-column
              field="operatingFrequency"
              min-width="120"
              title="工作频率（MHz）"
              align="center"
            />
            <vxe-column
              field="emissionBandwidth"
              min-width="120"
              title="发射带宽（kHz）"
              align="center"
            />
            <vxe-column
              field="transmissionPower"
              min-width="120"
              title="发射功率（dBm）"
              align="center"
            />
          </vxe-table>
        </div>

        <!-- 下：实际用频 -->
        <div class="flex-1">
          <div class="bg-lime-300 text-center font-bold py-2 mb-2">实际用频</div>
          <vxe-table
            :data="actualData"
            :row-config="{ isCurrent: true, isHover: true }"
            border
            stripe
            size="small"
            class="mt-0"
          >
            <!-- 使用自定义模板来渲染单元格内容 -->
            <vxe-column field="time" title="用频时间" align="center">
              <template #default="{ row }">
                <span :class="{ 'text-red-500': isDiff(row, 'time') }">{{ row.time }}</span>
              </template>
            </vxe-column>
            <vxe-column field="area" title="用频区域" align="center">
              <template #default="{ row }">
                <span :class="{ 'text-red-500': isDiff(row, 'area') }">{{ row.area }}</span>
              </template>
            </vxe-column>
            <vxe-column field="freq" title="工作频率（MHz）" align="center" width="120">
              <template #default="{ row }">
                <span :class="{ 'text-red-500': isDiff(row, 'freq') }">{{ row.freq }}</span>
              </template>
            </vxe-column>
            <vxe-column field="bw" title="发射带宽（kHz）" align="center" width="120">
              <template #default="{ row }">
                <span :class="{ 'text-red-500': isDiff(row, 'bw') }">{{ row.bw }}</span>
              </template>
            </vxe-column>
            <vxe-column field="pw" title="发射功率（dBm）" align="center" width="120">
              <template #default="{ row }">
                <span :class="{ 'text-red-500': isDiff(row, 'pw') }">{{ row.pw }}</span>
              </template>
            </vxe-column>
          </vxe-table>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<style scoped lang="scss"></style>
