import { createApp } from 'vue'
import '@/assets/styles/tailwind.css'
import '@/assets/styles/theme/light-var.css'
import '@/assets/styles/theme/dark-var.css'
import '@/assets/styles/index.scss' // global css
import '@/assets/styles/var.scss'
import App from './App'
import store from './store'
import router from './router'
import { regComponents } from './components' //注册全局组件
import directive from './directive' // 注册指令
import useDictStore from '@/store/modules/dict'
import plugins from './plugins' // plugins
import './permission' // permission control
import VxeUIAll from 'vxe-pc-ui'
import 'vxe-pc-ui/lib/style.css'
import VxeUITable from 'vxe-table'
import 'vxe-table/lib/style.css'
import 'xe-utils'
import MessagePlugin from './plugins/message'
import GreenTheme from './constant/theme/28.js'

async function bootstrap() {
  const app = createApp(App)
  app.use(VxeUITable)
  // 注册主题(28suo主题样式)
  VxeUITable.use(GreenTheme)
  app.use(VxeUIAll)
  // 注册Message插件
  app.use(MessagePlugin)
  app.use(router)
  app.use(store)
  app.use(plugins)
  regComponents(app)
  directive(app)
  const dict = useDictStore()
  await dict.getAllDictList().catch(err => { }) // 获取所有字典
  app.mount('#app')
}

void bootstrap()
