import defaultSettings from '@/settings'
// import { useDynamicTitle } from '@/utils/dynamicTitle'
import { defineStore } from 'pinia'
import dark from '@/constant/theme/dark'
import light from '@/constant/theme/light'

const THEME_KEY = 'el-theme-appearance'
const mode = localStorage.getItem(THEME_KEY) || 'light'

const { sideTheme, showSettings, topNav, tagsView, fixedHeader, sidebarLogo, dynamicTitle } =
  defaultSettings

const storageSetting = JSON.parse(localStorage.getItem('layout-setting')) || ''

const useSettingsStore = defineStore('settings', {
  state: () => ({
    title: '',
    mode: mode,
    theme: storageSetting.theme || '#1c3052',
    sideTheme: storageSetting.sideTheme || sideTheme,
    showSettings: showSettings,
    topNav: storageSetting.topNav === undefined ? topNav : storageSetting.topNav,
    tagsView: storageSetting.tagsView === undefined ? tagsView : storageSetting.tagsView,
    fixedHeader:
      storageSetting.fixedHeader === undefined ? fixedHeader : storageSetting.fixedHeader,
    sidebarLogo:
      storageSetting.sidebarLogo === undefined ? sidebarLogo : storageSetting.sidebarLogo,
    dynamicTitle:
      storageSetting.dynamicTitle === undefined ? dynamicTitle : storageSetting.dynamicTitle
  }),
  getters: {
    themeStyle(state) {
      let currentMode = state.mode;
      if (currentMode === 'auto') {
        const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
        currentMode = prefersDark ? 'dark' : 'light';
      }
      return currentMode === 'dark' ? dark : light;
    },
  },
  actions: {
    // 修改布局设置
    changeSetting(data) {
      const { key, value } = data
      if (this.hasOwnProperty(key)) {
        this[key] = value
      }
    },
    // 设置网页标题
    setTitle(title) {
      this.title = title
      // useDynamicTitle()
    }
  }
})

export default useSettingsStore
