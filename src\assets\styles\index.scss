@import "./variables.module.scss";
@import "./mixin.scss";
@import "./element-ui.scss";
@import "./sidebar.scss";
@import "./vxeTable.scss";

body {
  width: 100%;
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Source Han Sans CN;
  color: #454545;
}
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

ul,
ol,
dl {
  list-style: none;
}
label {
  font-weight: 700;
}

html {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

#app {
  width: 100%;
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: border-box;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.dark .switch__action {
  transform: translate(20px) !important;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.pagination-container {
  margin-top: 16px;
  .el-pagination {
    justify-content: end;
  }
}

.text-center {
  text-align: center;
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(
    90deg,
    rgba(32, 182, 249, 1) 0%,
    rgba(32, 182, 249, 1) 0%,
    rgba(33, 120, 241, 1) 100%,
    rgba(33, 120, 241, 1) 100%
  );

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

//refine vue-multiselect plugin
.multiselect {
  line-height: 16px;
}

.multiselect--active {
  z-index: 1000 !important;
}

#app,
body,
html {
  height: 100%;
  overflow: hidden;
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: Microsoft YaHei;
  line-height: 1.5;
  color: var(--text-color);
  font-size: 14px;
  background-color: var(--el-bg-color);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  .vxe-table--render-default .vxe-table--body-wrapper,
  .vxe-table--render-default .vxe-table--footer-wrapper {
    background-color: var(--el-bg-color);
    color: var(--text-color);
  }
}

//重置样式
.anticon,
.el-icon {
  color: inherit;
  svg {
    display: inline-block;
    vertical-align: initial;
  }
}

a {
  color: #57a3f3;
  background: transparent;
  text-decoration: none;
  outline: none;
  cursor: pointer;
  transition: color 0.2s ease;
}

:focus-visible {
  outline: none;
}

a:active,
a:hover {
  outline-width: 0;
}

a:hover {
  color: #57a3f3;
}

a:active {
  color: #2b85e4;
}

a:active,
a:hover {
  outline: 0;
  text-decoration: none;
}

/* 滚动条凹槽的颜色，还可以设置边框属性 */
*::-webkit-scrollbar-track-piece {
  background-color: #f8f8f8;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

/* 滚动条的宽度 */
*::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

/* 滚动条的设置 */
*::-webkit-scrollbar-thumb {
  background-color: #d3eadd;
  background-clip: padding-box;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}

/* 滚动条鼠标上移 */
*::-webkit-scrollbar-thumb:hover {
  background-color: #44b283;
}

/* 滚动条鼠标激活 */
*::-webkit-scrollbar-thumb:active {
  background-color: #00603b;
}

.tags-view-wrapper::-webkit-scrollbar {
  display: none;
}

.tags-view-wrapper {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* router view transition */
.zoom-fade-enter-active,
.zoom-fade-leave-active {
  transition:
    transform 0.35s,
    opacity 0.28s ease-in-out;
}

.zoom-fade-enter-from {
  opacity: 0;
  transform: scale(0.97);
}

.zoom-fade-leave-to {
  opacity: 0;
  transform: scale(1.03);
}

.form-header {
  font-size: 17px;
  border-bottom: 1px solid var(--el-border-color-lighter);
  margin: 1em 0;
  font-weight: bold;
  padding-bottom: 0.5em;
  &:first-child {
    margin-top: 0;
  }
}

.page-container {
  width: 100%;
  border-radius: 4px;
  padding: 50px 0;
  height: 100vh;
  .text-center {
    h1 {
      color: #666;
      padding: 20px 0;
    }
  }
  img {
    width: 350px;
    margin: 0 auto;
  }
}
.el-drawer__header,
.ptitle {
  border-bottom: 1px solid var(--el-border-color);
  padding-bottom: 0.5em;
  font-size: 1rem;
  line-height: 1.6;
  margin-bottom: 1rem;
  font-weight: bold;
  overflow: hidden;
  &::before {
    width: 5px;
    margin-right: 10px;
    background-color: var(--el-color-primary);
    content: "";
    border-radius: 3px;
    display: inline-block;
    vertical-align: middle;
    height: 1.1em;
  }

  .action {
    float: right;
    .el-radio-group,
    .el-button {
      vertical-align: top;
    }
  }
}

.el-link + .el-link,
.el-link + .el-dropdown {
  margin-left: 0.5rem;
}
.el-table {
  --el-table-header-background-color: var(--el-table-header-background);
  border-top: 1px solid var(--el-border-color) !important;
  &.el-table--border {
    border-bottom: 1px solid var(--el-border-color) !important;
    border-top: none !important;
  }

  // th.el-table__cell {
  // color: var(--scan-text-color);
  // }

  .el-table__header-wrapper tr th.el-table-fixed-column--left,
  .el-table__header-wrapper tr th.el-table-fixed-column--right {
    background-color: var(--el-table-header-bg-color);
  }

  .el-input-number {
    width: auto;
  }
  .cell .el-form-item {
    margin-bottom: 0;
    &.is-error {
      margin-top: 1rem;
      margin-bottom: 1rem;
    }
  }
}

.hidden {
  display: none;
}
.input {
  width: 100%;
  height: 32px;
  border-radius: 4px;
  outline: 0;
  margin: 0 15px;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
  padding: 0 0.6em;
  color: #606266;
  border: 1px solid #dcdfe6;
  background-color: #fff;
  box-shadow: none;
}

/* 全局样式 */
@media (max-width: 1000px) {
  .fixed-width-container {
    width: 100%;
    height: auto;
    min-height: 900px;
  }
}
