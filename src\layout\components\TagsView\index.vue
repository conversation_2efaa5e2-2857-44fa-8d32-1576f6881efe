<template>
  <div class="tags-view-container">
    <el-scrollbar ref="scrollContainer" class="tags-view-wrapper" @scroll="handleScroll">
      <router-link
        v-for="tag in visitedViews"
        :key="tag.path"
        :data-path="tag.path"
        class="tags-view-item"
        :class="isActive(tag) ? 'active' : ''"
        :to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
        @contextmenu.prevent="openMenu(tag, $event)"
      >
        {{ tag.title }}
        <span v-if="!isAffix(tag)" @click.prevent.stop="closeSelectedTag(tag)">
          <close class="el-icon-close" />
        </span>
      </router-link>
    </el-scrollbar>
    <!-- 上下文菜单 -->
    <ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
      <li @click="refreshSelectedTag(selectedTag)">
        <refresh-right style="width: 1em; height: 1em" /> 刷新页面
      </li>
      <li v-if="!isAffix(selectedTag)" @click="closeSelectedTag(selectedTag)">
        <close style="width: 1em; height: 1em" /> 关闭当前
      </li>
      <li @click="closeOthersTags"> <circle-close style="width: 1em; height: 1em" /> 关闭其他 </li>
      <li v-if="!isFirstView()" @click="closeLeftTags">
        <back style="width: 1em; height: 1em" /> 关闭左侧
      </li>
      <li v-if="!isLastView()" @click="closeRightTags">
        <right style="width: 1em; height: 1em" /> 关闭右侧
      </li>
      <li @click="closeAllTags(selectedTag)">
        <circle-close style="width: 1em; height: 1em" /> 全部关闭
      </li>
    </ul>
  </div>
</template>

<script setup>
  import { ref, computed, watch, onMounted, nextTick, getCurrentInstance } from 'vue'
  import { useRoute, useRouter } from 'vue-router'
  import { getNormalPath } from '@/utils/utils'
  import useTagsViewStore from '@/store/modules/tagsView'
  import usePermissionStore from '@/store/modules/permission'

  const visible = ref(false)
  const top = ref(0)
  const left = ref(0)
  const selectedTag = ref({})
  const affixTags = ref([])
  const tagAndTagSpacing = ref(4)
  const scrollContainer = ref(null)
  const { proxy } = getCurrentInstance()
  const route = useRoute()
  const router = useRouter()
  const tagsViewStore = useTagsViewStore()
  const permissionStore = usePermissionStore()
  const visitedViews = tagsViewStore.visitedViews
  const routes = computed(() => permissionStore.routes)

  watch(
    () => route.path,
    async (val, oldVal) => {
      if (route.meta.noAddTag && oldVal.includes('analyse')) {
        const current = visitedViews.find(item => item.path === oldVal)
        tagsViewStore.updateView(route, current)
        return
      }
      addTags()
      moveToCurrentTag()
    }
  )

  watch(visible, value => {
    if (value) {
      document.body.addEventListener('click', closeMenu)
    } else {
      document.body.removeEventListener('click', closeMenu)
    }
  })

  const moveToTarget = currentTag => {
    nextTick(() => {
      const container = scrollContainer.value?.$el
      const containerWidth = container.offsetWidth
      const scrollLeft = container.scrollLeft
      const scrollWidth = container.scrollWidth
      const tagElements = container.querySelectorAll('.tags-view-item')
      const currentIndex = visitedViews.findIndex(item => item === currentTag)
      const currentTagElement = tagElements[currentIndex]

      if (currentTagElement) {
        const tagLeft = currentTagElement.offsetLeft
        const tagRight = tagLeft + currentTagElement.offsetWidth

        if (tagLeft < scrollLeft) {
          container.scrollLeft = tagLeft - tagAndTagSpacing.value
        } else if (tagRight > scrollLeft + containerWidth) {
          container.scrollLeft = tagRight - containerWidth + tagAndTagSpacing.value
        }
        // 将新标签滚动到可视区域
        currentTagElement.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'center' })
      }
    })
  }

  // 判断是否为当前激活的标签
  const isActive = r => r.path === route.path

  // 判断标签是否是固定标签（有 affix 属性）
  const isAffix = tag => tag.meta && tag.meta.affix

  // 判断是否是第一个视图
  const isFirstView = () => {
    try {
      return (
        selectedTag.value.fullPath === visitedViews[1]?.fullPath ||
        selectedTag.value.fullPath === '/index'
      )
    } catch (err) {
      return false
    }
  }

  // 判断是否是最后一个视图
  const isLastView = () => {
    try {
      return selectedTag.value.fullPath === visitedViews[visitedViews.length - 1]?.fullPath
    } catch (err) {
      return false
    }
  }

  // 过滤出固定标签（有 affix 属性的标签）
  const filterAffixTags = (routes, basePath = '') => {
    let tags = []
    routes.forEach(route => {
      if (route.meta && route.meta.affix) {
        const tagPath = getNormalPath(basePath + '/' + route.path)
        tags.push({
          fullPath: tagPath,
          path: tagPath,
          name: route.name,
          meta: { ...route.meta }
        })
      }
      if (route.children) {
        const tempTags = filterAffixTags(route.children, route.path)
        if (tempTags.length >= 1) {
          tags = [...tags, ...tempTags]
        }
      }
    })
    return tags
  }

  // 初始化标签，添加固定标签到相关存储中
  const initTags = () => {
    const res = filterAffixTags(routes.value)
    affixTags.value = res
    for (const tag of res) {
      if (tag.name) {
        tagsViewStore.addVisitedView(tag)
      }
    }
  }

  // 添加标签到相关存储中，根据路由情况添加普通视图或 iframe 视图
  const addTags = () => {
    const { name } = route
    if (name) {
      tagsViewStore.addView(route)
      if (route.meta.link) {
        tagsViewStore.addIframeView(route)
      }
    }
    return false
  }

  // 移动到当前标签对应的位置，并根据情况更新视图
  const moveToCurrentTag = () => {
    nextTick(() => {
      for (const r of visitedViews) {
        if (r.path === route.path) {
          moveToTarget(r)
          // when query is different then update
          if (r.fullPath !== route.fullPath) {
            tagsViewStore.updateVisitedView(route)
          }
        }
      }
    })
  }

  // 刷新选中的标签对应的页面，并处理 iframe 视图相关逻辑
  const refreshSelectedTag = view => {
    proxy.$tab.refreshPage(view)
    if (route.meta.link) {
      tagsViewStore.delIframeView(route)
    }
  }

  // 关闭选中的标签，并根据情况跳转到最后一个视图
  const closeSelectedTag = view => {
    proxy.$tab.closePage(view).then(({ visitedViews }) => {
      if (isActive(view)) {
        toLastView(visitedViews, view)
      }
    })
  }

  // 关闭右侧的标签，并根据情况跳转到最后一个视图
  const closeRightTags = () => {
    proxy.$tab.closeRightPage(selectedTag.value).then(visitedViews => {
      if (!visitedViews.find(i => i.fullPath === route.fullPath)) {
        toLastView(visitedViews)
      }
    })
  }

  // 关闭左侧的标签，并根据情况跳转到最后一个视图
  const closeLeftTags = () => {
    proxy.$tab.closeLeftPage(selectedTag.value).then(visitedViews => {
      if (!visitedViews.find(i => i.fullPath === route.fullPath)) {
        toLastView(visitedViews)
      }
    })
  }

  // 关闭其他标签，只保留当前选中的标签，并移动到当前标签
  const closeOthersTags = () => {
    router.push(selectedTag.value).catch(() => {})
    proxy.$tab.closeOtherPage(selectedTag.value).then(() => {
      moveToCurrentTag()
    })
  }

  // 关闭所有标签，根据情况跳转到最后一个视图或特定页面
  const closeAllTags = view => {
    proxy.$tab.closeAllPage().then(({ visitedViews }) => {
      if (affixTags.value.some(tag => tag.path === route.path)) {
        return
      }
      toLastView(visitedViews, view)
    })
  }

  // 根据提供的已访问视图列表和当前视图，跳转到最后一个视图或者特定页面
  const toLastView = (visitedViews, view) => {
    const latestView = visitedViews.slice(-1)[0]
    if (latestView) {
      router.push(latestView.fullPath)
    } else {
      if (view.name === 'Dashboard') {
        router.replace({ path: '/redirect' + view.fullPath })
      } else {
        router.push('/')
      }
    }
  }

  // 打开右键菜单，根据鼠标点击位置计算菜单显示位置，并设置相关状态
  const openMenu = (tag, e) => {
    e.preventDefault() // 防止默认右键菜单
    const menuMinWidth = 105
    const menuHeight = 200 // 估算菜单高度，可以根据实际情况调整

    const clickX = e.pageX
    const clickY = e.pageY

    const windowWidth = window.innerWidth
    const windowHeight = window.innerHeight

    let calculatedLeft = clickX
    let calculatedTop = clickY
    // 检查右侧是否超出窗口
    if (clickX + menuMinWidth > windowWidth) {
      calculatedLeft = windowWidth - menuMinWidth - 10 // 10px 的边距
    }
    // 检查底部是否超出窗口
    if (clickY + menuHeight > windowHeight) {
      calculatedTop = windowHeight - menuHeight - 10 // 10px 的边距
    }

    left.value = calculatedLeft
    top.value = calculatedTop
    visible.value = true
    selectedTag.value = tag
  }

  // 关闭右键菜单，设置菜单可见状态为 false
  const closeMenu = () => {
    visible.value = false
  }

  // 处理滚动事件，关闭右键菜单
  const handleScroll = () => {
    closeMenu()
  }
  // 鼠标滚轮事件处理函数
  const handleWheel = event => {
    event.preventDefault()
    const wrap = scrollContainer.value.$el.querySelector('.el-scrollbar__wrap')
    if (wrap) {
      wrap.scrollLeft += event.deltaY
    }
  }

  onMounted(() => {
    // 添加滚轮事件监听
    const container = scrollContainer.value?.$el
    if (container) {
      container.addEventListener('wheel', handleWheel, { passive: false })
    }
    initTags()
    addTags()
  })

  onBeforeUnmount(() => {
    // 移除滚轮事件监听
    const container = scrollContainer.value?.$el
    if (container) {
      container.removeEventListener('wheel', handleWheel)
    }
  })
</script>

<style lang="scss" scoped>
  .tags-view-container {
    height: var(--nav-bar-height);
    width: 100%;
    display: flex;
    align-items: center;
    position: relative;
    scroll-behavior: smooth;

    .tags-view-wrapper {
      flex: 1;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      flex-wrap: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      padding: 0 10px;

      :deep(.el-scrollbar__wrap::-webkit-scrollbar) {
        display: none;
      }

      :deep(.el-scrollbar__wrap) {
        scrollbar-width: none;
        -ms-overflow-style: none; /* IE 和 Edge */
      }

      .tags-view-item {
        display: inline-flex;
        align-items: center;
        position: relative;
        cursor: pointer;
        height: var(--nav-bar-height);
        font-size: 14px;
        height: var(--nav-bar-height);
        color: var(--el-bg-color);
        padding: 0 23px;
        white-space: nowrap;
        flex: 0 0 auto;
        // &:first-of-type {
        //   margin-left: 20px;
        // }
        &:last-of-type {
          margin-right: 15px;
        }
        &.active {
          color: var(--el-bg-color);
          background-color: #436cf2;
          &::before {
            background: #fff;
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            position: relative;
            margin-right: 2px;
          }
        }
        .el-icon-close {
          width: 16px;
          height: 16px;
          vertical-align: middle;
          border-radius: 50%;
          text-align: center;
          transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
          transform-origin: 100% 50%;
          &:before {
            transform: scale(0.6);
            display: inline-block;
            vertical-align: -3px;
          }
          &:hover {
            background-color: #eee;
          }
        }
      }
    }

    .contextmenu {
      margin: 0;
      background: #fff;
      z-index: 3000;
      position: fixed; /* 修改为 fixed */
      list-style-type: none;
      padding: 5px 0;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 400;
      color: #333;
      box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
      li {
        margin: 0;
        padding: 7px 16px;
        cursor: pointer;
        display: flex;
        align-items: center;
        &:hover {
          background: #eee;
        }
        svg {
          display: inline-block;
          margin-right: 8px; /* 增加图标与文字的间距 */
        }
      }
    }
  }
</style>
