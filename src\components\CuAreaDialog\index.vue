<script setup>
  import { ElMessage } from 'element-plus'

  const props = defineProps({
    usageAreaEntity: {
      type: Object,
      default: () => ({})
    }
  })
  const emit = defineEmits(['confirm'])

  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const activeName = ref('circle')
  const validConfig = reactive({
    theme: 'normal'
  })
  // 表单数据
  const formData = reactive({
    circle: {
      longitude: '',
      latitude: '',
      radius: ''
    },
    polygon: {
      longitude: '',
      latitude: ''
    }
  })

  // 验证规则
  const rules = {
    circle: {
      longitude: [
        { required: true, message: '请输入圆心经度', trigger: 'blur' },
        { pattern: /^-?\d+(\.\d+)?$/, message: '请输入有效数字', trigger: 'blur' }
      ],
      latitude: [
        { required: true, message: '请输入圆心纬度', trigger: 'blur' },
        { pattern: /^-?\d+(\.\d+)?$/, message: '请输入有效数字', trigger: 'blur' }
      ],
      radius: [
        { required: true, message: '请输入半径', trigger: 'blur' },
        {
          validator({ itemValue }) {
            if (itemValue <= 0) {
              return new Error('半径必须大于0')
            } else {
              return true
            }
          }
        }
      ]
    },
    polygon: {
      longitude: [
        { required: true, message: '请输入经度', trigger: 'blur' },
        { pattern: /^-?\d+(\.\d+)?$/, message: '请输入有效数字', trigger: 'blur' }
      ],
      latitude: [
        { required: true, message: '请输入纬度', trigger: 'blur' },
        { pattern: /^-?\d+(\.\d+)?$/, message: '请输入有效数字', trigger: 'blur' }
      ]
    }
  }

  const circleRef = ref(null)
  const polygonRef = ref(null)

  // 多边形点列表
  const polygonPoints = ref([])

  // 添加多边形点
  const addPolygonPoint = async () => {
    try {
      const valid = await polygonRef.value.validate()
      if (valid) return
      polygonPoints.value.push({
        longitude: formData.polygon.longitude,
        latitude: formData.polygon.latitude
      })
      formData.polygon.longitude = ''
      formData.polygon.latitude = ''
    } catch (error) {
      console.log('验证失败', error)
    }
  }

  // 删除多边形点
  const removePolygonPoint = index => {
    polygonPoints.value.splice(index, 1)
  }

  const confirm = async () => {
    try {
      if (activeName.value === 'circle') {
        const errMaps = await circleRef.value.validate()
        if (errMaps) return
        emit('confirm', {
          type: 'circle',
          data: {
            longitude: formData.circle.longitude,
            latitude: formData.circle.latitude,
            radius: formData.circle.radius
          }
        })
      } else {
        if (polygonPoints.value.length < 3) {
          throw new Error('多边形区域至少需要3个点')
        }
        emit('confirm', {
          type: 'polygon',
          data: toRaw(polygonPoints.value)
        })
      }
      dialogTableVisible.value = false
    } catch (error) {
      ElMessage.error(error.message)
    }
  }

  const cancel = () => {
    dialogTableVisible.value = false
  }

  onMounted(() => {
    if (props.usageAreaEntity?.type === 1) {
      formData.circle.longitude = props.usageAreaEntity.longitude
      formData.circle.latitude = props.usageAreaEntity.latitude
      formData.circle.radius = props.usageAreaEntity.radius
      activeName.value = 'circle'
    } else if (props.usageAreaEntity?.type === 2) {
      polygonPoints.value = props.usageAreaEntity.geoPointList
      activeName.value = 'polygon'
    }
  })

  // 切换标签时重置表单
  watch(activeName, newTab => {
    if (newTab === 'circle') {
      polygonPoints.value = []
      formData.polygon.longitude = ''
      formData.polygon.latitude = ''
      polygonRef.value?.clearValidate()
    } else {
      formData.circle.longitude = ''
      formData.circle.latitude = ''
      formData.circle.radius = ''
      circleRef.value?.clearValidate()
    }
  })
</script>

<template>
  <cu-dialog
    v-model="dialogTableVisible"
    title="保护区域设置"
    width="560px"
    @confirm="confirm"
    @cancel="cancel"
  >
    <el-tabs v-model="activeName" type="border-card" class="region-tabs">
      <!-- 圆形区域 -->
      <el-tab-pane label="圆形区域" name="circle">
        <vxe-form
          ref="circleRef"
          :data="formData.circle"
          :rules="rules.circle"
          :valid-config="validConfig"
          title-align="right"
          title-width="180"
          title-colon
        >
          <vxe-form-item field="longitude" span="24" title="圆心经度（西半球负）">
            <vxe-input
              v-model="formData.circle.longitude"
              type="float"
              placeholder="例如：116.404"
              clearable
            />
          </vxe-form-item>
          <vxe-form-item field="latitude" span="24" title="圆心纬度（南半球负）">
            <vxe-input
              v-model="formData.circle.latitude"
              type="float"
              placeholder="例如：39.915"
              clearable
            />
          </vxe-form-item>
          <vxe-form-item field="radius" span="24" title="半径（千米）">
            <vxe-input
              v-model="formData.circle.radius"
              type="float"
              placeholder="请输入大于0的数值"
              clearable
            />
          </vxe-form-item>
        </vxe-form>
      </el-tab-pane>

      <!-- 多边形区域 -->
      <el-tab-pane label="多边形区域" name="polygon">
        <div class="polygon-form">
          <vxe-form
            ref="polygonRef"
            :data="formData.polygon"
            :rules="rules.polygon"
            title-width="80"
            title-colon
            class="point-form"
            :valid-config="validConfig"
          >
            <vxe-form-item field="longitude" title="经度" span="24">
              <vxe-input
                v-model="formData.polygon.longitude"
                type="float"
                placeholder="例如：116.404"
                clearable
              />
            </vxe-form-item>
            <vxe-form-item field="latitude" title="纬度" span="24">
              <div class="flex">
                <vxe-input
                  v-model="formData.polygon.latitude"
                  type="float"
                  placeholder="例如：39.915"
                  clearable
                />
                <cu-button content="添加点" @click="addPolygonPoint"> </cu-button>
              </div>
            </vxe-form-item>
          </vxe-form>

          <div class="point-list">
            <vxe-table :data="polygonPoints" border stripe max-height="240" show-overflow>
              <vxe-column type="seq" title="序号" width="60" align="center" />
              <vxe-column field="longitude" title="经度" width="140" align="center" />
              <vxe-column field="latitude" title="纬度" width="140" align="center" />
              <vxe-column title="操作" align="center" fixed="right">
                <template #default="{ rowIndex }">
                  <cu-button content="删除" type="info" @click="removePolygonPoint(rowIndex)">
                  </cu-button>
                </template>
              </vxe-column>
            </vxe-table>
            <div v-if="polygonPoints.length < 3" class="point-tip">
              提示：至少需要添加3个点才能构成多边形
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 图选区域 -->
      <el-tab-pane label="图选区域" name="map"> 132132 </el-tab-pane>
    </el-tabs>
  </cu-dialog>
</template>

<style scoped lang="scss">
  .region-tabs {
    :deep(.el-tabs__nav) {
      display: flex;
      width: 100%;
    }

    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      padding: 0 16px;
    }
  }

  .polygon-form {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .point-form {
    margin-bottom: 12px;
  }

  .point-list {
    position: relative;

    .point-tip {
      position: absolute;
      bottom: 8px;
      left: 0;
      width: 100%;
      text-align: center;
      color: var(--el-color-warning);
      font-size: 12px;
      padding: 4px 0;
      background-color: rgba(255, 255, 255, 0.8);
    }
  }

  .vxe-form {
    margin-top: 12px;
  }

  .vxe-button {
    margin-left: 8px;
  }
</style>
