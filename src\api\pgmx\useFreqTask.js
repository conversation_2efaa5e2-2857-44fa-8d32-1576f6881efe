import request from '@/utils/request'

/** 查询用频计划 */
export function queryAllList(data) {
  return request({
    url: '/pgmx/useFreqTask/queryList',
    method: 'post',
    data: data
  })
}

/** 查询用频计划下拉框 */
export function queryTaskOption(data) {
  return request({
    url: '/pgmx/useFreqTask/queryTaskOption',
    method: 'post',
    data: data
  })
}

/** 新增用频计划 */
export function addUFTask(data) {
  return request({
    url: '/pgmx/useFreqTask/add',
    method: 'post',
    data: data
  })
}

/** 更新用频计划 */
export function editUFTask(data) {
  return request({
    url: '/pgmx/useFreqTask/update',
    method: 'post',
    data: data
  })
}


/** 根据ID查询用频计划详情 */
export function getTaskById(query) {
  return request({
    url: '/pgmx/useFreqTask/queryDetail',
    method: 'get',
    params: query
  })
}

/** 删除用频假计划 */
export function deleteTask(data) {
  return request({
    url: '/pgmx/useFreqTask/batchDel',
    method: 'delete',
    data: data
  })
}




