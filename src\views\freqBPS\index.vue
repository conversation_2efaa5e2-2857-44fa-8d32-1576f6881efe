<!-- FrequencyPlanningPage.vue -->
<script setup>
  import SpecChart from '@/components/SpecChart/index.vue'
  import regionPlanCom from './components/regionPlanCom.vue'

  // 1. 任务下拉相关
  const taskId = ref('option1')
  const taskOptions = ref([{ value: 'option1', label: '电子突防演练3号任务' }])

  // 2. 控制图表是否渲染
  const showChart = ref(false)

  // 3. 共用的参数
  const categories = ref(['设备A', '设备B', '设备C', '设备D', '设备E'])
  const timeCategories = ref(['10:00', '11:00', '12:00', '13:00', '14:00', '15:00'])
  const xAxisMin = ref(30e6)
  const xAxisMax = ref(18e9)

  const FBChartRanges = reactive({
    available: [
      [],
      [],
      [
        [30e6, 3e9],
        [12e9, 13e9]
      ],
      [],
      []
    ],
    disabled: [[], [], [[3e9, 5e9]], [], []],
    conflict: [[], [], [[5e9, 6e9]], [], []],
    reserved: [[], [], [[6e9, 7e9]], [], []],
    protected: [[], [], [[7e9, 11e9]], [], []]
  })

  const EQRanges = reactive({
    available: [[[30e6, 3e9]], [[4e9, 5e9]], [[12e9, 13e9]], [[5e9, 6e9]], [[6e9, 7e9]]],
    disabled: [],
    conflict: [],
    reserved: [],
    protected: []
  })

  // 协同
  const CollaborationRanges = reactive({
    available: [
      [],
      [],
      [
        [30e6, 3e9],
        [12e9, 13e9]
      ],
      [],
      []
    ],
    disabled: [[], [], [[3e9, 5e9]], [], []],
    conflict: [[], [], [[5e9, 6e9]], [], []],
    reserved: [[], [], [[6e9, 7e9]], [], []],
    protected: [[], [], [[7e9, 11e9]], [], []]
  })

  const padFBChartRanges = computed(() => {
    const len = categories.value.length
    return {
      available: pad(FBChartRanges.available, len),
      disabled: pad(FBChartRanges.disabled, len),
      conflict: pad(FBChartRanges.conflict, len),
      reserved: pad(FBChartRanges.reserved, len),
      protected: pad(FBChartRanges.protected, len)
    }
  })

  const padCollaborationRanges = computed(() => {
    const len = categories.value.length
    return {
      available: pad(CollaborationRanges.available, len),
      disabled: pad(CollaborationRanges.disabled, len),
      conflict: pad(CollaborationRanges.conflict, len),
      reserved: pad(CollaborationRanges.reserved, len),
      protected: pad(CollaborationRanges.protected, len)
    }
  })

  const padEQRanges = computed(() => {
    const len = EQRanges.available.length
    return {
      available: pad(EQRanges.available, len),
      disabled: pad(EQRanges.disabled, len),
      conflict: pad(EQRanges.conflict, len),
      reserved: pad(EQRanges.reserved, len),
      protected: pad(EQRanges.protected, len)
    }
  })

  // 4. 原始后端给的、多分类的频段数组 (Array<Array<[s,e]>>)
  const rawRanges = reactive({
    available: [
      [[30e6, 1000e6]],
      [[1200e6, 3200e6]],
      [[3400e6, 5400e6]],
      [[5600e6, 7600e6]],
      [[7800e6, 9800e6]]
    ],
    disabled: [
      [[1000e6, 1200e6]], // 这里如果少于 5 个要自动 pad
      [[5400e6, 5600e6]],
      [[9800e6, 10000e6]]
    ],
    conflict: [[[3200e6, 3400e6]], [[7600e6, 7800e6]]],
    reserved: [[[10000e6, 12000e6]], [[15000e6, 18000e6]]],
    protected: [[[12000e6, 15000e6]]]
  })

  // 5. pad：保证每个数组长度等于 categories.length
  function pad(arr, len) {
    const a = arr.slice()
    while (a.length < len) a.push([])
    return a
  }

  // 6. 计算出「已经 pad 好」的 ranges
  const paddedRanges = computed(() => {
    const len = categories.value.length
    return {
      available: pad(rawRanges.available, len),
      disabled: pad(rawRanges.disabled, len),
      conflict: pad(rawRanges.conflict, len),
      reserved: pad(rawRanges.reserved, len),
      protected: pad(rawRanges.protected, len)
    }
  })

  // 7. 定义要渲染的图列表
  const chartList = computed(() => [
    {
      id: 'spectrum',
      title: '频谱频段规划统计',
      categories: categories.value,
      ranges: padFBChartRanges.value,
      showYAxisLabel: false
    },
    {
      id: 'equipment',
      title: '装备用频规划统计',
      categories: categories.value,
      ranges: padEQRanges.value,
      showLegend: false
    },
    {
      id: 'timeUse',
      title: '用频时间规划统计',
      categories: timeCategories.value,
      ranges: paddedRanges.value
    },
    {
      id: 'coordination',
      title: '协同频谱频段统计',
      categories: categories.value,
      ranges: padCollaborationRanges.value
    }
  ])

  // 8. 点击“确定”时：模拟拉数据，然后显示图表
  function confirmTask() {
    // TODO: 真正调用接口写 rawRanges.xxx = resp.xxx
    showChart.value = true
  }

  // 9. 页面挂载时可选预拉一次
  onMounted(() => {
    // confirmTask()
  })
</script>

<template>
  <div class="flex items-center mb-4">
    <div class="w-[80px] flex items-center">ZZ任务</div>
    <vxe-select v-model="taskId" style="width: 300px">
      <vxe-option
        v-for="opt in taskOptions"
        :key="opt.value"
        :value="opt.value"
        :label="opt.label"
      />
    </vxe-select>
    <cu-button class="ml-5" type="primary" content="确定" @click="confirmTask" />
  </div>

  <!-- 只要 showChart=false，下面整个区域都不挂载 -->
  <div v-if="showChart">
    <template v-for="chart in chartList" :key="chart.id">
      <cu-button :content="chart.title" class="mb-2" />

      <SpecChart
        :categories="chart.categories"
        :available-ranges="chart.ranges.available"
        :disabled-ranges="chart.ranges.disabled"
        :conflict-ranges="chart.ranges.conflict"
        :reserved-ranges="chart.ranges.reserved"
        :protected-ranges="chart.ranges.protected"
        :x-axis-min="xAxisMin"
        :x-axis-max="xAxisMax"
        :legend-data="['可用频率', '禁用频率', '冲突频率', '预留频率', '保护频率']"
        :show-legend="chart.showLegend"
        :show-YAxis-label="chart.showYAxisLabel"
      />
    </template>
    <regionPlanCom />
  </div>
  <!-- 空状态 -->
  <div v-else class="h-[770px] flex items-center justify-center text-center text-gray-500">
    请选择任务并点击"确定"按钮生成图表
  </div>
</template>
