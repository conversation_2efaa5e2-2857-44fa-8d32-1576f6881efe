import request from '@/utils/request'

/** 可用频段分析 */
export function queryAvailableFreqAnalyze(data) {
  return request({
    url: '/pgmx-emeMonitor/queryAvailableFreqAnalyze',
    method: 'post',
    data: data
  })
}


/** 电磁环境分析 */
export function queryEmeAnalyze(data) {
  return request({
    url: '/pgmx-emeMonitor/queryEmeAnalyze',
    method: 'post',
    data: data
  })
}

/** 监测信号查询-by 监测任务id */
export function queryMonitorSignal(data) {
  return request({
    url: '/pgmx-emeMonitor/queryMonitorSignal',
    method: 'post',
    data: data
  })
}

/** 监测任务查询 */
export function queryMonitorTask(data) {
  return request({
    url: '/pgmx-emeMonitor/queryMonitorTask',
    method: 'post',
    data: data
  })
}









