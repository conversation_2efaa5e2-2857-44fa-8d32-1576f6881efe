<script setup>
  // 在这里编写逻辑
  import Highcharts from 'highcharts'

  const props = defineProps({
    seriesData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    }
  })

  const data = reactive({
    xAxisData: ['电信传输设施', '无线通信设施', '交换设施', '终端设备'],
    seriesData: [863, 807, 138, 295]
  })

  onMounted(() => {
    const chart = Highcharts.chart('container', {
      chart: {
        type: 'column',
        margin: [50, 50, 100, 80],
        options3d: {
          enabled: true,
          alpha: 0,
          beta: 0,
          depth: 20,
          viewDistance: 25
        }
      },
      title: {
        text: '通信设施类型统计'
      },
      xAxis: {
        categories: props.xAxisData,
        title: {
          text: '通信设施类型'
        },
        labels: {
          rotation: 0,
          align: 'center',
          style: {
            fontSize: '13px',
            fontFamily: 'Verdana, sans-serif'
          }
        }
      },
      yAxis: {
        title: {
          text: '通信设施数量'
        },
        tickInterval: 100, // 间隔
        min: 0
      },
      tooltip: {
        headerFormat: '<b>{point.key}</b><br>',
        pointFormat: '装备数量: {point.y}'
      },
      plotOptions: {
        column: {
          depth: 25,
          dataLabels: {
            enabled: true
          }
        }
      },
      credits: {
        //版权
        enabled: false
      },
      legend: {
        enabled: false
      },
      accessibility: {
        enabled: false
      },
      series: [
        {
          name: '装备数量',
          data: props.seriesData
        }
      ]
    })

    // 窗口大小变化时重新调整图表大小
    window.addEventListener('resize', () => chart.reflow())
  })
</script>

<template>
  <div id="container"> </div>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
