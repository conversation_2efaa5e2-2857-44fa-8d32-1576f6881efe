<template>
  <el-table border :data="signList">
    <el-table-column label="序号" type="index" width="60" align="center" />
    <el-table-column label="信号频率（MHz）" prop="signalRate" width="200" align="center" />
    <el-table-column label="信号带宽（kHz）" prop="bandwidth" align="center" />
    <el-table-column label="示向度个数" prop="dtyNum" align="center" />
    <el-table-column label="示向度" prop="dty" align="center" width="400" />
    <el-table-column label="俯仰角" prop="pitchAngle" align="center" />
    <el-table-column label="测向质量" prop="dtyQuality" align="center" />
    <el-table-column label="信号场强(dBμV/m)" prop="fieldStrength" align="center" />
    <el-table-column label="操作" width="180" align="center">
      <template #default="{ row }">
        <el-button class="mt-2" size="small" @click="analyse(row)">分析</el-button>
        <el-button class="mt-2" size="small" @click="findDirection(row)">测向</el-button>
      </template>
    </el-table-column>
  </el-table>
</template>

<script setup>
  const props = defineProps({
    signList: {
      type: Array,
      default: () => []
    }
  })
  const analyse = () => {}
  const findDirection = () => {}
  const putStorage = () => {}
</script>
