{"name": "kycloud", "version": "1.0.0", "description": "信息环境分析平台", "private": "true", "scripts": {"dev": "vite", "build": "node ./build/script/recordBuildTime.js && vite build && esno ./build/script/postBuild.ts ", "build:stage": "vite build --mode staging", "lint": "eslint . --ext .vue,.js,.ts,.jsx,.tsx --fix", "preview": "vite preview"}, "dependencies": {"@antv/l7": "^2.15.5", "@antv/l7-leaflet": "^0.0.3", "@antv/l7-maps": "^2.15.5", "@element-plus/icons-vue": "^2.3.1", "@vicons/antd": "^0.12.0", "@vicons/ionicons5": "^0.12.0", "@vueup/vue-quill": "^1.0.0", "@vueuse/core": "8.5.0", "axios": "^1.3.5", "cesium": "^1.122.0", "china-area-data": "^5.0.1", "colormap": "^2.3.2", "crypto-js": "^4.1.1", "cubic-spline": "^3.0.3", "dayjs": "^1.11.7", "decimal.js": "^10.5.0", "echarts": "5.3.2", "element-plus": "^2.9.5", "elui-china-area-dht": "^2.0.0", "file-saver": "2.0.5", "fuse.js": "6.5.3", "geocoder": "^0.2.3", "highcharts": "^11.2.0", "jquery": "^3.7.0", "js-cookie": "3.0.1", "jsencrypt": "3.2.1", "lamejs": "^1.2.1", "lodash": "^4.17.21", "nprogress": "0.2.0", "ol": "^10.2.1", "ol-cesium": "^2.17.0", "ol-ext": "^4.0.23", "ol-geocoder": "^4.3.3", "olcs": "^2.22.0", "pinia": "2.0.14", "pinia-plugin-persist": "^1.0.0", "print-js": "^1.6.0", "proj4": "^2.11.0", "spark-md5": "^3.0.2", "tailwindcss": "^3.4.13", "uuid": "^10.0.0", "v-selectpage": "^3.0.1", "vue": "^3.4.27", "vue-cropper": "1.0.3", "vue-router": "^4.1.6", "vue-upload-component": "^3.1.17", "vxe-pc-ui": "4.6.9", "vxe-table": "4.13.29", "webuploader": "^0.1.8", "xe-utils": "^3.7.4", "xlsx": "^0.18.5", "yarn": "^1.22.22"}, "devDependencies": {"@rollup/plugin-inject": "^4.0.4", "@types/lodash": "^4.14.177", "@types/node": "^15.14.9", "@types/yargs": "^17.0.17", "@typescript-eslint/eslint-plugin": "^5.59.5", "@typescript-eslint/parser": "^5.59.5", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.0", "@vue/compiler-sfc": "3.2.36", "@vue/eslint-config-standard": "^7.0.0", "@vue/eslint-config-typescript": "^10.0.0", "autoprefixer": "^10.4.12", "dotenv": "^16.0.3", "eslint": "^8.41.0", "eslint-config-standard": "^15.0.0", "eslint-plugin-vue": "^8.7.1", "esno": "^0.16.3", "less": "^4.1.3", "less-loader": "^11.1.0", "mockjs": "^1.1.0", "postcss": "^8.4.18", "sass": "^1.83.0", "typescript": "^5.0.4", "unplugin-auto-import": "0.8.5", "unplugin-vue-define-options": "^1.3.5", "vite": "^4.5.5", "vite-plugin-cesium": "^1.2.23", "vite-plugin-compression": "0.5.1", "vite-plugin-eslint": "^1.8.1", "vite-plugin-html": "^2", "vite-plugin-lazy-import": "^1.0.7", "vite-plugin-mock": "^3.0.0", "vite-plugin-style-import": "^1.3.0", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-top-level-await": "^1.2.1", "vite-plugin-vue-setup-extend": "0.4.0", "vue-eslint-parser": "^7.11.0", "yargs": "^17.6.2"}}