import request from '@/utils/request'

/** 调度规划 */
export function dispatchPlan(data) {
  return request({
    url: '/pgmx/use-freq/dispatchPlan',
    method: 'post',
    data: data
  })
}

/** 调度规划分页结果查询 */
export function dispatchPlanResult(data) {
  return request({
    url: '/pgmx/use-freq/dispatchPlanResult',
    method: 'post',
    data: data
  })
}


/** 用频信息列表-分页查询 */
export function queryPlanUseFreq(data) {
  return request({
    url: '/pgmx/use-freq/queryPlanUseFreq',
    method: 'get',
    params: data
  })
}

/** 保存方案 */
export function saveFreqPlan(data) {
  return request({
    url: '/pgmx/use-freq/savePlan',
    method: 'post',
    data: data
  })
}

/** 保存调度规划-装备用频计划 */
export function updateEquipFreqUse(data) {
  return request({
    url: '/pgmx/use-freq/updateEquipFreqUse',
    method: 'post',
    data: data
  })
}


/** 查询用频计划详情 */
export function queryPlanUseFreqEquip(data) {
  return request({
    url: '/pgmx/use-freq/queryPlanUseFreqEquip',
    method: 'get',
    params: data
  })
}

/** 用频方案下拉选项 */
export function queryPlanOption(data) {
  return request({
    url: '/pgmx/use-freq/queryPlanOption',
    method: 'get',
    params: data
  })
}

/** 删除调度规划-用频信息 */
export function deleteEquipFreqUse(data) {
  return request({
    url: '/pgmx/use-freq/deleteEquipFreqUse',
    method: 'delete',
    data: data
  })
}

