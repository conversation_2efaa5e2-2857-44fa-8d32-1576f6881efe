<template>
  <el-scrollbar
    ref="scrollContainer"
    :horizontal="true"
    :vertical="false"
    class="scroll-container"
    @scroll="emitScroll"
  >
    <slot />
  </el-scrollbar>
</template>

<script setup>
  import {
    ref,
    computed,
    onMounted,
    onBeforeUnmount,
    getCurrentInstance,
    defineEmits,
    nextTick
  } from 'vue'
  import useTagsViewStore from '@/store/modules/tagsView'

  const emitScroll = defineEmits(['scroll'])

  const tagAndTagSpacing = ref(4)
  const { proxy } = getCurrentInstance()

  const scrollContainer = ref(null)

  onMounted(() => {
    // 如果需要，可以在这里添加额外的事件监听
  })

  onBeforeUnmount(() => {
    // 清理事件监听（如果有）
  })

  const tagsViewStore = useTagsViewStore()
  const visitedViews = computed(() => tagsViewStore.visitedViews)

  function moveToTarget(currentTag) {
    nextTick(() => {
      const container = scrollContainer.value?.$el
      const containerWidth = container.offsetWidth
      const scrollLeft = container.scrollLeft
      const scrollWidth = container.scrollWidth

      const tagElements = container.querySelectorAll('.tags-view-item')
      const currentIndex = visitedViews.value.findIndex(item => item === currentTag)
      const currentTagElement = tagElements[currentIndex]

      if (currentTagElement) {
        const tagLeft = currentTagElement.offsetLeft
        const tagRight = tagLeft + currentTagElement.offsetWidth

        if (tagLeft < scrollLeft) {
          container.scrollLeft = tagLeft - tagAndTagSpacing.value
        } else if (tagRight > scrollLeft + containerWidth) {
          container.scrollLeft = tagRight - containerWidth + tagAndTagSpacing.value
        }
      }
    })
  }

  defineExpose({
    moveToTarget
  })
</script>

<style lang="scss" scoped>
  .scroll-container {
    white-space: nowrap;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;

    /* 自定义滚动条样式（可选） */
    ::-webkit-scrollbar {
      height: 6px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 3px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
  }
</style>
