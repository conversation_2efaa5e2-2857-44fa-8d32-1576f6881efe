// export const transmitterFormConfig = [
//   {
//     title: '发射机名称',
//     field: 'name',
//     span: 12,
//     type: 'input',
//     rules: [
//       { required: true, message: '请输入发射机名称' },
//       { min: 2, max: 10, message: '长度在 2 到 10 个字符' }
//     ]
//   },
//   {
//     title: '发射机编码',
//     field: 'code',
//     span: 12,
//     type: 'input',
//     rules: [
//       { required: true, message: '请输入发射机编码' },
//       { min: 2, max: 10, message: '长度在 2 到 10 个字符' }
//     ]
//   },
//   {
//     title: '频率上限(MHz)',
//     field: 'freqCap',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '频率下限(MHz)',
//     field: 'freqLow',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '平均功率(dBm)',
//     field: 'avgPower',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '峰值功率(dBm)',
//     field: 'peakPower',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '频率可调性',
//     field: 'freqAdjust',
//     span: 12,
//     type: 'select'
//   },
//   {
//     title: '工作频点数量',
//     field: 'workFreqNum',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '信号间隔(MHz)',
//     field: 'signalInterval',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '信道数量',
//     field: 'channelNum',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '最低信道中心频率(MHz)',
//     field: 'lowestChannelFreq',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '相邻干工作频点间隔(MHz)',
//     field: 'adjacentWorkFreqInterval',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '调制方式',
//     field: 'modulationMode',
//     span: 12,
//     type: 'select'
//   },
//   {
//     title: '调制类型',
//     field: 'modulationType',
//     span: 12,
//     type: 'select'
//   },
//   {
//     title: '最高传输速率(kbit/s)',
//     field: 'maxTransmissionRate',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '杂散谐波电平(dB)',
//     field: 'miscHarmonicLevel',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '占用带宽(MHz)',
//     field: 'occupiedBandwidth',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '3db带宽(MHz)',
//     field: '3dbBandwidth',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '20db带宽(MHz)',
//     field: '20dbBandwidth',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '40db带宽(MHz)',
//     field: '40dbBandwidth',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '60db带宽(MHz)',
//     field: '60dbBandwidth',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '功率可调性',
//     field: 'powerAdjust',
//     span: 12,
//     type: 'select'
//   },
//   {
//     title: '二次谐波电平(dB)',
//     field: 'secondHarmonicLevel',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '三次谐波电平(dB)',
//     field: 'thirdHarmonicLevel',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '跳频频点数量',
//     field: 'jumpPointNum',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '跳频带宽(MHz)',
//     field: 'jumpBandwidth',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '跳频速率(次数/s)',
//     field: 'jumpRate',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '最小跳频间隔',
//     field: 'minJumpInterval',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '跳频增益(dB)',
//     field: 'jumpGain',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '捷变频率数量',
//     field: 'agileFreqNum',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '捷变速率(次数/s)',
//     field: 'agileRate',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '捷变带宽(MHz)',
//     field: 'agileBandwidth',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '最小捷变间隔(MHz)',
//     field: 'minAgileInterval',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '脉冲宽度(MHz)',
//     field: 'pulseWidth',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '上升时间(μs)',
//     field: 'riseTime',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '下降时间(μs)',
//     field: 'fallTime',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '脉冲重复频率(MHz)',
//     field: 'pulseRepeatFreq',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '脉冲压缩比',
//     field: 'pulseCompressionRatio',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
//   {
//     title: '距离分辨力(m)',
//     field: 'distanceResolution',
//     span: 12,
//       itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
//   },
// ]

export const transmitterFormConfig = [
  {
    title: '发射机名称',
    field: 'name',
    span: 12,
    itemRender: { name: 'VxeInput' },
  },
  {
    title: '发射机编码',
    field: 'code',
    span: 12,
    itemRender: { name: 'VxeInput' },
  },
  {
    title: '频率上限(MHz)',
    field: 'freqLowerLimit',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '频率下限(MHz)',
    field: 'freqUpperLimit',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '平均功率(dBm)',
    field: 'avgPower',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '峰值功率(dBm)',
    field: 'peakPower',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '频率可调性',
    field: 'freqAdjustability',
    span: 12,
    itemRender: {
      name: 'VxeSelect', options: [{ label: '是', value: true }, { label: '否', value: false }]
    },
  },
  {
    title: '工作频点数量',
    field: 'workingFreqPointNum',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '信号间隔(MHz)',
    field: 'signalInterval',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '信道数量',
    field: 'channelNum',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '最低信道中心频率(MHz)',
    field: 'minChannelCenterFreq',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '相邻工作频点间隔(MHz)',
    field: 'adjacentFreqPointInterval',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '调制方式',
    field: 'modulationMode',
    span: 12,
    itemRender: {
      name: 'VxeSelect', options: [{ label: '是', value: true }, { label: '否', value: false }]
    },
  },
  {
    title: '调制类型',
    field: 'modulationType',
    span: 12,
    itemRender: {
      name: 'VxeSelect', options: [{ label: '是', value: true }, { label: '否', value: false }]
    },
  },
  {
    title: '最高传输速率(kbit/s)',
    field: 'maxTransmissionRate',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '杂散谐波电平(dB)',
    field: 'spuriousHarmonicLevel',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '占用带宽(MHz)',
    field: 'occupiedBandwidth',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '3db带宽(MHz)',
    field: 'bandwidth3db',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '20db带宽(MHz)',
    field: 'bandwidth20db',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '40db带宽(MHz)',
    field: 'bandwidth40db',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '60db带宽(MHz)',
    field: 'bandwidth60db',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '功率可调性',
    field: 'powerAdjustability',
    span: 12,
    itemRender: {
      name: 'VxeSelect', options: [{ label: '是', value: true }, { label: '否', value: false }]
    },
  },
  {
    title: '二次谐波电平(dB)',
    field: 'secondHarmonicLevel',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '三次谐波电平(dB)',
    field: 'thirdHarmonicLevel',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '跳频频点数量',
    field: 'fhPointNum',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '跳频带宽(MHz)',
    field: 'fhBandwidth',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '跳频速率(次数/s)',
    field: 'fhRate',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '最小跳频间隔',
    field: 'minFhInterval',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '跳频增益(dB)',
    field: 'fhGain',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '捷变频率数量',
    field: 'jtPointNum',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '捷变速率(次数/s)',
    field: 'jtRate',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '捷变带宽(MHz)',
    field: 'jtBandwidth',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '最小捷变间隔(MHz)',
    field: 'minJtInterval',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '脉冲宽度(MHz)',
    field: 'pulseWidth',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '上升时间(μs)',
    field: 'riseTime',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '下降时间(μs)',
    field: 'fallTime',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '脉冲重复频率(MHz)',
    field: 'pulseRepetitionFreq',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '脉冲压缩比',
    field: 'pulseCompressionRatio',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '距离分辨力(m)',
    field: 'rangeResolution',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
]
