<template>
  <div id="map" class="map mt-2"></div>
</template>

<script setup>
  import { onMounted, onBeforeUnmount } from 'vue'
  import Map from 'ol/Map'
  import XYZ from 'ol/source/XYZ'
  import View from 'ol/View'
  import TileLayer from 'ol/layer/Tile'
  import VectorLayer from 'ol/layer/Vector'
  import gcj02Mecator from '@/utils/gcj02Mecator.js'
  import VectorSource from 'ol/source/Vector'
  import Feature from 'ol/Feature'
  import { Circle as CircleGeom, Polygon } from 'ol/geom'
  import { fromLonLat } from 'ol/proj'
  import { Stroke, Fill, Style } from 'ol/style'
  import FullScreen from 'ol/control/FullScreen' // 导入全屏控件
  // —— 模拟数据 ——
  // type: "circle" | "regular" | "diamond" | "triangle" | "polygon"
  // center: [lng, lat]；radius 单位米；points 仅对 regular 有效；coords 对 polygon/triangle/diamond 有效
  const shapeData = [
    {
      type: 'circle',
      center: [116.58, 39.9],
      radius: 15000,
      style: new Style({
        stroke: new Stroke({ color: 'rgba(0,0,255,0.6)', width: 2 }),
        fill: new Fill({ color: 'rgba(0,0,255,0.2)' })
      })
    },
    {
      type: 'regular',
      center: [116.1, 39.92],
      radius: 12000,
      points: 5,
      style: new Style({
        stroke: new Stroke({ color: 'rgba(255,165,0,0.8)', width: 2 }),
        fill: new Fill({ color: 'rgba(255,165,0,0.3)' })
      })
    },
    {
      type: 'diamond',
      center: [116.22, 39.9],
      width: 20000,
      height: 10000,
      style: new Style({
        stroke: new Stroke({ color: 'rgba(0,128,0,0.8)', width: 2 }),
        fill: new Fill({ color: 'rgba(0,128,0,0.3)' })
      })
    },
    {
      type: 'triangle',
      coords: [
        [116.36, 39.88],
        [116.38, 39.84],
        [116.42, 39.87]
      ],
      style: new Style({
        stroke: new Stroke({ color: 'rgba(128,0,128,0.8)', width: 2 }),
        fill: new Fill({ color: 'rgba(128,0,128,0.3)' })
      })
    },
    {
      type: 'polygon',
      coords: [
        [116.34, 39.92],
        [116.36, 39.94],
        [116.38, 39.92],
        [116.37, 39.9],
        [116.35, 39.9]
      ],
      style: new Style({
        stroke: new Stroke({ color: 'rgba(255,0,0,0.8)', width: 2 }),
        fill: new Fill({ color: 'rgba(255,0,0,0.3)' })
      })
    }
  ]

  let map = null

  onMounted(() => {
    // 1. 瓦片图层（以 WebMercator 为例）
    const tileLayer = new TileLayer({
      source: new XYZ({
        // 如果要用高德，需要 EPSG:3857 的 URL；这里演示用 OpenStreetMap
        url: 'http://wprd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&style=7&x={x}&y={y}&z={z}'
      })
    })

    // 2. 矢量图层
    const vectorSource = new VectorSource()
    const vectorLayer = new VectorLayer({ source: vectorSource })

    shapeData.forEach(item => {
      let geom
      if (item.type === 'circle') {
        // circle → ol/geom/Circle
        const c = fromLonLat(item.center)
        geom = new CircleGeom(c, item.radius)
      } else if (item.type === 'regular') {
        const [lng, lat] = item.center
        // 先把中心投影到 WebMercator
        const [cx, cy] = fromLonLat([lng, lat])

        const n = item.points || 6 // 边数
        const r = item.radius // 半径，单位米
        const coords = []

        for (let i = 0; i < n; i++) {
          const theta = (2 * Math.PI * i) / n
          coords.push([cx + Math.cos(theta) * r, cy + Math.sin(theta) * r])
        }
        // 闭合最后一环
        coords.push(coords[0])

        geom = new Polygon([coords])
      } else if (item.type === 'diamond') {
        // 菱形：手动计算四个顶点
        const c = fromLonLat(item.center)
        const [cx, cy] = c
        const w2 = item.width / 2
        const h2 = item.height / 2
        const coords = [
          [cx, cy - h2],
          [cx + w2, cy],
          [cx, cy + h2],
          [cx - w2, cy],
          [cx, cy - h2]
        ]
        geom = new Polygon([coords])
      } else if (item.type === 'triangle') {
        // 三角形：给定经纬度 coords → 投影后用 Polygon
        const pts = item.coords.map(p => fromLonLat(p))
        geom = new Polygon([[...pts, pts[0]]])
      } else if (item.type === 'polygon') {
        // 任意多边形
        const pts = item.coords.map(p => fromLonLat(p))
        geom = new Polygon([[...pts, pts[0]]])
      }

      if (geom) {
        const feat = new Feature(geom)
        feat.setStyle(item.style)
        vectorSource.addFeature(feat)
      }
    })
    // 这里直接示例一个圆：
    // const c = fromLonLat([116.38, 39.9])
    // const circleFeat = new Feature(new CircleGeom(c, 15000))
    // circleFeat.setStyle(
    //   new Style({
    //     stroke: new Stroke({ color: 'blue', width: 2 }),
    //     fill: new Fill({ color: 'rgba(0,0,255,0.2)' })
    //   })
    // )
    // vectorSource.addFeature(circleFeat)
    // 其余的 regular/diamond/triangle/polygon 同理…

    // 3. 创建 Map，一定要把两层都放进 layers 数组
    const map = new Map({
      target: 'map',
      layers: [tileLayer, vectorLayer],
      view: new View({
        // **这里用与瓦片同样的 WebMercator 投影 (EPSG:3857)**
        projection: 'EPSG:3857',
        center: fromLonLat([116.38, 39.9]),
        zoom: 11
      }),
      controls: [new FullScreen()]
    })
  })

  onBeforeUnmount(() => {
    if (map) {
      map.setTarget(null)
      map = null
    }
  })
</script>

<style scoped>
  .map {
    width: 100%;
    height: 500px;
  }
</style>
