<script setup>
  import Cookies from 'js-cookie'
  import useDictStore from '@/store/modules/dict'
  import FileUpload from 'vue-upload-component'
  //分片上传
  import { upload } from '@/api/tool/request'
  import { uploadServerFile, getFileProgress } from '@/api/tool/request/upload.js'
  import { fileChunkList, setFileChunkList, clearFileHash } from '@/api/tool/request/file'
  import { checkFileExist } from '@/api/singalManage'
  import { plotToNum } from '@/utils/utils'
  import { ElMessage } from 'element-plus'
  const props = defineProps({
    // 遮罩层显示/隐藏
    message: {
      type: Boolean
    },
    // 调制样式枚举列表
    debugmodemessage: {
      type: Array
    }
  })
  let { message } = toRefs(props)

  const formData = ref({
    visible: 'false',
    fileType: '0', //数据类型
    centerFreqIn: '100MHz', //中心频率
    intermediateFrequencyBandwidth: '100MHz', //中心带宽
    samplerateIn: '125MHz', //采样率
    startOffLength: '0', //起始字节
    CutoffLength: '0', //末尾字节
    IQpermutation: '1', //IQ排列
    debugMode: '0', //调制样式
    dataType: '1', //数据类型
    IQfile: '', //IQ文件名称
    powerMultiple: '1', //功率倍数，
    unitType: '1', //单位类型，
    steplen: '25KHz', //步长
    fileExtractFlag: false, //文件提取
    sameUnitFlag: false, //单位类型
    sameUnit: 'MHz', //相同单位类型
    startAndEndFlag: false, //使用起止频率
    centerFreqPreFix: '', //中心频率前缀
    centerFreqUnit: 'MHz', //中心频率单位
    intermediateFrequencyBandwidthPreFix: '', //中心带宽前缀
    intermediateFrequencyBandwidthUnit: 'MHz', //中心带宽单位
    startFreqPreFix: '', //起始频率前缀
    startFreqUnit: 'MHz', //起始频率单位
    endFreqPreFix: '', //终止频率前缀
    endFreqUnit: 'MHz', //终止频率单位
    sampleRatePreFix: '', //采样率前缀
    sampleRateUnit: 'MHz', //采样率单位
    stepLenPreFix: '', //步长前缀
    stepLenUnit: 'MHz', //步长单位
    delimiter: '-', //分隔符
    serveSideFile: '', //服务端文件
    frameLength: '8192' //帧长度
  })

  const uploadParams = ref({}) //上传文件参数
  const filetypeList = ref([]) //文件类型
  const dataOrganizationList = ref([]) //IQ数据组织方式
  const datatypeList = ref([]) //IQ数据类型
  const debugModeList = ref([]) //调制样式
  const unitTypeList = ref([]) //单位类型
  const fatherValue = ref(false)
  const title = ref('上传文件')
  const width = ref(680)
  const height = ref(830)
  const currentFile = ref({
    index: 0,
    name: ''
  })
  const singlePercentage = ref(0) //文件上传进度
  const progressModal = ref(false) //进度条
  const emit = defineEmits(['childcLick'])
  const units = ['GHz', 'MHz', 'kHz', 'Hz']
  const commonType = reactive(['0', '1']) //数据类型
  const mode = ref('file') // 文件上传类型 'file' 或 'folder'
  const files = ref([]) // 上传文件列表
  const uploadRef = ref(null) // 文件上传组件引用
  const folderFlag = ref(false) // 是否是文件夹上传
  const subfolderFlag = ref(false) // 是否是子文件夹上传
  const fileSource = ref('local') // 文件来源
  const chunkProgress = ref([]) // 保存每个分片的进度
  const serverProgressModal = ref(false)
  const serverProgress = ref(0)
  let currentFileSize = 0 // 当前文件的总大小
  let currentUploaded = 0 // 当前文件的已上传字节数

  /** * 切换文件夹标志并更新模式 */
  const folderFlagChange = () => {
    folderFlag.value ? (mode.value = 'folder') : (mode.value = 'file')
    subfolderFlag.value = false
  }

  /**
   * 重置文件资源变更
   * 当文件资源发生变化时，该函数将被调用以重置表单数据中的文件信息。
   */
  const fileResourceChange = () => {
    formData.value.IQfile = ''
    formData.value.serveSideFile = ''
  }

  /**
   * 过滤嵌套文件夹中的文件
   * @param newFile 新文件对象
   * @param oldFile 旧文件对象
   * @param prevent 阻止默认操作的函数
   */
  const filterNestedFolders = (newFile, oldFile, prevent) => {
    const segments = newFile.name.split(/[/\\]/) // 处理 Windows 和 Unix 路径分隔符
    const fileName = segments[segments.length - 1]
    if (fileName.startsWith('.')) {
      console.log(`忽略隐藏文件: ${newFile.name}`)
      prevent()
      return
    }
    formData.value.IQfile = segments[0]
    // 如果大于二则表示有子文件夹
    if (segments.length > 2 && !subfolderFlag.value) {
      console.log(`忽略子文件夹的文件: ${newFile.name}`)
      prevent()
      return
    }
    console.log(`添加文件: ${newFile.name}`)
  }

  // 手动触发上传
  const triggerUpload = () => {
    // 清空文件列表
    files.value = [] // 重置文件列表
    const input = uploadRef.value.$refs.input // 获取到 <input> 元素
    if (input) {
      input.click() // 手动触发点击事件
    }
  }

  /**
   * *校验文件路径是否正确
   */
  const pathVerification = async () => {
    await checkFileExist({ filePath: formData.value.serveSideFile }).then(res => {
      if (res.code === 200) {
        ElMessage.success(res.msg)
      } else {
        ElMessage.error(res.msg)
      }
    })
  }

  /** * 清空文件内容 */
  const clearFile = () => {
    formData.value.IQfile = ''
    formData.value.serveSideFile = ''
  }

  /** * 关闭模态框 */
  const closeModal = () => {
    emit('childcLick', false)
    clearFileHash(false, files.value[files.value.length - 1], uploadParams.value)
  }

  /**
   * 处理文件类型变更的函数
   * @param data 变更的数据
   */
  const fileTypeChange = data => {
    resetFormData()
    formData.value.fileType = data.label
  }

  /**
   * 更改频率单位为相同单位
   * @param data 需要处理的数据对象
   */
  const sameUnitChange = () => {
    formData.value.centerFreqUnit =
      formData.value.intermediateFrequencyBandwidthUnit =
      formData.value.sampleRateUnit =
      formData.value.stepLenUnit =
      formData.value.startFreqUnit =
      formData.value.endFreqUnit =
        formData.value.sameUnit
  }

  /**
   * 处理文件提取变化
   * @param data 包含文件提取变化信息的对象
   */
  const fileExtractChange = data => {
    if (!data.value) {
      formData.value.startAndEndFlag = false
      formData.value.sameUnitFlag = false
    }
  }

  /**
   * 根据字典类型获取字典数据
   * @param dictType 字典类型
   * @param targetList 目标列表，用于存储转换后的数据
   */
  const getDictDataByType = (dictType, targetList) => {
    const store = useDictStore()
    const filteredItems = store.dict.filter(item => item.dictType === dictType)
    filteredItems.forEach(item => {
      targetList.value.push({ label: item.dictLabel, value: item.dictValue })
    })
  }

  /**
   * 将输入的数据转换为合适的频率单位
   * @param data 输入的数据
   * @returns 转换后的数据，包含单位
   */
  const interfaceUnitConversion = data => {
    if (/[a-zA-Z]/.test(data)) {
      return data
    } else {
      if (data / 1000 >= 0 && data / 1000 < 1000) {
        return data / 1000 + 'kHz'
      } else if (data / 1000000 > 0 && data / 1000000 < 1000) {
        return data / 1000000 + 'MHz'
      } else if (data / 1000000000 > 0) {
        return data / 1000000000 + 'GHz'
      }
      return data
    }
  }

  /**
   * 重置表单数据
   * 重置表单数据到初始状态，将所有字段设置为默认值或空值
   */
  const resetFormData = () => {
    formData.value = {
      visible: 'false',
      fileType: '0', //数据类型
      centerFreqIn: '100MHz', //中心频率
      intermediateFrequencyBandwidth: '100MHz', //中心带宽
      samplerateIn: '125MHz', //采样率
      startOffLength: '0', //起始字节
      CutoffLength: '0', //末尾字节
      IQpermutation: '1', //IQ排列
      debugMode: '0', //调制样式
      dataType: '1', //数据类型
      IQfile: '', //IQ文件名称
      powerMultiple: '1', //功率倍数，
      unitType: '1', //单位类型，
      steplen: '25KHz', //步长
      fileExtractFlag: false, //文件提取
      sameUnitFlag: false, //单位类型
      sameUnit: 'MHz', //相同单位类型
      startAndEndFlag: false, //使用起止频率
      centerFreqPreFix: '', //中心频率前缀
      centerFreqUnit: 'MHz', //中心频率单位
      intermediateFrequencyBandwidthPreFix: '', //中心带宽前缀
      intermediateFrequencyBandwidthUnit: 'MHz', //中心带宽单位
      startFreqPreFix: '', //起始频率前缀
      startFreqUnit: 'MHz', //起始频率单位
      endFreqPreFix: '', //终止频率前缀
      endFreqUnit: 'MHz', //终止频率单位
      sampleRatePreFix: '', //采样率前缀
      sampleRateUnit: 'MHz', //采样率单位
      stepLenPreFix: '', //步长前缀
      stepLenUnit: 'MHz', //步长单位
      delimiter: '-', //分隔符
      serveSideFile: '', //服务端文件
      frameLength: '8192'
    }
    singlePercentage.value = 0
    currentFile.value = { index: 0, name: '' }
    currentFileSize = 0
    currentUploaded = 0
    files.value = []
    uploadParams.value = {}
  }

  const insertEvent = async () => {
    // 校验文件来源和必要参数
    if (formData.value.fileExtractFlag) {
      const requiredFields = formData.value.startAndEndFlag
        ? ['startFreqPreFix', 'endFreqPreFix', 'sampleRatePreFix']
        : ['centerFreqPreFix', 'intermediateFrequencyBandwidthPreFix', 'sampleRatePreFix']

      if (requiredFields.some(field => !formData.value[field])) {
        return ElMessage.error('请输入必填前缀')
      }
    }
    if (fileSource.value === 'local' && !files.value.length) {
      return ElMessage.error('请选择文件/文件夹上传')
    }
    // 公用参数
    const commonParams = {
      fileType: formData.value.fileType,
      powerMultiple: formData.value.powerMultiple || 1,
      unitType: formData.value.unitType,
      centerfregInStr: interfaceUnitConversion(formData.value.centerFreqIn),
      bwInStr: interfaceUnitConversion(formData.value.intermediateFrequencyBandwidth),
      samplerateInStr: interfaceUnitConversion(formData.value.samplerateIn),
      steplen: plotToNum(formData.value.steplen),
      steplenStr: interfaceUnitConversion(formData.value.steplen),
      dataOrganization: formData.value.IQpermutation,
      debugMode: formData.value.debugMode,
      dataType: formData.value.dataType,
      startOffLength: formData.value.startOffLength,
      CutoffLength: formData.value.CutoffLength,
      coderateStr: '0KHz',
      createBy: Cookies.get('username'),
      frameLength: formData.value.frameLength
    }
    // 公用文件参数
    const fileParams = {
      fileExtractFlag: formData.value.fileExtractFlag,
      sameUnitFlag: formData.value.sameUnitFlag,
      sameUnit: formData.value.sameUnit,
      startAndEndFlag: formData.value.startAndEndFlag,
      centerFreqPreFix: formData.value.centerFreqPreFix,
      centerFreqUnit: formData.value.centerFreqUnit,
      intermediateFrequencyBandwidthPreFix: formData.value.intermediateFrequencyBandwidthPreFix,
      intermediateFrequencyBandwidthUnit: formData.value.intermediateFrequencyBandwidthUnit,
      startFreqPreFix: formData.value.startFreqPreFix,
      startFreqUnit: formData.value.startFreqUnit,
      endFreqPreFix: formData.value.endFreqPreFix,
      endFreqUnit: formData.value.endFreqUnit,
      sampleRatePreFix: formData.value.sampleRatePreFix,
      sampleRateUnit: formData.value.sampleRateUnit,
      stepLenPreFix: formData.value.stepLenPreFix,
      stepLenUnit: formData.value.stepLenUnit,
      delimiter: formData.value.delimiter
    }
    // 根据文件来源设置参数
    if (fileSource.value === 'local') {
      uploadParams.value = {
        ...commonParams,
        ...fileParams
      }
      uploadFolder(files.value, uploadParams.value)
    } else if (fileSource.value === 'server') {
      uploadParams.value = {
        ...commonParams,
        filePath: formData.value.serveSideFile,
        fileParam: {
          ...fileParams
        }
      }
      await uploadServerFile(uploadParams.value).then(async res => {
        if (res.code === 200) {
          const progressKey = res.data.progressKey
          serverProgressModal.value = true // 显示进度模态框
          await pollFileProgress(progressKey)
        }
      })
    } else {
      ElMessage.error('请选择文件来源')
    }
  }

  const pollFileProgress = async progressKey => {
    while (true) {
      try {
        const progressRes = await getFileProgress({ key: progressKey })
        if (progressRes.code === 200) {
          console.log('当前进度:', progressRes.data.progress)
          serverProgress.value = progressRes.data.progress
          if (progressRes.data.progress >= 100) {
            console.log('文件上传完成')
            // 延迟关闭模态框，确保用户看到 100% 进度
            await new Promise(resolve => setTimeout(resolve, 500)) // 延时 500 毫秒
            progressVisible.value = false // 隐藏进度模态框
            resetFormData() // 重置表单数据
            break
          }
        } else {
          ElMessage.error(progressRes.msg)
          console.error('获取进度失败:', progressRes.message)
          break // 中断轮询
        }
      } catch (error) {
        console.error('轮询进度时发生错误:', error)
        serverProgressModal.value = false // 隐藏进度模态框
        break
      }
    }
  }

  const uploadFolder = async () => {
    try {
      progressModal.value = true // 显示上传进度模态框
      console.log(files)
      // 遍历文件数组，逐个上传文件
      for (let i = 0; i < files.value.length; i++) {
        const fileObject = files.value[i]
        console.log(`正在上传文件: ${fileObject.name}`)
        // 初始化当前文件的进度
        currentFileSize = fileObject.file.size
        currentUploaded = 0
        chunkProgress.value.splice(0, chunkProgress.value.length) // 清空分块进度
        currentFile.value.index = i + 1 // 更新当前文件索引
        currentFile.value.name = fileObject.name // 更新当前文件名称
        singlePercentage.value = 0 // 重置单文件进度
        // 上传单个文件
        await uploadSingleFile(fileObject.file, uploadParams.value)
      }
      ElMessage.success('文件夹上传成功')
    } catch (error) {
      ElMessage.error(`上传失败: ${error.message}`)
    } finally {
      // 重置状态
      progressModal.value = false
      closeModal()
      resetFormData()
    }
  }

  // 单文件上传逻辑
  const uploadSingleFile = async (file, params) => {
    if (!file.size) {
      throw new Error('文件大小为 0，无法上传')
    }
    // 调用实际上传接口
    await upload(file, onUploadProgress, params)
  }

  // 上传进度回调函数
  const onUploadProgress = chunkIndex => e => {
    console.log(e, 'e')
    // 计算当前分块上传的增量
    const previousProgress = chunkProgress.value[chunkIndex] || 0 // 之前的分块进度
    const newProgress = e.loaded / currentFileSize // 当前分块的进度百分比
    // 更新已上传字节数
    currentUploaded += (newProgress - previousProgress) * currentFileSize
    // 更新分块进度
    chunkProgress.value[chunkIndex] = newProgress
    // 更新单文件进度百分比
    singlePercentage.value = Math.min(
      parseFloat(((currentUploaded / currentFileSize) * 100).toFixed(2)),
      100
    )
    console.log('singlePercentage', singlePercentage.value)
  }

  onMounted(() => {
    fatherValue.value = props.message
    // 获取字典数据
    getDictDataByType('filetype', filetypeList)
    getDictDataByType('data_organization', dataOrganizationList)
    getDictDataByType('data_type', datatypeList)
    getDictDataByType('spec_unit_type', unitTypeList)
  })

  watch(
    () => formData.value.fileType,
    (newVal, oldVal) => {
      if (newVal == 0) {
        formData.value.dataType = '1'
      } else {
        formData.value.dataType = '3'
      }
    }
  )

  //监测父子传值的变化
  watch(
    () => props,
    (newVal, oldVal) => {
      debugModeList.value = oldVal.debugmodemessage
      resetFormData()
      folderFlag.value = false
      subfolderFlag.value = false
    },
    { deep: true }
  )
</script>

<template>
  <div>
    <vxe-modal
      :model-value="message"
      :title="title"
      :min-height="700"
      :width="width"
      :height="commonType.includes(formData.fileType) ? height : '280'"
      position="center"
      @close="closeModal"
    >
      <template #default>
        <vxe-form :data="formData">
          <vxe-form-item title="数据类型" field="fileType" span="22">
            <template #default="{ data }">
              <vxe-radio
                v-for="item in filetypeList"
                v-model="data.fileType"
                name="fileType"
                :label="item.value"
                :content="item.label"
                @change="fileTypeChange"
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="commonType.includes(formData.fileType)"
            field="fileExtractFlag"
            :item-render="{}"
            span="22"
          >
            <template #default="{ data }">
              <div>
                <vxe-checkbox @change="fileExtractChange" v-model="data.fileExtractFlag"
                  >从文件名提取频率参数</vxe-checkbox
                >
              </div>
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="formData.fileExtractFlag && commonType.includes(formData.fileType)"
            field="sameUnitFlag"
            :disabled="formData.fileExtractFlag"
            span="22"
            :item-render="{}"
          >
            <template #default="{ data }">
              <div class="flex items-center">
                <vxe-checkbox class="mr-5" v-model="data.sameUnitFlag">
                  频率使用相同单位
                </vxe-checkbox>
                <select
                  v-model="data.sameUnit"
                  @change="sameUnitChange"
                  class="select-style"
                  style="width: 30%"
                  transfer
                  clearable
                >
                  <option v-for="(item, index) in units" :key="index" :value="item" :label="item" />
                </select>
              </div>
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="formData.fileExtractFlag && commonType.includes(formData.fileType)"
            field="startAndEndFlag"
            span="22"
            :item-render="{}"
          >
            <template #default="{ data }">
              <vxe-checkbox v-model="data.startAndEndFlag">使用起止频率</vxe-checkbox>
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="!formData.startAndEndFlag && commonType.includes(formData.fileType)"
            title="中心频率"
            field="centerFreqIn"
            :item-render="{}"
            span="22"
          >
            <template #default="{ data }">
              <div v-if="!data.fileExtractFlag">
                <input
                  v-model="data.centerFreqIn"
                  class="center-name"
                  clearable
                  maxlength="20"
                  @input="
                    data.centerFreqIn = data.centerFreqIn.replace(
                      /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                      ''
                    )
                  "
                />
              </div>
              <div v-else class="flex justify-around items-center">
                <div class="flex items-center">
                  <div class="mr-2">前缀</div>
                  <input
                    v-model="data.centerFreqPreFix"
                    placeholder="请输入前缀(如:cf)"
                    class="center-name"
                    style="width: 80%"
                    clearable
                    maxlength="20"
                  />
                </div>
                <div class="flex items-center">
                  <div class="w-20">单位</div>
                  <select
                    :disabled="data.sameUnitFlag"
                    v-model="data.centerFreqUnit"
                    class="select-style"
                    transfer
                    clearable
                  >
                    <option
                      v-for="(item, index) in units"
                      :key="index"
                      :value="item"
                      :label="item"
                    /> </select
                ></div>
              </div>
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="formData.startAndEndFlag && commonType.includes(formData.fileType)"
            field="startFreqPreFix"
            title="起始频率"
            :item-render="{}"
            span="22"
          >
            <template #default="{ data }">
              <div class="flex justify-around items-center">
                <div class="flex items-center">
                  <div class="mr-2">前缀</div>
                  <input
                    v-model="data.startFreqPreFix"
                    placeholder="请输入前缀(如:sf)"
                    class="center-name"
                    style="width: 80%"
                    clearable
                    maxlength="20"
                  />
                </div>
                <div class="flex items-center">
                  <div class="w-20">单位</div>
                  <select
                    :disabled="data.sameUnitFlag"
                    v-model="data.startFreqUnit"
                    class="select-style"
                    transfer
                    clearable
                  >
                    <option
                      v-for="(item, index) in units"
                      :key="index"
                      :value="item"
                      :label="item"
                    /> </select
                ></div>
              </div>
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="!formData.startAndEndFlag && commonType.includes(formData.fileType)"
            title="中频带宽"
            field="intermediateFrequencyBandwidth"
            :item-render="{}"
            span="22"
          >
            <template #default="{ data }">
              <div v-if="!data.fileExtractFlag"
                ><input
                  v-model="data.intermediateFrequencyBandwidth"
                  class="center-name"
                  maxlength="20"
                  @input="
                    data.intermediateFrequencyBandwidth =
                      data.intermediateFrequencyBandwidth.replace(
                        /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                        ''
                      )
                  "
              /></div>
              <div v-else class="flex justify-around items-center">
                <div class="flex items-center">
                  <div class="mr-2">前缀</div>
                  <input
                    v-model="data.intermediateFrequencyBandwidthPreFix"
                    placeholder="请输入前缀(如:bw)"
                    class="center-name"
                    style="width: 80%"
                    clearable
                    maxlength="20"
                  />
                </div>
                <div class="flex items-center">
                  <div class="w-20">单位</div>
                  <select
                    v-model="data.intermediateFrequencyBandwidthUnit"
                    :disabled="data.sameUnitFlag"
                    class="select-style"
                    transfer
                    clearable
                  >
                    <option
                      v-for="(item, index) in units"
                      :key="index"
                      :value="item"
                      :label="item"
                    /> </select
                ></div>
              </div>
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="formData.startAndEndFlag && commonType.includes(formData.fileType)"
            field="endFreqPreFix"
            title="终止频率"
            :item-render="{}"
            span="22"
          >
            <template #default="{ data }">
              <div class="flex justify-around items-center">
                <div class="flex items-center">
                  <div class="mr-2">前缀</div>
                  <input
                    v-model="data.endFreqPreFix"
                    placeholder="请输入前缀(如:ef)"
                    class="center-name"
                    style="width: 80%"
                    clearable
                    maxlength="20"
                  />
                </div>
                <div class="flex items-center">
                  <div class="w-20">单位</div>
                  <select
                    :disabled="data.sameUnitFlag"
                    v-model="data.endFreqUnit"
                    class="select-style"
                    transfer
                    clearable
                  >
                    <option
                      v-for="(item, index) in units"
                      :key="index"
                      :value="item"
                      :label="item"
                    /> </select
                ></div>
              </div>
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="commonType.includes(formData.fileType)"
            title="步长"
            field="steplen"
            :item-render="{}"
            span="22"
          >
            <template #default="{ data }">
              <div v-if="!data.fileExtractFlag"
                ><input
                  v-model="data.steplen"
                  class="center-name"
                  maxlength="20"
                  @input="
                    data.steplen = data.steplen.replace(
                      /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                      ''
                    )
                  "
              /></div>
              <div v-else class="flex justify-around items-center">
                <div class="flex items-center">
                  <div class="mr-2">前缀</div>
                  <input
                    v-model="data.stepLenPreFix"
                    placeholder="请输入前缀(如:sl)"
                    class="center-name"
                    style="width: 80%"
                    clearable
                    maxlength="20"
                  />
                </div>
                <div class="flex items-center">
                  <div class="w-20">单位</div>
                  <select
                    v-model="data.stepLenUnit"
                    :disabled="data.sameUnitFlag"
                    class="select-style"
                    transfer
                    clearable
                  >
                    <option
                      v-for="(item, index) in units"
                      :key="index"
                      :value="item"
                      :label="item"
                    /> </select
                ></div>
              </div>
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="commonType.includes(formData.fileType)"
            title="采样率"
            field="samplerateIn"
            :item-render="{}"
            span="22"
          >
            <template #default="{ data }">
              <div v-if="!data.fileExtractFlag"
                ><input
                  v-model="data.samplerateIn"
                  class="sampleRateIn-name"
                  maxlength="20"
                  @input="
                    data.samplerateIn = data.samplerateIn.replace(
                      /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                      ''
                    )
                  "
              /></div>
              <div v-else class="flex justify-around items-center">
                <div class="flex items-center">
                  <div class="mr-2">前缀</div>
                  <input
                    v-model="data.sampleRatePreFix"
                    placeholder="请输入前缀(如:sm)"
                    class="center-name"
                    style="width: 80%"
                    clearable
                    maxlength="20"
                  />
                </div>
                <div class="flex items-center">
                  <div class="w-20">单位</div>
                  <select
                    v-model="data.sampleRateUnit"
                    :disabled="data.sameUnitFlag"
                    class="select-style"
                    transfer
                    clearable
                  >
                    <option
                      v-for="(item, index) in units"
                      :key="index"
                      :value="item"
                      :label="item"
                    /> </select
                ></div>
              </div>
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="formData.fileExtractFlag && commonType.includes(formData.fileType)"
            title="分隔符"
            field="delimiter"
            :item-render="{}"
            span="22"
          >
            <template #default="{ data }">
              <input v-model="data.delimiter" class="center-name" clearable maxlength="20" />
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="formData.fileType === '1'"
            title="功率倍数"
            field="powerMultiple"
            :item-render="{}"
            span="22"
          >
            <template #default="{ data }">
              <input
                v-model="data.powerMultiple"
                class="sampleRateIn-name"
                maxlength="20"
                @input="
                  data.powerMultiple = data.powerMultiple.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="commonType.includes(formData.fileType)"
            :title="formData.fileType == 0 ? 'IQ数据起始字节' : '频谱数据起始字节'"
            field="startOffLength"
            :item-render="{}"
            span="22"
          >
            <template #default="{ data }">
              <input
                v-model="data.startOffLength"
                class="sampleRateIn-name"
                maxlength="20"
                @input="
                  data.startOffLength = data.startOffLength.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="commonType.includes(formData.fileType)"
            title="文件末尾截断字节数"
            field="CutoffLength"
            :item-render="{}"
            span="22"
          >
            <template #default="{ data }">
              <input
                v-model="data.CutoffLength"
                class="sampleRateIn-name"
                maxlength="20"
                @input="
                  data.CutoffLength = data.CutoffLength.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="commonType.includes(formData.fileType)"
            title="帧长度"
            field="frameLength"
            :item-render="{}"
            span="22"
          >
            <template #default="{ data }">
              <input
                v-model="data.frameLength"
                class="sampleRateIn-name"
                maxlength="20"
                @input="
                  data.frameLength = data.frameLength.replace(
                    /[^/0/1/2/3/4/5/6/7/8/9/./k/K/H/h/Z/z/M/m/G/g/]/gi,
                    ''
                  )
                "
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="commonType.includes(formData.fileType)"
            title="调制样式"
            field="debugMode"
            :item-render="{}"
            span="22"
            placeholder="请选择调制样式"
          >
            <template #default="{ data }">
              <!-- <vxe-select
                v-model="data.debugMode"
                type="text"
                :options="debugModeList"
                transfer
                clearable
              /> -->
              <select v-model="data.debugMode" class="select-style" transfer clearable>
                <option
                  v-for="(item, index) in debugModeList"
                  :key="index"
                  :value="item.value"
                  :label="item.label"
                />
              </select>
            </template>
          </vxe-form-item>

          <vxe-form-item
            :visible="formData.fileType === '0'"
            title="IQ排列"
            field="IQpermutation"
            span="22"
          >
            <template #default="{ data }">
              <vxe-radio
                v-for="item in dataOrganizationList"
                v-model="data.IQpermutation"
                name="IQpermutation"
                :label="item.value"
                :content="item.label"
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="formData.fileType === '1'"
            title="频谱数据单位"
            field="unitType"
            span="22"
          >
            <template #default="{ data }">
              <vxe-radio
                v-for="item in unitTypeList"
                v-model="data.unitType"
                name="unitType"
                :label="item.value"
                :content="item.label"
              />
            </template>
          </vxe-form-item>
          <vxe-form-item
            :visible="commonType.includes(formData.fileType)"
            title="数据类型"
            field="dataType"
            span="22"
          >
            <template #default="{ data }">
              <vxe-radio
                v-for="item in datatypeList"
                v-model="data.dataType"
                name="dataType"
                :label="item.value"
                :content="item.label"
              />
            </template>
          </vxe-form-item>
          <vxe-form-item :item-render="{}" span="22">
            <!-- 标题和单选框部分 -->
            <template #title>
              <div class="flex items-center">
                <vxe-radio-group v-model="fileSource" @change="fileResourceChange" class="flex">
                  <vxe-radio label="local">本地文件</vxe-radio>
                  <vxe-radio label="server">服务器端文件</vxe-radio>
                </vxe-radio-group>
              </div>
            </template>
            <!-- 内容部分 -->
            <template #default="{ data }">
              <!-- 本地文件内容 -->
              <div v-if="fileSource === 'local'" class="flex items-center space-x-2">
                <vxe-input
                  v-model="data.IQfile"
                  placeholder="请上传本地文件"
                  clearable
                  @clear="clearFile"
                  class="flex-1"
                />
                <vxe-button
                  status="success"
                  content="浏览"
                  @click="triggerUpload"
                  class="auto-button"
                ></vxe-button>
                <file-upload
                  ref="uploadRef"
                  v-model="files"
                  @input-filter="filterNestedFolders"
                  :directory="mode === 'folder'"
                  :multiple="true"
                  :post-action="null"
                  style="display: none"
                ></file-upload>
              </div>

              <!-- 服务器端文件内容 -->
              <div v-else-if="fileSource === 'server'" class="flex items-center space-x-2">
                <vxe-input
                  v-model="data.serveSideFile"
                  placeholder="请输入服务器端文件路径"
                  clearable
                  @clear="clearFile"
                  class="flex-1"
                />
                <vxe-button
                  status="success"
                  content="路径验证"
                  @click="pathVerification"
                  class="auto-button"
                ></vxe-button>
              </div>
            </template>
          </vxe-form-item>

          <vxe-form-item field="folderFlag" :item-render="{}" span="22">
            <vxe-checkbox @change="folderFlagChange" v-model="folderFlag">
              文件夹上传
            </vxe-checkbox>
            <vxe-checkbox :disabled="!folderFlag" v-model="subfolderFlag">
              遍历子文件夹
            </vxe-checkbox>
          </vxe-form-item>
        </vxe-form>
        <div class="flex justify-center items-center mt-3">
          <vxe-button status="info" content="取消" @click="closeModal" />
          <vxe-button status="primary" content="上传" @click="insertEvent(false)" />
        </div>
      </template>
    </vxe-modal>

    <!-- 本地文件上传进度 -->
    <vxe-modal
      :model-value="progressModal"
      title="本地文件上传进度"
      :min-height="120"
      :width="450"
      :height="260"
      position="center"
      @close="progressModal = false"
    >
      <div class="flex flex-col items-center justify-center w-full h-full p-4 rounded-md shadow">
        <!-- 进度条 -->
        <el-progress
          :percentage="singlePercentage"
          :stroke-width="18"
          striped
          striped-flow
          class="w-full"
        ></el-progress>

        <!-- 显示当前文件信息 -->
        <div class="w-full mt-4">
          <!-- 当前文件名称 -->
          <div class="w-full text-wrap break-words whitespace-normal text-lg font-medium">
            当前文件：{{ currentFile.name }}
          </div>
          <!-- 文件进度 -->
          <div class="mt-2 text-sm">
            文件进度：{{ currentFile.index }} / {{ files.length }} 个
          </div>
        </div>
      </div>
    </vxe-modal>

    <!-- 服务器端文件上传进度 -->
    <vxe-modal
      :model-value="serverProgressModal"
      title="服务器端文件上传进度"
      :width="450"
      :height="200"
      position="center"
      @close="serverProgressModal = false"
    >
      <div class="text-center mt-5">
        <el-progress
          :percentage="serverProgress"
          :stroke-width="18"
          striped
          striped-flow
          class="w-full"
        ></el-progress>
        <div class="mt-3">当前进度: {{ serverProgress }}%</div>
      </div>
    </vxe-modal>
  </div>
</template>

<style scoped>
  .browse-button {
    float: right;
    margin: -43px 30px;
  }

  .center-name {
    width: 100%;
    height: 32px;
    border-radius: 4px;
    outline: 0;
    margin: 0;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    padding: 0 0.6em;
    border: 1px solid var(--chart-text-color);
    color: var(--chart-text-color);
    background-color: var(--background-color);
    box-shadow: none;
  }
  .sampleRateIn-name {
    width: 95%;
    height: 32px;
    border-radius: 4px;
    outline: 0;
    margin: 0 15px;
    font-size: inherit;
    font-family: inherit;
    line-height: inherit;
    padding: 0 0.6em;
    border: 1px solid var(--chart-text-color);
    color: var(--chart-text-color);
    background-color: var(--background-color);
    box-shadow: none;
  }
  :deep(.vxe-modal--header) {
    color: var(--chart-text-color);
    background-color: var(--background-color);
  }
  :deep(.vxe-form--wrapper) {
    color: var(--chart-text-color);
    background-color: var(--background-color);
  }
  :deep(.vxe-modal--content) {
    color: var(--chart-text-color);
    background-color: var(--background-color);
  }

  .select-style {
    background-color: var(--background-color);
    border: 1px solid var(--chart-text-color);
    border-radius: 4px;
    height: 32px;
    padding: 4px;
    color: var(--chart-text-color);
    width: 100%;
    cursor: pointer;
  }
  :deep() .vxe-modal--header-title {
    text-align: center;
  }
  :deep() .vxe-form--item {
    margin: 0 30px;
  }

  .auto-button {
    display: inline-block;
    padding: 0.5rem 1rem; /* 根据按钮内容控制内边距 */
    font-size: 14px; /* 字体大小 */
    line-height: 1.5; /* 行高 */
    border-radius: 4px; /* 圆角 */
    white-space: nowrap; /* 防止按钮内容换行 */
    text-align: center; /* 居中对齐 */
  }

  :deep(.el-progress-bar__inner) {
    background-color: #0d84ff !important;
  }
</style>
