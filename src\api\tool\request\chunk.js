import SparkMD5 from 'spark-md5'
import { fileChunkList } from './file'

export const DefaultChunkSize = 50 * 1024 * 1024 //5M

/**
 * 将文件按块读取并计算文件的哈希值
 * @param file 文件对象
 * @param params 其他参数
 * @param chunkSize 分块大小，默认为 DefaultChunkSize
 * @returns 返回一个 Promise，当读取和计算完成后，Promise 将被解析为文件的哈希值
 */
export const getFileChunk = (file, params, chunkSize = DefaultChunkSize) => {
  console.log(file.size, '文件大小');
  console.log(params, 'chunkFile');
  return new Promise(resolve => {
    const blobSlice = File.prototype.slice || File.prototype.mozSlice || File.prototype.webkitSlice
    const chunks = Math.ceil(file.size / chunkSize) // 分了几块
    let currentChunk = 0,
      spark = new SparkMD5.ArrayBuffer(), // 用于生成文件哈希值
      // FileReader 对象允许Web应用程序异步读取存储在用户计算机上的文件（或原始数据缓冲区）的内容，
      // 使用 File 或 Blob 对象指定要读取的文件或数据。
      fileReader = new FileReader() // 问题，fileReader 性能问题，读的太慢了

    // 做一个闭包保持持续作用域
    fileReader.onload = e => {
      console.log('读到第', currentChunk + 1, '块')

      const chunk = e.target.result
      spark.append(chunk) // 将读取的块内容添加到 spark 中进行哈希计算
      currentChunk++

      // 如果还有未读的块，继续读取下一个
      if (currentChunk < chunks) {
        // 持续读取
        loadNext()
      } else {
        // 所有块读取完成，生成最终的文件哈希
        let fileHash = spark.end()
        console.info('结束生成hash', fileHash)
        resolve(fileHash)
      }
    }

    fileReader.onerror = err => {
      console.warn('something went wrong', err)
    }

    /** * 加载下一个文件块 */
    const loadNext = () => {
      /// 计算开始和结束位置
      const start = currentChunk * chunkSize
      const end = start + chunkSize >= file.size ? file.size : start + chunkSize
      let chunk = blobSlice.call(file, start, end)

      // 将块添加到 fileChunkList 中
      const len = fileChunkList.value.length
      fileChunkList.value.push({ chunk, size: chunk.size, name: file.name, num: len, ...params })

      // 使用 FileReader 读取块的内容
      fileReader.readAsArrayBuffer(chunk)
    }

    loadNext() // 启动分块读取
  })
}
