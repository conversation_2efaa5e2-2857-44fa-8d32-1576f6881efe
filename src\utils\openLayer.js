import { Point } from 'ol/geom'


// 贝塞尔曲线计算函数示例
export function getBezierCurve(points) {
  const bezierCoordinates = []
  if (points.length < 2) return bezierCoordinates
  // 添加起始点
  bezierCoordinates.push(points[0])
  for (let i = 0; i < points.length - 1; i++) {
    const p0 = i > 0 ? points[i - 1] : points[i]
    const p1 = points[i]
    const p2 = points[i + 1]
    const p3 = i + 2 < points.length ? points[i + 2] : p2

    // 计算控制点
    const cp1x = p1[0] + (p2[0] - p0[0]) / 6
    const cp1y = p1[1] + (p2[1] - p0[1]) / 6
    const cp2x = p2[0] - (p3[0] - p1[0]) / 6
    const cp2y = p2[1] - (p3[1] - p1[1]) / 6
    // 插值计算贝塞尔曲线中间点
    for (let t = 0; t <= 1; t += 0.05) {
      // t 是贝塞尔参数，0.05 表示取点的密度
      const x = bezier(cp1x, cp2x, p1[0], p2[0], t)
      const y = bezier(cp1y, cp2y, p1[1], p2[1], t)
      bezierCoordinates.push([x, y])
    }
  }
  // 添加结束点
  bezierCoordinates.push(points[points.length - 1])
  return bezierCoordinates
}

function bezier(c1, c2, p1, p2, t) {
  const t2 = t * t
  const t3 = t2 * t
  const mt = 1 - t
  const mt2 = mt * mt
  const mt3 = mt2 * mt
  return mt3 * p1 + 3 * mt2 * t * c1 + 3 * mt * t2 * c2 + t3 * p2
}



/**
 * 动画绘制线条，并可在地图上同步移动一个 marker
 * @param {Array} coordinates - 线条的坐标数组(投影后的坐标)，每个元素是 [x, y]
 * @param {LineString} geometry - 用于绘制的几何对象，必须包含 setCoordinates 方法
 * @param {Function} onComplete - 动画完成时的回调函数
 * @param {Number} duration - 动画持续时间，单位为毫秒，默认 1000
 * @param {Feature} [markerFeature] - 可选，用于显示移动图标的 Feature
 */
export function animateLine(
  coordinates,
  geometry,
  onComplete,
  duration = 1000,
  markerFeature
) {
  const total = coordinates.length
  let startTime = null

  function drawStep(timestamp) {
    if (!startTime) startTime = timestamp
    const elapsed = timestamp - startTime
    const progress = Math.min(elapsed / duration, 1)
    // 计算当前绘制到第几个点
    const currentIndex = Math.floor(progress * (total - 1))

    // 1) 更新折线的坐标
    geometry.setCoordinates(coordinates.slice(0, currentIndex + 1))

    // 2) 如果有 markerFeature，则让它跟随线的最后一个点
    if (markerFeature) {
      markerFeature.getGeometry().setCoordinates(coordinates[currentIndex])
    }

    if (progress < 1) {
      requestAnimationFrame(drawStep)
    } else {
      onComplete && onComplete()
    }
  }

  // 防止没坐标时出错
  if (coordinates.length === 0) {
    onComplete && onComplete()
    return
  }

  // 初始化第一个点，避免「闪烁」
  geometry.setCoordinates([coordinates[0]])
  if (markerFeature) {
    // 将 marker 也移动到第一个点
    markerFeature.setGeometry(new Point(coordinates[0]))
  }

  requestAnimationFrame(drawStep)
}

