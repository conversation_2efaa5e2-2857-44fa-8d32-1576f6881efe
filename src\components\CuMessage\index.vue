<template>
  <transition name="message-fade">
    <div
      v-show="visible"
      class="message"
      :class="[`message-${type}`, customClass]"
      :style="customStyle"
      @mouseenter="stopTimer"
      @mouseleave="startTimer"
    >
      <i class="message-icon" :class="iconClass"></i>
      <span class="message-content">{{ message }}</span>
      <button v-if="closable" class="message-close" @click="close">
        <i class="icon-close">×</i>
      </button>
    </div>
  </transition>
</template>

<script>
  export default {
    name: 'Message',
    props: {
      message: {
        type: String,
        default: ''
      },
      type: {
        type: String,
        default: 'info',
        validator: value => ['success', 'warning', 'info', 'error'].includes(value)
      },
      duration: {
        type: Number,
        default: 3000
      },
      customClass: {
        type: String,
        default: ''
      },
      customStyle: {
        type: Object,
        default: () => {}
      },
      closable: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {
        visible: false,
        timer: null
      }
    },
    computed: {
      iconClass() {
        const iconMap = {
          success: 'icon-success',
          warning: 'icon-warning',
          info: 'icon-info',
          error: 'icon-error'
        }
        return iconMap[this.type] || 'icon-info'
      }
    },
    mounted() {
      this.startTimer()
      this.visible = true
    },
    beforeUnmount() {
      this.clearTimer()
    },
    methods: {
      startTimer() {
        if (this.duration > 0) {
          this.clearTimer()
          this.timer = setTimeout(() => {
            this.close()
          }, this.duration)
        }
      },
      clearTimer() {
        if (this.timer) {
          clearTimeout(this.timer)
          this.timer = null
        }
      },
      close() {
        this.visible = false
        this.$emit('close')
        setTimeout(() => {
          this.$el.parentNode.removeChild(this.$el)
        }, 300)
      }
    }
  }
</script>

<style scoped>
  .message {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    min-width: 300px;
    height: 40px;
    line-height: 40px;
    border-radius: 4px;
    padding: 0 16px;
    display: flex;
    align-items: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    background: #fff;
    z-index: 9999;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    overflow: hidden;
  }

  .message-icon {
    margin-right: 8px;
    font-weight: bold;
    min-width: 16px;
    text-align: center;
  }

  .message-content {
    font-size: 14px;
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .message-close {
    margin-left: 16px;
    padding: 0;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 20px;
    opacity: 0.7;
    transition: opacity 0.2s;
  }

  .message-close:hover {
    opacity: 1;
  }

  .message-success {
    background-color: #2b9e16;
    color: #ffffff;
  }

  .message-warning {
    background-color: #ef7100;
    color: #ffffff;
  }

  .message-info {
    background-color: #eeeeee;
    color: #000000;
  }

  .message-error {
    background-color: #ff0000;
    color: #ffffff;
  }

  /* 动画效果 */
  .message-fade-enter-from,
  .message-fade-leave-to {
    opacity: 0;
    transform: translate(-50%, -20px);
  }

  /* 图标样式 */
  .icon-success:before {
    content: '✓';
  }
  .icon-warning:before {
    content: '!';
  }
  .icon-info:before {
    content: 'i';
  }
  .icon-error:before {
    content: '×';
  }
</style>
