import { onActivated, onDeactivated, ref } from 'vue'
import useScanStore from '@/store/modules/scanMonitor'
import { settings } from '@/utils/settings'
import { ElMessage, ElLoading } from 'element-plus'

export default function (spectrum, taskFunCode) {
  let ws = null
  let paramsForm = {}
  let hostRemember = ''
  let portRemember = ''
  let deviceCodeRemember = ''
  const pageData = ref(null)
  const loading = ref(null)
  let heartBeatTimer = null
  let reconnectTimer = null
  let manualClose = false
  let wsParams = {}

  /**
   * 发送心跳包
   */
  const sendHeartBeat = () => {
    if (ws && ws.readyState === WebSocket.OPEN) {
      ws.send('ping')
    }
  }

  /**
   * 尝试重连
   */
  const tryReconnect = () => {
    if (manualClose || reconnectTimer) return
    reconnectTimer = setInterval(() => {
      if (!ws || ws.readyState === WebSocket.CLOSED) {
        linkScan(paramsForm, hostRemember, portRemember, deviceCodeRemember)
      }
    }, 5000) // 每5秒尝试重连一次
  }

  /**
   * 清除定时器
   */
  const clearTimers = () => {
    if (heartBeatTimer) {
      clearInterval(heartBeatTimer)
      heartBeatTimer = null
    }
    if (reconnectTimer) {
      clearInterval(reconnectTimer)
      reconnectTimer = null
    }
  }

  /**
   * 设置心跳定时器
   */
  const resetHeartBeat = () => {
    if (heartBeatTimer) {
      clearInterval(heartBeatTimer)
    }
    heartBeatTimer = setInterval(sendHeartBeat, 1000) // 每1秒发送一次心跳包
  }

  /**
   * 扫描链接并返回扫描结果
   *
   * @param params 扫描参数
   * @returns 扫描结果
   */
  const linkScan = async (params, host, port, deviceCode) => {
    useScanStore().playFfts = []
    if (ws) {
      ws.close()
    }
    manualClose = false
    paramsForm = params
    hostRemember = host
    portRemember = port
    deviceCodeRemember = deviceCode
    wsParams = {
      host: host ? host : settings.VITE_GLOB_HOST,
      port: port ? port : settings.VITE_GLOB_PORT,
      taskFunCode: taskFunCode.value,
      sendTime: 10,
      deviceCode: deviceCode,
      data: encodeURIComponent(JSON.stringify(params))
    }
    let url = settings.VITE_GLOB_WS + '/websocket'
    Object.keys(wsParams).forEach((key, index) => {
      if (index === 0) {
        url += '?'
      } else {
        url += '&'
      }
      url += `${key}=${wsParams[key]}`
    })
    ws = new WebSocket(url)
    loading.value = ElLoading.service({
      lock: true,
      text: '设备连接中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    spectrum.status = 1

    // 绑定事件处理程序
    ws.onmessage = event => {
      try {
        if (!pageData.value) {
          console.log(event.data)
        }
        if (loading.value) {
          loading.value.close()
          loading.value = null
        }
        const { data } = JSON.parse(event.data)
        pageData.value = data.result
        // resetHeartBeat() // 接收到消息时重置心跳定时器
      } catch (err) {
        spectrum.status = 0
        manualClose = true
        ws.close()
        // clearTimers()
        ElMessage.error('设备连接失败，请检查设备状态！')
      }
    }
    ws.onclose = event => {
      console.log("连接已关闭", event)
      spectrum.status = 0
      // clearTimers()
      // if (!manualClose) {
      //   tryReconnect()
      // }
    }

    ws.onerror = event => {
      console.log("WebSocket 错误", event)
      // clearTimers()
      // if (!manualClose) {
      //   tryReconnect()
      // }
    }

    ws.onopen = () => {
      console.log('连接已打开')
      // clearTimers()
      // resetHeartBeat() // 连接建立时设置心跳定时器
    }
  }

  const closeScan = () => {
    if (ws) {
      manualClose = true
      wsParams.taskFunCode = 0
      wsParams.data = decodeURIComponent(wsParams.data)
      ws.send(JSON.stringify(wsParams))
      ws.close()
      useScanStore().playFfts = []
      useScanStore().playSignalList = []
    }
    // clearTimers()
  }

  onActivated(async () => {
    console.log(ws, 'linkWs websocket')
    manualClose = false
    // 如果需要在激活时自动重新连接，可以取消注释以下代码
    // if (ws) {
    //   await linkScan(paramsForm, hostRemember, portRemember, deviceCodeRemember)
    // }
  })

  onDeactivated(() => {
    if (ws) {
      ws.close()
      useScanStore().playFfts = []
      useScanStore().playSignalList = []
    }
    // clearTimers()
  })

  return {
    linkScan,
    closeScan,
    pageData
  }
}
