<script setup>
  const props = defineProps({
    // 定义组件的属性
    tableData: {
      type: Array,
      default: () => []
    }
  })
</script>

<template>
  <vxe-table
    ref="xTable"
    border
    stripe
    size="medium"
    min-height="450"
    :data="tableData"
    :row-config="{ isCurrent: true, isHover: true }"
  >
    <vxe-column type="seq" width="60" fixed="left" align="center" />
    <vxe-column field="province" title="所在省" width="100" align="center" />
    <vxe-column field="city" title="所在市" width="100" align="center" />
    <vxe-column field="county" title="所在区县" width="100" align="center" />
    <vxe-column field="type" title="通信设施类型" width="120" align="center" />
    <vxe-column field="model" title="通信设置型号" width="120" align="center" />
    <vxe-column field="status" title="设施状态" width="100" align="center" />
    <vxe-column field="longitude" title="所在经度" width="100" align="center" />
    <vxe-column field="latitude" title="所在纬度" width="100" align="center" />
  </vxe-table>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
