<script setup>
  import Highcharts from 'highcharts'

  // 切换波长表格
  const isShow = ref(true)
  // 切换功率 vs 数量
  const isPower = ref(false)
  // Highcharts 实例
  const chartInstance = ref(null)

  // 波长对照数据
  const waveLengthCompareArr = reactive([
    { electroWave: '甚长波', waveLen: '100~100 km', freq: '3~30 kHz' },
    { electroWave: '长波/LW', waveLen: '10~1 km', freq: '30~300 kHz' },
    { electroWave: '中波/MW', waveLen: '1~0.1 km', freq: '300~3000 kHz' },
    { electroWave: '短波/SW', waveLen: '100~10 m', freq: '3~30 MHz' },
    { electroWave: '米波', waveLen: '10~1 m', freq: '30~300 MHz' },
    { electroWave: '分米波', waveLen: '1~0.1 m', freq: '300~3000 MHz' },
    { electroWave: '厘米波', waveLen: '100~10 mm', freq: '3~30 GHz' },
    { electroWave: '毫米波', waveLen: '10~1 mm', freq: '30~300 GHz' }
  ])

  // 示例图表数据（可由 API 或 props 提供）
  const chartCategories = ref([
    '甚长波',
    '长波/LW',
    '中波/MW',
    '短波/SW',
    '米波',
    '分米波',
    '厘米波',
    '毫米波'
  ])
  const countSeries = ref([8, 6, 5, 10, 12, 7, 4, 3])
  const powerSeries = ref([22, 18, 15, 30, 40, 28, 20, 10])

  // 动态计算 chartContainerId
  const chartContainerId = computed(() => (isPower.value ? 'powerContainer' : 'countContainer'))

  function makePowerChartOptions(seriesData, yTitle) {
    return {
      chart: { type: 'column', margin: [50, 50, 80, 80] },
      title: { text: '电磁波波长统计' },
      xAxis: { categories: chartCategories.value, title: { text: '电磁波名称' } },
      yAxis: { title: { text: yTitle }, min: 0 },
      tooltip: { pointFormat: yTitle + ': <b>{point.y}</b>' },
      plotOptions: { column: { dataLabels: { enabled: true } } },
      series: [{ name: yTitle, data: seriesData }],
      credits: { enabled: false },
      accessibility: { enabled: false },
      legend: { enabled: false }
    }
  }

  function makeCountChartOptions(seriesData, yTitle) {
    return {
      chart: { type: 'column', margin: [50, 50, 80, 80] },
      title: { text: '电磁波波长统计' },
      xAxis: { categories: chartCategories.value, title: { text: '电磁波名称' } },
      yAxis: { title: { text: yTitle }, min: 0 },
      tooltip: { pointFormat: yTitle + ': <b>{point.y}</b>' },
      plotOptions: { column: { dataLabels: { enabled: true } } },
      series: [{ name: yTitle, data: seriesData }],
      credits: { enabled: false },
      accessibility: { enabled: false },
      legend: { enabled: false }
    }
  }

  // 渲染或更新图表
  async function renderChart() {
    await nextTick()
    const container = document.getElementById(chartContainerId.value)
    if (!container) return
    const seriesData = isPower.value ? powerSeries.value : countSeries.value
    const yTitle = isPower.value ? '信号功率 (dBμV/m)' : '信号数量'
    const options = isPower.value
      ? makePowerChartOptions(seriesData, yTitle)
      : makeCountChartOptions(seriesData, yTitle)
    chartInstance.value = Highcharts.chart(container, options)
  }

  // 切换统计类型
  function toggleChart() {
    isPower.value = !isPower.value
  }

  onMounted(() => {
    renderChart()
    window.addEventListener('resize', () => {
      chartInstance.value?.reflow()
    })
  })

  onBeforeUnmount(() => {
    chartInstance.value?.destroy()
    window.removeEventListener('resize', () => {})
  })

  watch(isPower, renderChart)
</script>

<template>
  <div>
    <!-- 标题和切换按钮 -->
    <div class="flex justify-between items-center my-2">
      <div>电磁频率波长分布</div>
      <cu-button :content="isPower ? '数量统计' : '功率统计'" @click="toggleChart" />
    </div>

    <!-- 波长对照表 折叠按钮 -->
    <div class="flex items-center cursor-pointer mb-4" @click="isShow = !isShow">
      <vxe-icon :name="isShow ? 'arrow-down' : 'arrow-up'" class="mr-2" />
      <div>电磁波长对照表</div>
    </div>

    <!-- 对照表 -->
    <vxe-table
      v-show="isShow"
      :data="waveLengthCompareArr"
      border
      stripe
      size="medium"
      min-height="160"
      :row-config="{ isCurrent: true, isHover: true }"
      class="mb-6"
    >
      <vxe-column field="electroWave" title="电磁波" align="center" />
      <vxe-column field="waveLen" title="波长" align="center" />
      <vxe-column field="freq" title="频率" align="center" />
    </vxe-table>

    <!-- 图表容器 -->
    <div :id="chartContainerId" class="chart-container"></div>
  </div>
</template>

<style scoped>
  .chart-container {
    width: 100%;
    height: 360px;
    margin-top: 16px;
  }
</style>
