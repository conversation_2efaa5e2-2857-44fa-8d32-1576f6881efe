import VXETable from 'vxe-table'
import dayjs from 'dayjs'

/**
 * useList 是一个自定义的 Vue 组合函数，用于处理列表数据的加载、删除和分页等操作。
 * @param {Function} listRequestFn 用于获取列表数据的请求函数
 * @param {Function} deleteFun 用于删除数据的请求函数
 * @param {Object} options 包含配置选项的对象，如 immediate 和 transformFn
 * @param {ref} tableRef
 * @returns {Object} 包含列表操作相关的响应式数据和方法
 */
export default function useList(listRequestFn, deleteFun, options = {}, tableRef, deleteParamsBuilder = null) {
  const ids = ref([])
  const files = ref([])
  const {
    immediate = true,
  } = options

  const filterOption = ref({ ...options })

  const $table = tableRef
  // 加载态
  const loading = ref(false)
  // 当前页
  const curPage = ref(1)
  // 总
  const total = ref(0)
  // 分页大小
  const size = ref(10)
  // 数据
  const list = ref([])



  // 加载数据
  const loadData = async (pageNum = curPage.value, pageSize = size.value) => {
    loading.value = true
    try {
      const result = await listRequestFn({
        pageNum,
        pageSize,
        ...filterOption.value
      })
      const { data } = result
      list.value = data?.list
      total.value = data?.total
    } catch (error) {
      console.error('数据加载失败:', error)
    } finally {
      loading.value = false
      list.value?.forEach((item, index) => {
        item.listId = index + 1
      })
    }
  }

  // 时间搜索
  const timeSearch = (data) => {
    filterOption.value = data
    const { startTime, endTime } = filterOption.value
    if (startTime) {
      filterOption.value.startTime = dayjs(startTime, 'YYYY-MM-DD').format('YYYY-MM-DD')
    }
    if (endTime) {
      filterOption.value.endTime = dayjs(endTime, 'YYYY-MM-DD').format('YYYY-MM-DD')
    }
    loadData()
  }

  // 删除当前行
  const deleteCurrentLine = async (row, isTips = true) => {
    ids.value.push(row.id)
    if (isTips) {
      const type = await VXETable.modal.confirm('您确定要删除该数据?')
      if (type === 'confirm') {
        await deleteRow(row)
      }
    } else {
      await deleteRow(row)
    }
  }

  // 删除行的通用逻辑
  const deleteRow = async (row) => {
    if ($table) {
      $table.value.remove(row);
      try {
        // 动态生成参数
        const params = deleteParamsBuilder
          ? deleteParamsBuilder(ids.value, row)
          : ids.value;
        await deleteFun(params); // 统一调用 deleteFun
        loadData();
      } catch (error) {
        console.error('删除失败:', error);
      } finally {
        ids.value = [];
      }
    }
  };

  // 批量删除
  const deleteAll = async (isTips = true) => {
    if (ids.value.length === 0) {
      VXETable.modal.message({ content: '请选择删除的数据', status: 'error' })
      return
    }
    if (isTips) {
      const type = await VXETable.modal.confirm('您确定要删除该数据?')
      if (type === 'confirm') {
        await deleteRows()
      }
    } else {
      await deleteRows()
    }
  }

  // 批量删除逻辑（同理）
  const deleteRows = async () => {
    try {
      const params = deleteParamsBuilder
        ? deleteParamsBuilder(ids.value, files.value)
        : ids.value;
      await deleteFun(params);
      loadData();
    } catch (error) {
      console.error('批量删除失败:', error);
    } finally {
      ids.value = [];
    }
  };

  // 勾选框事件
  const selectChangeEvent = () => {
    const selectRecords = $table.value.getCheckboxRecords()
    ids.value = selectRecords.map(item => item.id)
    files.value = selectRecords
  }



  // 监听分页数据改变
  watch([curPage, size], () => {
    loadData(curPage.value, size.value)
  })

  // 挂载时加载数据
  onMounted(() => {
    if (immediate) {
      loadData()
    }
  })

  return {
    loading,
    curPage,
    total,
    list,
    filterOption,
    size,
    loadData,
    timeSearch,
    deleteCurrentLine,
    deleteAll,
    selectChangeEvent,
  }
}