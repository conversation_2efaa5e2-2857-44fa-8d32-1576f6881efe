<script setup>
  import {
    getEquipList,
    addOrUpdateEquip,
    getEquipDetail,
    delEquip,
    batchDelEquip
  } from '@/api/system/equipment'
  import { ElMessage } from 'element-plus'
  import { VXETable } from 'vxe-table'
  import DeviceTest from './components/DeviceTest.vue'

  const { proxy } = getCurrentInstance()
  const { sys_equip_type } = proxy.useDict('sys_equip_type')
  const xTable = ref(null) // 设备附属列表
  const equipTable = ref(null) // 设备管理列表
  const attachIds = ref([]) // 附件id数组
  const files = ref([]) // 附件列表

  /**信号模板列表 */
  const equipmentList = ref([]) // 信号模板列表
  const open = ref(false) // 遮罩层
  const loading = ref(true) // 遮罩层
  const ids = ref([]) // 选中数组
  const multiple = ref(false) // 是否多选
  const total = ref(0) // 总条数
  const title = ref('') // 弹窗标题
  const deviceTestFlag = ref(false) // 设备测试标志
  const currentRow = ref(null) // 当前行数据

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10
    },
    rules: {
      name: [{ required: true, message: '设备名称不能为空', trigger: 'blur' }],
      code: [{ required: true, message: '设备编号不能为空', trigger: 'blur' }],
      ip: [{ required: true, message: 'IP不能为空', trigger: 'blur' }],
      port: [{ required: true, message: '端口不能为空', trigger: 'blur' }]
    }
  })

  const { queryParams, form, rules } = toRefs(data)

  /** 查询信号模板列表 */
  const getList = () => {
    loading.value = true
    getEquipList(queryParams.value).then(response => {
      equipmentList.value = response.data.list
      total.value = response.data.total || 0
      loading.value = false
    })
  }

  /** 取消按钮 */
  const cancel = () => {
    open.value = false
    reset()
  }

  /** 表单重置 */
  const reset = () => {
    form.value = {
      id: undefined,
      ip: undefined,
      name: undefined,
      code: undefined,
      port: undefined,
      redWarningCR: undefined,
      blueWarningCR: undefined,
      type: '0',
      deviceAffiliateModuleList: [] // 初始化为数组，确保可以添加设备
    }
    proxy.resetForm('equipRef')
  }

  /**
   * 添加设备
   */
  const handleAdd = () => {
    reset()
    open.value = true
    title.value = '添加设备'
  }

  /**
   * 设备管理列表-多选框选中事件
   */
  const handleSelectionChange = () => {
    const selectRecords = equipTable.value.getCheckboxRecords()
    ids.value = []
    selectRecords.forEach(item => {
      ids.value.push(item.id)
    })
    if (selectRecords.length != ids.value.length) {
      ids.value = []
      selectRecords.forEach(item => {
        ids.value.push(item.id)
      })
    }
    multiple.value = ids.value.length > 0
  }

  /** 修改按钮操作 */
  const handleUpdate = row => {
    reset()
    const equipId = row.id || ids.value
    getEquipDetail(equipId).then(response => {
      form.value = response.data
      form.value.type = form.value.type.toString()
      open.value = true
      title.value = '修改设备'
    })
  }

  /**
   * 删除设备管理列表项
   * @param row 当前行数据
   */
  const handleDelete = row => {
    proxy.$modal
      .confirm('是否确认删除')
      .then(function () {
        if (ids.value.length > 0) {
          return batchDelEquip(ids.value)
        } else {
          return delEquip({ id: row.id })
        }
      })
      .then(() => {
        getList()
        proxy.$modal.msgSuccess('删除成功')
      })
      .catch(() => {})
  }

  /** 导出按钮操作 */
  const handleExport = () => {
    proxy.download(
      '/device/export',
      {
        ...queryParams.value
      },
      `post_${new Date().getTime()}.xlsx`
    )
  }

  /** 提交按钮 */
  const submitForm = () => {
    proxy.$refs['equipRef'].validate(valid => {
      if (valid) {
        const submitData = {
          name: form.value.name,
          code: form.value.code,
          ip: form.value.ip,
          port: form.value.port,
          type: form.value.type,
          redWarningCR: Number(form.value.redWarningCR),
          blueWarningCR: Number(form.value.blueWarningCR),
          deviceAffiliateModuleList: form.value.deviceAffiliateModuleList // 包含附属设备列表
        }
        open.value = false
        if (form.value.id != undefined) {
          submitData.id = form.value.id
          addOrUpdateEquip(submitData).then(() => {
            proxy.$modal.msgSuccess(`修改成功`)
            open.value = false
            getList()
          })
        } else {
          addOrUpdateEquip(submitData).then(() => {
            proxy.$modal.msgSuccess('新增成功')
            open.value = false
            getList()
          })
        }
      }
    })
  }

  /**
   * 设备附属模块-新增按钮
   * @param row 当前行数据
   */
  const insertEvent = async row => {
    if (xTable) {
      const newModule = {
        moduleNm: '', // 模块名称
        moduleIp: '', // 模块IP
        modulePort: '', // 模块端口
        moduleUrl: '', // 访问URL
        note: '' // 备注
      }
      // 插入新设备模块到 deviceAffiliateModuleList 数组
      form.value.deviceAffiliateModuleList.push(newModule)
      // 在表格中插入这条数据
      const { row: newRow } = await xTable.value.insertAt(newModule, row)
      await xTable.value.setEditCell(newRow, 'moduleNm')
    }
  }

  /**
   * 设备附属模块 - URL访问按钮
   * @param row 当前行数据
   */
  const interView = row => {
    if (row.moduleUrl) {
      window.open(row.moduleUrl)
    } else {
      ElMessage.error('请先配置访问URL')
    }
  }

  /**
   * 设备附属模块 - 选中事件
   * @param row 当前行数据
   */
  const selectChangeEvent = () => {
    const selectRecords = xTable.value.getCheckboxRecords()
    attachIds.value = []
    files.value = []
    selectRecords.forEach(item => {
      attachIds.value.push(item.id)
      files.value.push(item)
    })
    if (selectRecords.length != attachIds.value.length) {
      attachIds.value = []
      selectRecords.forEach(item => {
        attachIds.value.push(item.id)
      })
    }
  }

  /**
   * 设备附属模块 - 删除按钮
   * @param row 当前行数据
   */
  const deleteAll = async () => {
    if (attachIds.value.length != 0) {
      console.log(attachIds)
      await xTable.value.remove(attachIds.value)
      form.value.deviceAffiliateModuleList = form.value.deviceAffiliateModuleList.filter(item => {
        return !attachIds.value.includes(item.id)
      })
    } else {
      VXETable.modal.message({ content: '请选择删除的数据', status: 'error' })
    }
  }

  /**
   * 设备附属模块 - 删除当前行按钮
   * @param row
   */
  const deleteCurrentLine = async row => {
    if (xTable) {
      xTable.value.remove(row)
      form.value.deviceAffiliateModuleList.findIndex((item, index) => {
        if (item.id == row.id) {
          form.value.deviceAffiliateModuleList.splice(index, 1)
        }
      })
    }
  }

  /**
   * 设备附属模块 - 格式化输入框为一位小数
   * @param field 字段名
   */
  const formatToOneDecimal = field => {
    // 允许用户输入数字和一个小数点，并限制小数点后只有一位
    form.value[field] = form.value[field]
      .toString()
      .replace(/[^0-9.]/g, '') // 只允许数字和小数点
      .replace(/^0+/, '0') // 禁止以多个0开头
      .replace(/(\..*)\./g, '$1') // 防止多个小数点
      .replace(/^(\d+)\.(\d{0,1}).*$/, '$1.$2') // 保留最多一位小数
  }

  /**
   *  设备附属模块 - 失去焦点时格式化成一位小数的浮点数
   * @param field 字段名
   */
  const formatOnBlur = field => {
    // 失去焦点时格式化成一位小数的浮点数
    if (form.value[field]) {
      form.value[field] = parseFloat(form.value[field]).toFixed(1)
    }
  }

  const equipTest = row => {
    deviceTestFlag.value = true
    currentRow.value = row
  }

  // 监听分页数据改变
  watch(
    () => [queryParams.value.pageNum, queryParams.value.pageSize],
    () => getList()
  )

  onMounted(() => {
    getList()
  })
</script>

<template>
  <div class="app-container">
    <vxe-toolbar class="vxe-toolbar">
      <template #buttons>
        <cu-button content="新增" @click="handleAdd" />
        <cu-button content="批量删除" :disabled="!multiple" @click="handleDelete" />
        <cu-button content="导出" @click="handleExport" />
      </template>
    </vxe-toolbar>
    <vxe-table
      ref="equipTable"
      max-height="300"
      align="center"
      border
      :data="equipmentList"
      :loading="loading"
      :checkbox-config="{ labelField: 'id' }"
      :keep-source="true"
      :row-config="{ isCurrent: true, isHover: true }"
      @checkbox-change="handleSelectionChange"
      @checkbox-all="handleSelectionChange"
    >
      <vxe-column type="checkbox" title="序号" width="90" fixed="left" align="center" />
      <vxe-column field="type" title="设备类型">
        <template #default="{ row }">
          <dict-tag :options="sys_equip_type" :value="row.type" />
        </template>
      </vxe-column>
      <vxe-column field="code" title="设备编号"> </vxe-column>
      <vxe-column field="name" title="设备名称"> </vxe-column>
      <vxe-column field="ip" title="IP地址"> </vxe-column>
      <vxe-column field="port" title="端口"> </vxe-column>
      <!-- <vxe-column field="redWarningCR" title="红色警戒圈半径"> </vxe-column> -->
      <!-- <vxe-column field="blueWarningCR" title="蓝色警戒圈半径"> </vxe-column> -->
      <vxe-column field="操作" width="auto" title="操作">
        <template #default="{ row }">
          <cu-button content="修改" @click="handleUpdate(row)" />
          <cu-button content="状态检测" @click="equipTest(row)" />
        </template>
      </vxe-column>
    </vxe-table>

    <vxe-pager
      v-show="total > 0"
      v-model:current-page="queryParams.pageNum"
      v-model:page-size="queryParams.pageSize"
      perfect
      :total="total"
      :page-sizes="[10, 20, 50, 100, 200, 500]"
      :layouts="[
        'PrevJump',
        'PrevPage',
        'Number',
        'NextPage',
        'NextJump',
        'Sizes',
        'FullJump',
        'Total'
      ]"
    />

    <!-- 添加或修改信号模板对话框 -->
    <cu-dialog
      v-model="open"
      width="1000"
      :title="title"
      append-to-body
      @confirm="submitForm"
      @cancel="cancel"
    >
      <el-form ref="equipRef" :model="form" :rules="rules" label-width="auto">
        <el-form-item label="设备类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio v-for="dict in sys_equip_type" :key="dict.value" :value="dict.value">
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="设备名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入设备名称" />
        </el-form-item>
        <el-form-item label="设备编号" prop="code">
          <el-input v-model="form.code" placeholder="请输入编号" />
        </el-form-item>
        <el-form-item label="ip" prop="ip">
          <el-input v-model="form.ip" placeholder="请输入IP地址" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input v-model="form.port" placeholder="请输入端口" />
        </el-form-item>
        <!-- <el-form-item label="红色警戒圈半径" prop="redWarningCR">
          <el-input
            v-model="form.redWarningCR"
            placeholder="请输入红色警戒圈半径"
            @input="formatToOneDecimal('redWarningCR')"
            @blur="formatOnBlur('redWarningCR')"
          />
        </el-form-item>
        <el-form-item label="蓝色警戒圈半径" prop="blueWarningCR">
          <el-input
            v-model="form.blueWarningCR"
            placeholder="请输入蓝色警戒圈半径"
            @input="formatToOneDecimal('blueWarningCR')"
            @blur="formatOnBlur('blueWarningCR')"
          />
        </el-form-item> -->
      </el-form>
      <div>
        <div class="text-lg font-bold border-l-4 border-black pl-3 mb-2">设备附属模块</div>
        <vxe-toolbar class="ml-3">
          <template #buttons>
            <cu-button size="mini" content="新增" @click="insertEvent(-1)" />
            <cu-button
              size="mini"
              content="删除"
              :disabled="attachIds.length <= 0"
              @click="deleteAll"
            />
          </template>
        </vxe-toolbar>
        <vxe-table
          ref="xTable"
          max-height="300"
          align="center"
          border
          round
          :data="form.deviceAffiliateModuleList"
          :loading="loading"
          :edit-config="{
            trigger: 'click',
            mode: 'cell',
            showStatus: true
          }"
          keep-source
          @checkbox-change="selectChangeEvent"
          @checkbox-all="selectChangeEvent"
        >
          <vxe-column type="checkbox" width="60" fixed="left" />
          <vxe-column type="seq" width="60"></vxe-column>
          <vxe-column field="moduleNm" title="模块名称" :edit-render="{}">
            <template #edit="{ row }">
              <input v-model="row.moduleNm" class="input-name" type="text" maxlength="15" />
            </template>
          </vxe-column>
          <vxe-column field="moduleIp" title="模块IP" width="120" :edit-render="{}">
            <template #edit="{ row }">
              <input v-model="row.moduleIp" class="input-name" type="text" maxlength="15" />
            </template>
          </vxe-column>
          <vxe-column field="modulePort" title="模块端口" width="120" :edit-render="{}">
            <template #edit="{ row }">
              <input v-model="row.modulePort" class="input-name" type="text" maxlength="15" />
            </template>
          </vxe-column>
          <vxe-column field="moduleUrl" title="访问URL" :edit-render="{}">
            <template #edit="{ row }">
              <input v-model="row.moduleUrl" class="input-name" type="text" />
            </template>
          </vxe-column>
          <vxe-column field="note" title="备注" width="160" :edit-render="{}">
            <template #edit="{ row }">
              <input v-model="row.note" class="input-name" type="text" maxlength="15" />
            </template>
          </vxe-column>
          <vxe-column field="操作" title="操作" width="160">
            <template #default="{ row }">
              <cu-button size="mini" content="访问" @click="interView(row)" />
              <cu-button size="mini" content="删除" @click="deleteCurrentLine(row)" />
            </template>
          </vxe-column>
        </vxe-table>
      </div>
    </cu-dialog>

    <DeviceTest v-model="deviceTestFlag" :row="currentRow" />
  </div>
</template>
