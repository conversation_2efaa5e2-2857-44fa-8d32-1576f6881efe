<script setup>
  const props = defineProps({
    type: {
      type: String,
      default: 'add'
    },
    currentRow: {
      type: Object,
      default: () => ({})
    }
  })
  // 对话框可见性
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const emits = defineEmits(['confirm'])

  const formRef = ref(null)
  const formData = ref(
    props.currentRow || {
      unit: '',
      assetTag: '',
      serialNumber: '',
      model: '',
      status: '',
      remarks: '',
      city: '',
      imagePath: '',
      facName: '',
      warrantyMonths: ''
    }
  )
  const uploadRef = ref()

  const confirm = () => {
    emits('confirm', formData.value)
  }

  const cancel = () => {
    dialogTableVisible.value = false
  }

  const selectEvent = () => {
    const $upload = uploadRef.value
    if ($upload) {
      $upload.choose()
    }
  }
</script>

<template>
  <cu-dialog
    v-model="dialogTableVisible"
    :title="type === 'add' ? '新增通信设施' : '编辑通信设施'"
    width="600"
    @confirm="confirm"
    @cancel="cancel"
  >
    <vxe-form
      ref="formRef"
      :title-colon="true"
      title-width="100"
      title-align="right"
      title-bold
      :data="formData"
    >
      <vxe-form-item title="单位" field="unit" :span="24">
        <vxe-select v-model="formData.unit">
          <vxe-option value="1" label="单位1">单位1</vxe-option>
        </vxe-select>
      </vxe-form-item>
      <vxe-form-item title="资产标签" field="assetTag" :span="24">
        <vxe-select v-model="formData.assetTag">
          <vxe-option value="1" label="标签1">标签1</vxe-option>
        </vxe-select>
      </vxe-form-item>
      <vxe-form-item title="序列号" field="serialNumber" :span="24">
        <vxe-input v-model="formData.serialNumber"></vxe-input>
      </vxe-form-item>
      <vxe-form-item title="型号" field="model" :span="24">
        <vxe-select v-model="formData.model">
          <vxe-option value="1" label="型号1">型号1</vxe-option>
        </vxe-select>
      </vxe-form-item>
      <vxe-form-item title="状态" field="status" :span="24">
        <vxe-select v-model="formData.status">
          <vxe-option value="1" label="正常">正常</vxe-option>
        </vxe-select>
      </vxe-form-item>
      <vxe-form-item title="备注" field="remarks" :span="24">
        <vxe-textarea v-model="formData.remarks" resize="vertical"></vxe-textarea>
      </vxe-form-item>
      <vxe-form-item title="默认位置" field="city" :span="24">
        <vxe-select v-model="formData.city">
          <vxe-option value="1" label="默认位置1">默认位置1</vxe-option>
        </vxe-select>
      </vxe-form-item>
      <vxe-form-item title="上传图片" field="assetsTags" :span="24">
        <div>
          <cu-button content="点击上传" @click="selectEvent"></cu-button>
          <div class="text-gray-400 mt-2">接受jpg、png、gif、svg类型的文件，文件大小应小于2M</div>
        </div>
        <vxe-upload
          ref="uploadRef"
          v-model="formData.imagePath"
          :file-types="['jpg', 'png', 'gif', 'svg']"
          :limit-size="2"
          :limit-count="1"
          :showUploadButton="false"
        >
        </vxe-upload>
      </vxe-form-item>
      <vxe-form-item title="资产名称" field="facName" :span="24">
        <vxe-input v-model="formData.facName"></vxe-input>
      </vxe-form-item>
      <vxe-form-item title="质保" field="warrantyMonths" :span="12">
        <vxe-input
          class="monthInput"
          v-model="formData.warrantyMonths"
          type="number"
          :min="0"
          step="1"
        >
          <template #suffix>
            <div>月数</div>
          </template>
        </vxe-input>
      </vxe-form-item>
    </vxe-form>
  </cu-dialog>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
  .monthInput {
    :deep(.vxe-input--suffix-icon) {
      padding-right: 0;
      width: 50px;
      text-align: center;
      color: #000;
      background-color: #f1f1f1;
    }
  }
</style>
