<script setup>
  // 在这里编写逻辑
  import useList from '@/api/tool/filemanage/tableFunctionPro'
  import { ElMessage } from 'element-plus'
  import { VxeUI } from 'vxe-pc-ui'
  import { processDictionary } from '@/utils/index.js'
  import conflictResolutionDialog from './components/conflictResolutionDialog.vue'
  import enablePlanDialog from './components/enablePlanDialog.vue'
  import freqPlanDialog from './components/freqPlanDialog.vue'
  import { queryTaskOption } from '@/api/pgmx/useFreqTask.js'
  import { queryPlanOption, queryPlanUseFreq } from '@/api/pgmx/useFreq.js'
  import {
    conflictAnalyze,
    analyzeResolutionResult,
    conflictResolution,
    saveResolution
  } from '@/api/pgmx/freqResolution.js'
  import { readPlanUseFreqEquip } from '@/api/pgmx/schedulingMonitoring.js'

  const filterOption = ref({
    immediate: false,
    planId: null
  })
  const xTable = ref(null)
  const { list, loading, curPage, size, total, timeSearch } = useList(
    queryPlanUseFreq,
    null,
    filterOption.value,
    xTable
  )

  const conflictResolutionVisible = ref(false) //冲突消解弹窗
  const freqPlanDialogVisible = ref(false) //用频计划弹窗
  const enablePlanVisible = ref(false) //启用本用频方案弹窗

  const taskOptions = ref([]) //zz任务枚举
  const planOptions = ref([]) //用频方案枚举
  const equipmentModelOptions = ref([]) //设备型号枚举
  const unitOptions = ref([]) //用频单位枚举
  const useFreqConflictOptions = ref([]) //用频冲突枚举

  /** 用频冲突列表参数 */
  const conflictionAnalyzeId = ref(null) //冲突分析ID
  const cLoading = ref(false)
  const cPage = ref(1)
  const cSize = ref(10)
  const cTotal = ref(0)
  const conflictList = ref([]) //用频冲突列表

  const taskId = ref(null) //zz任务ID
  const planId = ref(null) //用频方案ID
  const resolutionType = ref('1') //消解维度
  const isResolution = ref(false) //是否消解

  const currentRowValue = ref({}) //当前行数据--用来查看当前行的用频计划/消解后用频计划
  const freqPlanType = ref(1) //用频计划类型--用来区分是查看用频计划还是消解后用频计划

  const getDictionaryData = async () => {
    processDictionary('pgmx_equipment', equipmentModelOptions)
    processDictionary('pgmx_unit', unitOptions)
    processDictionary('pgmx_usefreqconflict', useFreqConflictOptions)
    await queryTaskOption().then(res => {
      if (res.code === 200) {
        taskOptions.value = res.data
      }
    })
  }

  /** 任务切换，请求对应用频方案 */
  const taskChange = async ({ value }) => {
    resetData()
    await queryPlanOption({ taskName: value }).then(res => {
      if (res.code === 200) {
        planOptions.value = res.data
      }
    })
  }

  /** 重置数据 */
  const resetData = () => {
    planId.value = null
    list.value = []
    filterOption.value.planId = null
    conflictList.value = []
    isResolution.value = false
    conflictionAnalyzeId.value = null
    cPage.value = 1
    cSize.value = 10
    cTotal.value = 0
  }

  /** 单元格点击事件 */
  const handleCellClick = ({ row, column, $event }) => {
    if (column.field === 'freqPlan' || column.field === 'resolutionList') {
      currentRowValue.value = row
      readPlanUseFreqEquip({
        freqUsageId: row.id
      })
        .then(res => {
          if (res.code === 200) {
            nextTick(() => {
              currentRowValue.value.equipmentList = res.data
            })
          }
        })
        .finally(() => {
          freqPlanType.value = column.field === 'freqPlan' ? 1 : 2
          freqPlanDialogVisible.value = true
        })
    }
  }

  /** 查询（获取用频规划后的方案） */
  const queryTask = () => {
    // 这里查询的逻辑
    if (!taskId.value || !planId.value) {
      ElMessage.error('请选择zz任务和规划策略')
    } else {
      filterOption.value.planId = planId.value
      timeSearch(filterOption.value)
    }
  }

  /** 获取用频冲突列表 */
  const getConflictAnalysisList = async (page, size) => {
    cLoading.value = true
    await analyzeResolutionResult({
      conflictionAnalyzeId: conflictionAnalyzeId.value,
      pageNum: page ? page : cPage.value,
      pageSize: size ? size : cSize.value
    }).then(res => {
      if (res.code === 200) {
        conflictList.value = res.data.list
        cTotal.value = res.data.total
        cLoading.value = false
      }
    })
  }

  /** 冲突分析按钮 */
  const conflictAnalysis = () => {
    VxeUI.loading.open({
      text: '分析中...'
    })
    // 这里编写冲突分析的逻辑
    conflictAnalyze({
      taskName: taskId.value,
      planId: planId.value
    })
      .then(async res => {
        if (res.code === 200) {
          conflictionAnalyzeId.value = res.data.conflictionAnalyzeId
          // 请求冲突分析列表
          getConflictAnalysisList()
        }
      })
      .catch(err => {
        loading.value = false
        ElMessage.error(err)
      })
      .finally(() => {
        VxeUI.loading.close()
      })
  }

  /**冲突消解按钮-触发维度选择弹窗 */
  const conflictResolutionHandle = () => {
    // 显示冲突消解界面
    conflictResolutionVisible.value = true
  }

  /** 冲突消解执行函数 */
  const confirmConflictResolution = async () => {
    // 确认冲突消解
    VxeUI.loading.open({
      text: '消解中...'
    })
    conflictResolution({
      conflictionAnalyzeId: conflictionAnalyzeId.value,
      resolutionType: resolutionType.value,
      planId: planId.value,
      taskName: taskId.value
    })
      .then(res => {
        if (res.code === 200) {
          isResolution.value = true
          ElMessage.success('操作成功')
          getConflictAnalysisList()
        }
      })
      .finally(() => {
        VxeUI.loading.close()
      })
  }

  /** 保存冲突消解结果 */
  const saveResolutionHandle = async () => {
    VxeUI.loading.open({
      text: '保存中...'
    })
    await saveResolution({
      conflictionAnalyzeId: conflictionAnalyzeId.value
    })
      .then(res => {
        if (res.code === 200) {
          ElMessage.success('保存成功')
        } else {
          ElMessage.error('保存失败')
        }
      })
      .finally(() => {
        VxeUI.loading.close()
      })
  }

  /**启用本用频方案按钮 */
  const enablePlan = () => {
    // 这里编写启用计划的逻辑
    enablePlanVisible.value = true
  }

  onMounted(async () => {
    await getDictionaryData()
  })

  // 监听分页数据改变
  watch([cPage, cSize], () => {
    getConflictAnalysisList(cPage.value, cSize.value)
  })
</script>

<template>
  <div class="flex items-center mb-4">
    <div class="w-[70px] flex items-center">ZZ任务</div>
    <vxe-select style="width: 260px" v-model="taskId" @change="taskChange">
      <vxe-option
        v-for="item in taskOptions"
        :value="item.value"
        :label="item.label"
        :key="item.value"
      ></vxe-option>
    </vxe-select>
    <cu-button type="primary" class="ml-5" content="查询" @click="queryTask"></cu-button>
  </div>
  <div class="flex items-center mb-4">
    <div class="w-[70px] flex items-center">用频方案</div>
    <vxe-select style="width: 260px" v-model="planId">
      <vxe-option
        v-for="item in planOptions"
        :value="item.value"
        :label="item.label"
        :key="item.value"
      ></vxe-option>
    </vxe-select>
    <cu-button
      type="primary"
      class="ml-5"
      content="冲突分析"
      @click="conflictAnalysis"
      :disabled="!filterOption.planId"
    ></cu-button>
    <cu-button
      type="primary"
      content="冲突消解"
      @click="conflictResolutionHandle"
      :disabled="!filterOption.planId || !conflictionAnalyzeId"
    ></cu-button>
    <cu-button
      type="primary"
      content="启用本用频方案"
      @click="enablePlan"
      :disabled="!filterOption.planId"
    ></cu-button>
  </div>

  <!-- 用频规划列表 -->
  <div>
    <vxe-table
      ref="xTable"
      border
      stripe
      size="medium"
      height="355"
      :data="list"
      :loading="loading"
      :row-config="{ isCurrent: true, isHover: true }"
      @cell-click="handleCellClick"
    >
      <vxe-column type="seq" width="60" fixed="left" align="center" />
      <vxe-column
        field="taskDescribe"
        title="用频单位"
        fixed="left"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
        <template #default="{ row }">
          {{ selectDictLabel(unitOptions, row.frequencyUsingUnit) }}
        </template>
      </vxe-column>
      <vxe-column
        field="createBy"
        title="装备型号"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
        <template #default="{ row }">
          {{ selectDictLabel(equipmentModelOptions, row.equipmentModel) }}
        </template>
      </vxe-column>
      <vxe-column field="freqUsageEquipment" title="用频装备" align="center" />
      <vxe-column field="freqPlan" title="用频计划" align="center" :edit-render="{}">
        <template #default="{ row }">
          <div class="cursor-pointer text-[#00603b] underline"> 查看计划 </div>
        </template>
      </vxe-column>
    </vxe-table>
    <!-- 分页 -->
    <p>
      <vxe-pager
        v-model:current-page="curPage"
        v-model:page-size="size"
        class="vxe-page"
        perfect
        :total="total"
        :page-sizes="[10, 20, 50, 100, 200, 500]"
        :layouts="[
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'Sizes',
          'FullJump',
          'Total'
        ]"
      />
    </p>
  </div>

  <div class="my-4 flex items-center gap-x-5">
    <div>用频冲突</div>
    <cu-button
      content="保存消解方案"
      @click="saveResolutionHandle"
      :disabled="!isResolution"
    ></cu-button>
  </div>
  <div>
    <vxe-table
      ref="xTable"
      border
      stripe
      size="medium"
      height="355"
      :data="conflictList"
      :loading="cLoading"
      :row-config="{ isCurrent: true, isHover: true }"
      @cell-click="handleCellClick"
    >
      <vxe-column type="seq" width="60" fixed="left" align="center" />
      <vxe-column
        field="taskDescribe"
        title="用频单位"
        fixed="left"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
        <template #default="{ row }">
          {{ selectDictLabel(unitOptions, row.frequencyUsingUnit) }}
        </template>
      </vxe-column>
      <vxe-column
        field="createBy"
        title="装备型号"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
        <template #default="{ row }">
          {{ selectDictLabel(equipmentModelOptions, row.equipmentModel) }}
        </template>
      </vxe-column>
      <vxe-column field="freqUsageEquipment" title="用频装备" align="center" />
      <vxe-column field="freqPlan" title="用频计划" align="center">
        <template #default="{ row }">
          <div class="cursor-pointer text-[#00603b] underline"> 查看计划 </div>
        </template>
      </vxe-column>
      <vxe-column
        field="conflictResult"
        title="用频冲突"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
        <template #default="{ row }">
          {{ selectDictLabel(useFreqConflictOptions, row.conflictResult) }}
        </template>
      </vxe-column>
      <vxe-column field="resolutionList" title="消解后用频计划" align="center" width="140">
        <template #default="{ row }">
          <div class="cursor-pointer text-[#00603b] underline">
            {{ row.resolutionList && row.resolutionList.length > 0 ? '查看计划' : '' }}
          </div>
        </template>
      </vxe-column>
    </vxe-table>
    <!-- 分页 -->
    <p>
      <vxe-pager
        v-model:current-page="cPage"
        v-model:page-size="cSize"
        class="vxe-page"
        perfect
        :total="cTotal"
        :page-sizes="[5, 10, 20, 50, 100, 200, 500]"
        :layouts="[
          'PrevJump',
          'PrevPage',
          'Number',
          'NextPage',
          'NextJump',
          'Sizes',
          'FullJump',
          'Total'
        ]"
      />
    </p>
  </div>

  <conflict-resolution-dialog
    v-if="conflictResolutionVisible"
    v-model="conflictResolutionVisible"
    v-model.resolutionType="resolutionType"
    @confirmConflictResolution="confirmConflictResolution"
  ></conflict-resolution-dialog>

  <enablePlanDialog
    v-if="enablePlanVisible"
    v-model="enablePlanVisible"
    :taskId="taskId"
    :planId="planId"
    :conflictionAnalyzeId="conflictionAnalyzeId"
    :resolutionType="resolutionType"
    :taskOptions="taskOptions"
    :planOptions="planOptions"
  ></enablePlanDialog>

  <freq-plan-dialog
    v-if="freqPlanDialogVisible"
    v-model="freqPlanDialogVisible"
    :planObject="currentRowValue"
    :type="freqPlanType"
    :title="freqPlanType === 1 ? '用频计划' : '消解后用频计划'"
  ></freq-plan-dialog>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
