<script setup>
  // 在这里编写逻辑
  import listCom from './components/listCom.vue'

  const taskName = ref('')

  const freqDirectiveList = reactive([
    '17:12:12 电台x12用频从1212.12MHz调整为1313.13MHz',
    '17:12:12 电台x12用频从1212.12MHz调整为1313.13MHz',
    '17:12:12 电台x12用频从1212.12MHz调整为1313.13MHz',
    '17:12:12 电台x12用频从1212.12MHz调整为1313.13MHz',
    '17:12:12 电台x12用频从1212.12MHz调整为1313.13MHz',
    '17:12:12 电台x12用频从1212.12MHz调整为1313.13MHz',
    '17:12:12 电台x12用频从1212.12MHz调整为1313.13MHz',
    '17:12:12 电台x12用频从1212.12MHz调整为1313.13MHz'
  ])

  const participatingUnits = reactive(['合成77连', '合成77连', '合成77连', '合成77连'])

  const equipmentList = reactive([
    '电台77',
    '电台77',
    '电台77',
    '电台77',
    '电台77',
    '电台77',
    '电台77',
    '电台77'
  ])

  const protectedFreqList = reactive([
    '1717.17MHz-1818.18MHz',
    '1717.17MHz-1818.18MHz',
    '1717.17MHz-1818.18MHz',
    '1717.17MHz-1818.18MHz'
  ])

  const collaborationFreqList = reactive([
    '1717.17MHz-1818.18MHz',
    '1717.17MHz-1818.18MHz',
    '1717.17MHz-1818.18MHz',
    '1717.17MHz-1818.18MHz'
  ])
  const freqMngAccess = () => {
    // 这里编写频管接入的逻辑
  }
</script>

<template>
  <div class="flex items-center mb-4">
    <div class="w-[80px] flex items-center">ZZ任务</div>
    <vxe-input v-model="taskName" placeholder="请输入任务名称" />
    <cu-button
      width="98px"
      type="primary"
      class="ml-5"
      content="频管接入"
      @click="freqMngAccess"
    ></cu-button>
  </div>
  <list-com title="频管指令" :list="freqDirectiveList" :height="270" />
  <div class="grid grid-cols-2 gap-x-10 mt-4">
    <list-com title="参与单位" :list="participatingUnits" />
    <list-com title="用频装备" :list="equipmentList" />
  </div>
  <div class="grid grid-cols-2 gap-x-10 mt-4">
    <list-com title="保护频段" :list="protectedFreqList" />
    <list-com title="协同频率" :list="collaborationFreqList" />
  </div>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
