<template>
  <vxe-modal
    v-model="internalVisible"
    :width="width"
    :show-footer="true"
    :mask="true"
    :lock-view="true"
    :esc-closable="true"
    resize
    remember
    @hide="handleClose"
  >
    <!-- 自定义 Header -->
    <template #header>
      <div class="custom-modal-header">
        <span class="custom-modal-title">{{ title }}</span>
        <!-- 关闭按钮 -->
        <vxe-icon name="close" class="custom-modal-close" @click="cancel" />
      </div>
    </template>

    <!-- 主体内容区域 -->
    <div class="modal-content">
      <slot></slot>
    </div>

    <!-- 底部操作按钮区域 -->
    <template #footer>
      <div class="modal-footer">
        <cu-button content="确定" @click="confirm" />
        <cu-button type="minor" content="取消" @click="cancel" />
      </div>
    </template>
  </vxe-modal>
</template>

<script setup>
  // 定义组件 Props
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: '对话框标题'
    },
    width: {
      type: String,
      default: '500px'
    }
  })

  // 向父组件抛出事件
  const emit = defineEmits(['confirm', 'cancel'])

  // 内部维护的 visible 状态
  const internalVisible = defineModel({ type: Boolean, default: false })

  function handleClose() {
    internalVisible.value = false
  }

  function confirm() {
    emit('confirm')
  }

  function cancel() {
    emit('cancel')
    internalVisible.value = false
  }
</script>

<style lang="scss" scoped>
  // 把默认的 header padding 清掉，给自定义容器撑满
  :deep(.vxe-modal--header) {
    padding: 0 !important;
    height: 40px; // 你想要的高度
    line-height: 40px;
  }

  .custom-modal-header {
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgb(2, 68, 16), rgb(254, 255, 255)) !important;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 14px;
    box-sizing: border-box;
  }

  .custom-modal-title {
    font-size: 14px;
    font-weight: bold;
    color: #fff;
  }

  .custom-modal-close {
    font-size: 16px;
    font-weight: 700;
    color: #44b283;
    cursor: pointer;
    transition: color 0.2s;
  }
  .custom-modal-close:hover {
    color: #00603b;
  }

  // 其余样式保持不变...

  :deep(.vxe-modal--footer) {
    padding: 20px 14px 14px 0;
    text-align: right;
  }

  .modal-footer {
    display: flex;
    justify-content: center;
    gap: 12px;
  }
</style>
