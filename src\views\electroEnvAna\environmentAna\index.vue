<script setup>
  import CuAreaMapCom from '@/components/CuAreaMapCom/index.vue'
  import monitorSSCom from '../components/monitorSSCom.vue'
  import envAnalysisCom from '../components/envAnalysisCom.vue'
  import radiationTypeCom from '../components/radiationTypeCom.vue'
  import electFreqWaveDisCom from '../components/electFreqWaveDisCom.vue'
  import timeDER from '../components/timeDER.vue'
  import { queryEmeAnalyze } from '@/api/pgmx/emeMonitor.js'
  import { ElMessage } from 'element-plus'
  import { round } from 'lodash'

  const CuAreaMapComRef = ref(null)
  const formData = reactive({
    startTime: '',
    endTime: ''
  })

  const monitorChartList = ref([])
  const chartData = ref({})
  const showCharts = ref(false)

  const monitorStatistic = async () => {
    try {
      // 调用子组件的校验方法
      await CuAreaMapComRef.value.validateForm()
      const active = CuAreaMapComRef.value.activeName
      const { polygon, circle } = CuAreaMapComRef.value.formData
      const queryData = {
        area: {
          geoPointList: [
            { latitude: polygon.lat, longitude: polygon.lon },
            { latitude: polygon.latEnd, longitude: polygon.lonEnd }
          ],
          longitude: circle.cLon,
          latitude: circle.cLat,
          radius: circle.radius,
          type: active === 'polygon' ? 3 : active === 'circle' ? 1 : 2
        },
        starTime: formData.startTime,
        endTime: formData.endTime
      }
      // 这里编写查询监测任务的逻辑
      queryEmeAnalyze(queryData).then(res => {
        ElMessage.success('查询成功')
        monitorChartList.value = res.data
        chartData.value = {
          xAxisData: res.data.map(item => round(item.signalFrequency / 1e6, 4)),
          seriesData: res.data.map(item => item.signalPower)
        }
      })
    } catch (error) {}
  }

  const startAnalysis = data => {
    console.log(data)
    showCharts.value = true
  }
</script>

<template>
  <el-row>
    <el-col>
      <div class="w-full h-10 flex items-center justify-center bg-[#007575] text-white my-1 mb-2">
        Step1:环境参数选择
      </div>
      <div class="grid grid-cols-2 gap-4 w-full mt-4">
        <CuAreaMapCom ref="CuAreaMapComRef"></CuAreaMapCom>
        <div>
          <cu-title title="监测时间"> </cu-title>
          <vxe-form :data="formData">
            <vxe-form-item span="24" field="startTime" title="开始时间">
              <vxe-input
                v-model="formData.startTime"
                type="datetime"
                placeholder="选择开始时间"
                clearable
              />
            </vxe-form-item>
            <vxe-form-item span="24" field="endTime" title="结束时间">
              <vxe-input
                v-model="formData.endTime"
                type="datetime"
                placeholder="选择结束时间"
                clearable
              />
            </vxe-form-item>
          </vxe-form>
        </div>
      </div>
      <monitorSSCom :list="monitorChartList" :chart-data="chartData" @start="monitorStatistic" />
    </el-col>
    <el-col>
      <div class="w-full h-10 flex items-center justify-center bg-[#00b7b7] text-white my-5">
        Step2:电磁环境分析
      </div>
      <envAnalysisCom @start="startAnalysis" />
      <div v-if="showCharts">
        <radiationTypeCom />
        <electFreqWaveDisCom />
        <timeDER />
      </div>
    </el-col>
  </el-row>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
