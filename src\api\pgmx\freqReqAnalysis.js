import request from '@/utils/request'

/** 查询所有电磁频谱需求匹配分析记录 */
export function readAllRecords(data) {
  return request({
    url: '/pgmx/freqReqAnalysis/readAll',
    method: 'post',
    data: data
  })
}

/** 删除记录 */
export function deleteRecord(data) {
  return request({
    url: '/pgmx/freqReqAnalysis/batchDel',
    method: 'delete',
    data: data
  })
}

/** 新增记录 */
export function addRecord(data) {
  return request({
    url: '/pgmx/freqReqAnalysis/add',
    method: 'post',
    data: data
  })
}


/** 添加/修改 通信设备 */
export function saveOrUpdateCMFac(data) {
  return request({
    url: '/pgmx/comm-fac/saveOrUpdate',
    method: 'post',
    data: data
  })
}


/** 根据ID查询单条记录 */
export function readOneRecord(data) {
  return request({
    url: '/pgmx/freqReqAnalysis/readOne',
    method: 'get',
    params: data
  })
}

/** 根据ID更新记录 */
export function updateRecord(data) {
  return request({
    url: '/pgmx/freqReqAnalysis/update',
    method: 'post',
    data: data
  })
}








