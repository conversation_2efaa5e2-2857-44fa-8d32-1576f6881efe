import request from '@/utils/request'

/** 查询信息设施层级树 */
export function queryInfoFacTypeTree(data) {
  return request({
    url: '/pgmx/info-fac/queryInfoFacTypeTree',
    method: 'post',
    data: data
  })
}

/** 通过信息设备类型查询通信设备 */
export function queryInfoByType(data) {
  return request({
    url: '/pgmx/info-fac/queryInfoByType',
    method: 'get',
    params: data
  })
}

/** 删除信息设备 */
export function deleteIMFac(data) {
  return request({
    url: '/pgmx/info-fac/delete',
    method: 'delete',
    data: data
  })
}


/** 添加/修改 信息设备 */
export function saveOrUpdateIMFac(data) {
  return request({
    url: '/pgmx/info-fac/saveOrUpdate',
    method: 'post',
    data: data
  })
}


/** 信息设施分析 */
export function infoAnalyzed(data) {
  return request({
    url: '/pgmx/info-fac/infoAnalyzed',
    method: 'post',
    data: data
  })
}

/** 信息设施分布查询 */
export function infoFacilities(data) {
  return request({
    url: '/pgmx/info-fac/infoFacilities',
    method: 'post',
    data: data
  })
}








