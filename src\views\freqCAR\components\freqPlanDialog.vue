<script setup>
  import { selectDictLabel } from '@/utils/utils.js'
  import { processDictionary } from '@/utils/index.js'

  const props = defineProps({
    title: { type: String, default: '用频计划' },
    planObject: { type: Object, default: () => {} },
    type: {
      type: Number,
      default: 1
    }
  })
  // 对话框可见性
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const loading = ref(false)
  const equipmentModelOptions = ref([]) //装备类型枚举
  const unitOptions = ref([]) //用频单位枚举
  const purposeOptions = ref([]) //用途枚举

  // 表格数据 & 加载状态
  const tableData = ref(
    props.type === 1 ? props.planObject.equipmentList : props.planObject.resolutionList
  )
  const formData = computed(() => ({
    frequencyUsingUnit: selectDictLabel(unitOptions.value, props.planObject.frequencyUsingUnit),
    equipmentModel: selectDictLabel(equipmentModelOptions.value, props.planObject.equipmentModel),
    freqUsageEquipment: props.planObject.freqUsageEquipment
  }))

  const getDictionaryData = async () => {
    processDictionary('pgmx_equipment', equipmentModelOptions)
    processDictionary('pgmx_unit', unitOptions)
    processDictionary('pgmx_purpose', purposeOptions)
  }

  // 确认、取消
  const confirm = () => {
    dialogTableVisible.value = false
  }
  const cancel = () => {
    dialogTableVisible.value = false
  }

  // 挂载时加载初始数据
  onMounted(async () => {
    await getDictionaryData()
  })
</script>

<template>
  <cu-dialog
    v-model="dialogTableVisible"
    :title="title"
    width="880"
    @confirm="confirm"
    @cancel="cancel"
  >
    <div class="mb-4">
      <vxe-form
        :data="formData"
        :title-colon="true"
        :label-config="{ width: '120px', align: 'left' }"
      >
        <vxe-form-item field="unit" title="用频单位" span="24">
          <vxe-input v-model="formData.frequencyUsingUnit" placeholder="请输入用频单位" disabled />
        </vxe-form-item>

        <vxe-form-item field="type" title="装备型号" span="24">
          <vxe-input v-model="formData.equipmentModel" placeholder="请输入装备型号" disabled />
        </vxe-form-item>

        <vxe-form-item field="equipment" title="用频装备" span="24">
          <vxe-input v-model="formData.freqUsageEquipment" placeholder="请输入用频装备" disabled />
        </vxe-form-item>
      </vxe-form>
    </div>

    <div class="font-bold text-[20px] mr-4 mb-2">用频计划</div>
    <vxe-table
      ref="xTable"
      border
      stripe
      height="400"
      size="medium"
      :data="tableData"
      :loading="loading"
      :row-config="{ isCurrent: true, isHover: true }"
      :keep-source="true"
    >
      <vxe-column type="seq" width="60" fixed="left" align="center" />
      <vxe-column field="purpose" title="用途" min-width="160" align="center">
        <template #default="{ row }">
          {{ selectDictLabel(purposeOptions, row.purpose) }}
        </template>
      </vxe-column>

      <vxe-column field="operatingFrequency" title="工作频率" min-width="120" align="center">
      </vxe-column>

      <vxe-column field="emissionBandwidth" title="发射带宽" min-width="120" align="center">
      </vxe-column>

      <vxe-column field="transmissionPower" title="发射功率" min-width="120" align="center">
      </vxe-column>

      <vxe-column
        field="areaStr"
        title="使用区域"
        min-width="200"
        align="center"
        :edit-render="{ defaultSlot: true }"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
      </vxe-column>

      <vxe-column
        field="useStartTime"
        title="用频起始时间"
        min-width="180"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
      </vxe-column>

      <vxe-column
        field="useEndTime"
        title="用频截止时间"
        min-width="180"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
      </vxe-column>

      <vxe-column
        field="createTime"
        title="创建时间"
        min-width="180"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      />
    </vxe-table>
  </cu-dialog>
</template>

<style scoped lang="scss"></style>
