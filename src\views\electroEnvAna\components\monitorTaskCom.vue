<script setup>
  const props = defineProps({
    list: {
      type: Array,
      default: () => []
    }
  })

  const xTable = ref(null)
  const selectedRow = ref(null) // 只存单个对象

  /** 单选框事件 */
  const radioChangeEvent = ({ row }) => {
    selectedRow.value = row
  }

  // 可暴露给父组件使用
  defineExpose({
    getSelectedRow: () => selectedRow.value,
    clearSelection: () => {
      xTable.value?.clearRadioRow()
      selectedRow.value = null
    }
  })
</script>

<template>
  <cu-title title="监测任务" />
  <vxe-table
    ref="xTable"
    border
    stripe
    size="medium"
    min-height="120"
    :data="list"
    :row-config="{ isCurrent: true, isHover: true }"
    @radio-change="radioChangeEvent"
  >
    <vxe-column type="radio" width="90" fixed="left" align="center" />
    <vxe-column field="stationName" title="监测系统" align="center" />
    <vxe-column field="longitude" title="站点经度" width="100" align="center" />
    <vxe-column field="latitude" title="站点纬度" width="100" align="center" />
    <vxe-column field="monitorTime" title="监测时间" align="center" />
  </vxe-table>
</template>
