<script setup>
  // 在这里编写逻辑
  const threshold = ref(0) // 占用度门限

  const startAnalysis = () => {
    // 这里编写启动分析的逻辑
  }
</script>

<template>
  <cu-title title="监测信号分析" />
  <div class="flex items-center mb-4">
    <div class="mr-10">占用度门限</div>
    <div class="flex items-center">
      <vxe-input v-model="threshold" style="width: 80px"></vxe-input>
      <span class="ml-2">dBuv/m</span>
    </div>
    <cu-button content="启动分析" class="ml-5" @click="startAnalysis"></cu-button>
  </div>
  <el-progress :text-inside="true" :stroke-width="16" :percentage="30" status="success" />
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
