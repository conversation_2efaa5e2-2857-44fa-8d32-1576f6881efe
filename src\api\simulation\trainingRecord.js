import request from '@/utils/request'

//训练记录列表查询
export function allTRList(data) {
  return request({
    url: '/train/pageList',
    method: 'post',
    data
  })
}




//训练记录查看
export function checkTRAnswer(data) {
  return request({
    url: '/train/resultOpratortCurrent',
    method: 'post',
    data
  })
}

//训练记录评分
export function getTRAnswer(data) {
  return request({
    url: '/train/resultOpratortAnswer',
    method: 'post',
    data
  })
}

export function teaScore(data) {
  return request({
    url: '/train/corrent',
    method: 'post',
    data
  })
}

export function exportTRAnswer(data) {
  return request({
    url: '/train/export',
    method: 'get',
    params: data,
    responseType: 'blob'
  })
}

export function getTRStatistic(data) {
  return request({
    url: '/signal-statistic/statistic',
    method: 'post',
    data
  })
}

export function getStuTRStatisticList(data) {
  return request({
    url: '/signal-statistic/statisticStuList',
    method: 'post',
    data
  })
}

export function getStuTRStatisticDetail(data) {
  return request({
    url: '/signal-statistic/statisticStuDetail',
    method: 'post',
    data
  })
}




