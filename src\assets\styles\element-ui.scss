.el-button--primary,
.el-button--danger,
.el-button--info,
.el-button--success,
.el-button--warning,
.is-disabled {
  .el-icon,
  .x-icon {
    color: inherit;
  }
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;
    .el-tag {
      margin-right: 0px;
    }
  }
}

.el-dialog {
  transform: none;
  left: 0;
  position: relative;
  margin: 0 auto;
}

.upload-container {
  .el-upload {
    width: 100%;
    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

.el-dropdown-menu {
  a {
    display: block;
  }
}

.el-range-editor.el-input__inner {
  display: inline-flex !important;
}

.el-range-separator {
  box-sizing: content-box;
}

.el-menu--collapse > div > .el-submenu > .el-submenu__title .el-submenu__icon-arrow {
  display: none;
}

.el-dropdown .el-dropdown-link {
  color: var(--el-color-primary) !important;
}

.el-checkbox__input.is-checked + .el-checkbox__label {
  color: var(--text-color) !important;
}

.el-radio__input.is-checked + .el-radio__label {
  color: var(--text-color) !important;
}

.el-select {
  width: var(--cu-select-width);
  height: var(--cu-select-height);
  --el-color-primary: var(--sys-text-color) !important;
  .el-input__wrapper {
    width: var(--cu-select-width) !important;
    height: var(--cu-select-height) !important;
    &.is-focus {
      border: none !important;
    }
  }
}

.el-select-dropdown,
.el-select-dropdown__wrap {
  min-width: calc(var(--cu-select-width) - 5px) !important;
  // border-radius: 6px !important;
}

.el-input__wrapper,
.el-select__wrapper {
  // width: var(--cu-select-width);
  height: var(--cu-select-height);
  &.is-focus {
    border: 1px solid #175eff;
    box-shadow: none !important;
  }
}

.el-select-dropdown__item {
  height: var(--cu-select-height) !important;
  line-height: var(--cu-select-height) !important;
  font-family: Microsoft YaHei;
  &.selected {
    color: var(--sys-text-color) !important;
    font-weight: 400 !important;
    background: #f6f9ff;
  }
  &.hover {
    background: #f6f9ff;
  }
}

.el-date-editor.el-input,
.el-date-editor.el-input__wrapper {
  height: var(--cu-select-height) !important;
  font-family: Microsoft YaHei;
  &.is-active {
    border: 1px solid #175eff;
    box-shadow: none !important;
  }
}

.el-button + .el-dropdown,
.el-dropdown + .el-button {
  margin-left: 12px;
}

.el-input__prefix-inner {
  align-items: center;
}

.el-drawer__header {
  margin-bottom: 1rem !important;
  &::before {
    top: 50%;
  }
}

.el-checkbox {
  height: var(--cu-select-height) !important;
}

/**
 * tab切样式调整
 */
// 标签页背景色
.el-tabs--border-card > .el-tabs__header {
  background-color: #ffffff !important;
}

// 标签页标题居中
.el-tabs--left .el-tabs__item.is-left,
.el-tabs--right .el-tabs__item.is-left {
  justify-content: center !important;
}

.el-tabs--border-card > .el-tabs__header .el-tabs__item {
  color: #333333 !important;
  font-size: 16px;
  border-left: 0;
  border-right: 0;
  &:hover {
    color: #00603b !important;
    background-color: #cceddc;
  }
  &.is-active {
    color: #fff !important;
    font-weight: bold;
    border-left: 0;
    border-right: 0;
    background-color: #00603b !important;
  }
}

// 表单label标题居中
.el-form-item__label {
  font-weight: bold !important;
}
.el-form-item__label-wrap,
.el-form--inline .el-form-item {
  align-items: center !important;
}

/**
 * 消息提示样式调整
 */
.el-message {
  min-width: 300px;
  height: 40px !important;
  border-radius: 2px !important;
}
.el-message--info {
  --el-message-bg-color: #eeeeee !important;
  --el-message-border-color: transparent !important;
  --el-message-text-color: #000000 !important;
}

.el-message--success {
  --el-message-bg-color: #2b9e16 !important;
  --el-message-border-color: transparent !important;
  --el-message-text-color: #ffffff !important;
}
.el-message-error {
  --el-message-bg-color: #ff0000 !important;
  --el-message-border-color: transparent !important;
  --el-message-text-color: #ffffff !important;
}
.el-message--warning {
  --el-message-bg-color: #ef7100 !important;
  --el-message-border-color: transparent !important;
  --el-message-text-color: #ffffff !important;
}
