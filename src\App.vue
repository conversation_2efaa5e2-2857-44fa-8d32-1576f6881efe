<template>
  <el-config-provider :locale="zhCn">
    <router-view class="fixed-width-container" />
  </el-config-provider>
</template>

<script setup>
  import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
  import useSettingsStore from '@/store/modules/settings'
  import { handleThemeStyle } from '@/utils/theme'

  onMounted(() => {
    nextTick(() => {
      handleThemeStyle(useSettingsStore().theme)
    })
  })
</script>

<style lang="scss"></style>
