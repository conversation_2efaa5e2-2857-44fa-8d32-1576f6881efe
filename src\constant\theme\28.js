// theme-green.js - 完整的绿色主题实现

export default {
  install(VXETable) {
    // 1. 通过VXETable.setup设置全局默认配置
    VXETable.setup({
      // 基础配置
      size: 'medium',
      zIndex: 100,

      // 表格配置
      table: {
        border: true,
        stripe: true,
        rowHeight: 30,
        rowConfig: {
          isCurrent: true,
          isHover: true
        },
        // 表格样式配置
        headerBgColor: '#00603b',
        headerColor: '#ffffff',
        bodyRowColor: '#333333',
        bodyRowOddBgColor: '#ffffff',
        bodyRowEvenBgColor: '#d3eadd',
        bodyRowHoverBgColor: '#d3eadd',
        bodyRowCurrentBgColor: '#d3eadd',
        // 单元格边框颜色
        columnBorderColor: '#ccc'
      },

      // 复选框配置
      checkbox: {
        checkboxRangeStartClassName: 'vxe-custom-checked-range-start',
        checkboxRangeEndClassName: 'vxe-custom-checked-range-end',
        // 复选框选中颜色
        checkedColor: '#00603b'
      },

      // 单选框配置
      radio: {
        // 单选框选中颜色
        checkedColor: '#00603b'
      },

      // 输入框配置
      input: {
        clearable: true,
        // 输入框激活边框颜色
        focusBorderColor: '#00603b'
      },

      // 下拉框配置
      select: {
        optionMaxHeight: 300,
        // 下拉选项悬停颜色
        optionHoverBgColor: '#cceddc',
        optionHoverColor: '#333333',
        // 下拉选项选中颜色
        optionSelectedBgColor: '#00603b',
        optionSelectedColor: '#ffffff'
      },

      // 分页配置
      pager: {
        // 分页按钮激活颜色
        activeBgColor: '#00603b',
        activeColor: '#ffffff',
        // 分页悬停颜色
        hoverBgColor: '#cceddc'
      },

      // 弹窗配置
      modal: {
        // 弹窗标题颜色
        headerColor: '#00603b',
        // 无边框
        border: false
      },
      loading: {
        color: '#00603b',  // 修改为你想要的颜色，如蓝色
        background: 'rgba(0, 0, 0, 0.2)' // 可选：修改背景遮罩颜色
      }
    })

    // 2. 定义CSS变量，注入到document
    const cssVars = `
      :root {
        /* 主题颜色变量 */
        --vxe-primary-color: #00603b;
        --vxe-table-header-background-color: #00603b;
        --vxe-table-header-color: #ffffff;
        --vxe-table-body-odd-background-color: #ffffff;
        --vxe-table-body-even-background-color: #d3eadd;
        --vxe-table-row-hover-background-color: #d3eadd;
        --vxe-table-row-current-background-color: #d3eadd;
        --vxe-table-row-color: #333333;
        --vxe-table-border-color: #ccc;

        /* 表单元素变量 */
        --vxe-input-border-radius: 0;
        --vxe-input-height: var(--cu-select-height, 30px);
        --vxe-input-background-color: var(--background-color, #ffffff);
        --vxe-input-color: var(--chart-text-color, #333333);
        --vxe-input-border-color: #ccc;
        --vxe-input-focus-border-color: #00603b;

        /* 复选框变量 */
        --vxe-checkbox-checked-color: #00603b;
        --vxe-checkbox-indeterminate-color: #00603b;

        /* 下拉选项变量 */
        --vxe-select-option-hover-background-color: #cceddc;
        --vxe-select-option-hover-color: #333333;
        --vxe-select-option-selected-background-color: #00603b;
        --vxe-select-option-selected-color: #ffffff;

        /* 分页变量 */
        --vxe-pager-background-color: var(--background-color, #ffffff);
        --vxe-pager-color: var(--chart-text-color, #333333);
        --vxe-pager-item-active-background-color: #00603b;
        --vxe-pager-item-active-color: #ffffff;


        --vxe-ui-font-primary-color: #00603b;
      }
    `;

    // 3. 创建<style>标签，插入CSS变量
    const styleEl = document.createElement('style');
    styleEl.textContent = cssVars;
    document.head.appendChild(styleEl);

    // 4. 自定义主题样式，补充API无法完全覆盖的部分
    const customStyles = `
      /* 表格样式补充 */
      .vxe-table {
        /* 表头样式 */
        .vxe-table--header {
          color: #ffffff;
          background-color: #00603b;
        }

        /* 表格主体样式 */
        .vxe-table--body {
          color: #333333;

          /* 斑马纹效果 */
          .vxe-body--row {
            /* 奇数行背景色 */
            &:nth-child(odd) {
              background-color: #ffffff !important;
              > .vxe-body--column {
                background-color: #ffffff !important;
              }
            }

            /* 偶数行背景色 */
            &:nth-child(even) {
              background-color: #d3eadd !important;
              > .vxe-body--column {
                background-color: #d3eadd !important;
              }
            }

            /* 悬停效果 */
            &.row--hover {
              background-color: #d3eadd !important;
              > .vxe-body--column {
                background-color: #d3eadd !important;
              }
            }

            /* 当前行效果 */
            &.row--current {
              background-color: #d3eadd !important;
              > .vxe-body--column {
                background-color: #d3eadd !important;
              }
            }
          }
        }

        /* 复选框样式 */
        .vxe-cell--checkbox {
          &.hover {
            border-color: #00603b !important;
          }
        }
      }

      /* 弹出框样式 */
      .vxe-modal--box {
        border: 0 !important;
      }

      /* 工具栏和分页样式 */
      .vxe-toolbar,
      .vxe-page,
      .vxe-pager--goto,
      .vxe-pager--num-btn,
      .vxe-pager--next-btn,
      .vxe-pager--jump-next,
      .vxe-pager--jump-prev,
      .vxe-pager--prev-btn {
        background-color: var(--background-color) !important;
        color: var(--chart-text-color) !important;
      }

      /* 筛选和下拉框样式 */
      .vxe-table--filter-wrapper,
      .vxe-select-option--wrapper {
        background-color: var(--background-color) !important;
        border-color: #000 !important; /* 外部边框黑色 */
        color: var(--chart-text-color) !important;

        .vxe-table--filter-body > li:hover,
        .vxe-table--filter-header > li:hover {
          background-color: var(--scrollbar-color) !important;
          color: var(--chart-text-color) !important;
        }
      }

      /* 输入框样式 */
      .vxe-input {
        border-radius: 0 !important;
        height: var(--cu-select-height) !important;
        line-height: var(--cu-select-height) !important;

        .vxe-input--inner {
          background-color: var(--background-color) !important;
          color: var(--chart-text-color) !important;
          border-color: #fff !important; /* 内部边框白色 */
        }
      }

      /* 输入框激活状态 */
      .vxe-input.is--active {
        border: 1px solid #00603b !important;
      }

      /* input可删除鼠标激活（被按下）时 */
      .vxe-input--clear-icon:active,
      .vxe-input--clear-icon.is--active {
        color: #00603b !important; /* 高亮绿色 */
      }

      /* input可删除激活时样式 */
      .vxe-select.is--active:not(.is--filter) > .vxe-input {
        border-color: #00603b !important;
      }

      /* vxe-textarea 的基本样式 */
      .vxe-textarea {
        .vxe-textarea--inner {
          border-radius: 0 !important;
          background-color: var(--background-color) !important;
          color: var(--chart-text-color) !important;

          &:focus {
            border-color: #00603b !important; /* 聚焦时边框颜色 */
            background-color: var(--background-color) !important;
            color: var(--chart-text-color) !important;
          }
        }
      }

      /* 下拉选项样式 */
      .vxe-select--panel {
        .vxe-optgroup--title,
        .vxe-select-option {
          height: var(--cu-select-height) !important;
          line-height: var(--cu-select-height) !important;
          border-color: #fff !important; /* 内部边框白色 */
        }

        .vxe-select-option {
          max-width: 100% !important;

          /* 下拉框鼠标悬浮样式 */
          &:not(.is--disabled).is--hover {
            background-color: #cceddc !important;
            color: #333 !important;
          }

          /* 下拉框选中样式 */
          &.is--selected {
            background-color: #00603b !important;
            color: #fff !important;

            &.is--hover {
              background-color: #00603b !important;
              color: #fff !important;
            }
          }
        }
      }

      /* 复选框选中状态 */
      .is--checked,
      .is--indeterminate {
        &.vxe-checkbox,
        &.vxe-cell--checkbox,
        &.vxe-custom--option,
        &.vxe-export--panel-column-option,
        &.vxe-table--filter-option,
        .vxe-checkbox--icon {
          color: #00603b !important;

          &:hover {
            color: #00603b !important;
          }
        }
      }

      /* 分页选中样式 */
      .vxe-pager--num-btn.is--active {
        background-color: #00603b !important;
        color: #ffffff !important;
        border:none !important

      }
      .vxe-pager--num-btn{
         box-shadow:none !important;
      }

      /* loading加载状态 */
      .vxe-loading--icon,.vxe-loading--text{
        color:#00603b !important;
      }
    `;

    // 5. 创建<style>标签，插入自定义样式
    const customStyleEl = document.createElement('style');
    customStyleEl.textContent = customStyles;
    document.head.appendChild(customStyleEl);

    // 6. 可以额外注册一些主题相关的渲染器或格式化器
    VXETable.renderer.add('greenThemeCell', {
      renderDefault: function (h, cellRender, params) {
        // 可以自定义单元格渲染
        return [
          h('span', {
            style: {
              color: params.row.status === 'success' ? '#00603b' : '#333333'
            }
          }, params.row[params.column.property])
        ]
      }
    })
  }
}

// 使用方式：
// 在main.js中：
// import Vue from 'vue'
// import VXETable from 'vxe-table'
// import GreenTheme from './theme-green'
//
// Vue.use(VXETable)
// VXETable.use(GreenTheme)