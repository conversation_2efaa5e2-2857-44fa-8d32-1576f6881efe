<script setup>
  import { ref, onMounted, nextTick } from 'vue'
  import 'ol/ol.css'
  import TileLayer from 'ol/layer/Tile'
  import VectorLayer from 'ol/layer/Vector'
  import VectorSource from 'ol/source/Vector'
  import XYZ from 'ol/source/XYZ'
  import { Map, View } from 'ol'
  import { Circle as CircleStyle, Fill, Text, Stroke, Style, Icon } from 'ol/style'
  import Overlay from 'ol/Overlay'
  import Cluster from 'ol/source/Cluster'
  import Feature from 'ol/Feature'
  import Point from 'ol/geom/Point'
  import gcj02Mecator from '@/utils/gcj02Mecator.js'
  import FullScreen from 'ol/control/FullScreen' // 导入全屏控件
  import iconSrc from '@/assets/images/signal.png'
  import { ElMessage } from 'element-plus'

  // 地图实例
  const map = ref(null)

  // 聚合源和图层
  let clusterSource, clusterLayer

  // Tooltip Overlay
  let tooltipOverlay, tooltipElement

  // 示例原始点数据
  const baseData = [
    { name: 'A地点', coord: [118.8, 32.0] },
    { name: 'B地点', coord: [118.81, 32.01] },
    { name: 'C地点', coord: [118.82, 32.02] },
    { name: 'D地点', coord: [118.83, 32.03] },
    { name: 'E地点', coord: [118.84, 32.04] },
    { name: 'F地点', coord: [118.85, 32.05] },
    { name: 'G地点', coord: [118.86, 32.06] },
    { name: 'H地点', coord: [118.87, 32.07] },
    { name: 'I地点', coord: [118.88, 32.08] },
    { name: 'J地点', coord: [118.89, 32.09] }
  ]

  // 生成100个点的函数
  function generatePoints() {
    const result = []
    const baseLength = baseData.length
    for (let i = 0; i < 100; i++) {
      const baseIndex = i % baseLength
      const basePoint = baseData[baseIndex]
      // 对坐标进行较大范围的随机偏移
      const newCoord = [
        basePoint.coord[0] + (Math.random() - 0.5) * 1,
        basePoint.coord[1] + (Math.random() - 0.5) * 1
      ]
      const newName = `${basePoint.name}-${i}`
      result.push({ name: newName, coord: newCoord })
    }
    return result
  }

  const rawData = generatePoints()

  onMounted(async () => {
    await nextTick()
    initMap()
    initClusterLayer()
    loadData()
    addMapListeners()
  })

  function initMap() {
    // 创建高德地图图层
    const tileLayer = new TileLayer({
      source: new XYZ({
        projection: gcj02Mecator,
        url: 'http://wprd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&style=7&x={x}&y={y}&z={z}'
      })
    })

    map.value = new Map({
      target: 'map',
      layers: [tileLayer],
      view: new View({
        projection: 'EPSG:4326',
        center: [118.815588, 32.032081],
        zoom: 14,
        maxZoom: 18
      }),
      // 添加全屏控件
      controls: [
        new FullScreen() // 启用全屏按钮
      ]
    })

    // 创建并添加 tooltip overlay
    tooltipElement = document.createElement('div')
    tooltipElement.className = 'tooltip'
    tooltipOverlay = new Overlay({
      element: tooltipElement,
      offset: [10, -10],
      positioning: 'bottom-left',
      stopEvent: false
    })
    map.value.addOverlay(tooltipOverlay)
  }

  /**
   * 初始化聚合图层
   */
  function initClusterLayer() {
    const vectorSrc = new VectorSource()
    clusterSource = new Cluster({
      distance: 40,
      source: vectorSrc
    })

    clusterLayer = new VectorLayer({
      source: clusterSource,
      style: clusterStyleFunction
    })
    map.value.addLayer(clusterLayer)
  }

  /**
   * 加载原始数据
   */
  function loadData() {
    const features = rawData.map(item => {
      const f = new Feature({
        name: item.name,
        geometry: new Point(item.coord)
      })
      return f
    })
    // 载入底层 vector source
    clusterSource.getSource().addFeatures(features)
  }

  /**
   * 更新聚合距离
   */
  function updateClusterDistance() {
    const view = map.value.getView()
    const resolution = view.getResolution()
    // 让聚合半径在 20px ~ 60px 之间动态
    const px = Math.max(20, Math.min(60, 40 * (14 / view.getZoom())))
    clusterSource.setDistance(px)
  }

  /**
   * 添加地图事件监听器
   * - 缩放结束后更新聚合距离
   * - 鼠标移动显示 tooltip
   * - 点击聚合点自动放大
   * - 鼠标离开隐藏 tooltip
   * - 点击单点显示消息
   */
  function addMapListeners() {
    // 缩放结束后更新聚合距离
    map.value.getView().on('change:resolution', updateClusterDistance)

    // 鼠标移动显示 tooltip
    map.value.on('pointermove', evt => {
      const pixel = evt.pixel
      const feat = map.value.getFeaturesAtPixel(pixel)?.[0]
      if (feat) {
        map.value.getTargetElement().style.cursor = 'pointer'
        const cluster = feat.get('features')
        if (cluster.length > 1) {
          tooltipElement.innerHTML = `聚合：${cluster.length}点`
        } else {
          tooltipElement.innerHTML = cluster[0].get('name')
        }
        tooltipOverlay.setPosition(evt.coordinate)
        tooltipElement.style.display = 'block'
      } else {
        map.value.getTargetElement().style.cursor = ''
        tooltipElement.style.display = 'none'
      }
    })

    // 点击聚合点自动放大
    map.value.on('singleclick', evt => {
      const feat = map.value.getFeaturesAtPixel(evt.pixel)?.[0]
      if (!feat) return
      const cluster = feat.get('features')
      if (cluster.length > 1) {
        // 放大一级并居中到该点
        const view = map.value.getView()
        view.animate({
          zoom: view.getZoom() + 1,
          center: evt.coordinate,
          duration: 300
        })
      } else {
        ElMessage.success(`点击单点：${cluster[0].get('name')}`)
      }
    })
  }

  /**
   *  聚合点或单点的样式函数
   *  - 聚合点：半径随点数缩放，文字显示点数
   *  - 单点：图标加文字
   * @param feature 聚合点或单点
   */
  function clusterStyleFunction(feature) {
    const features = feature.get('features')
    const size = features.length
    if (size > 1) {
      // 半径随点数缩放
      const radius = 10 + Math.min(size, 50) * 0.5
      return new Style({
        image: new CircleStyle({
          radius,
          fill: new Fill({ color: '#00aaff' }),
          stroke: new Stroke({ color: '#fff', width: 2 })
        }),
        text: new Text({
          text: size.toString(),
          fill: new Fill({ color: '#fff' }),
          font: 'bold 12px sans-serif',
          offsetY: -1
        })
      })
    } else {
      const single = features[0]
      // 普通单点用图标加文字
      return new Style({
        image: new Icon({
          src: iconSrc,
          scale: 0.15,
          anchor: [0.5, 1]
        }),
        text: new Text({
          text: single.get('name'),
          font: '12px sans-serif',
          fill: new Fill({ color: '#000' }),
          stroke: new Stroke({ color: '#fff', width: 2 }),
          offsetY: -25
        })
      })
    }
  }
</script>

<template>
  <div id="map" class="w-full"></div>
</template>

<style scoped>
  #map {
    height: calc(100vh - 485px);
    width: 100%;
  }

  /* Tooltip 样式 */
  .tooltip {
    position: absolute;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid #ccc;
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 4px;
    pointer-events: none;
    white-space: nowrap;
    display: none;
  }
</style>
