<script setup>
  import useList from '@/api/tool/filemanage/tableFunctionPro'
  import { downloadFile } from '@/utils/fileDownload.js'
  import addTaskDialog from './components/addTaskDialog.vue'
  import demandEdit from '@/views/demandAccess/demandEdit/index.vue'
  import { addFQTask, readAllFreqProtection, deleteTask } from '@/api/pgmx/freqProtection.js'
  import { ElMessage } from 'element-plus'

  const filterOption = ref({
    startFreq: '', //起始频率
    endFreq: '', //结束频率
    startTime: '', //开始时间
    endTime: '', //结束时间
    taskName: '' //任务名称
  })
  const xTable = ref(null)
  const addTaskDialogRef = ref(null)
  const addTaskDialogVisible = ref(false) //新增任务弹窗
  const editDialogVisible = ref(false) //编辑任务弹窗
  const currentRowValue = ref(null) //当前行数据
  const {
    list,
    loading,
    curPage,
    size,
    total,
    timeSearch,
    deleteCurrentLine,
    deleteAll,
    selectChangeEvent
  } = useList(readAllFreqProtection, deleteTask, filterOption.value, xTable)

  const clearName = () => {
    filterOption.taskName = ''
    timeSearch(filterOption.value)
  }

  /** 导出 */
  const exportRecord = async () => {
    await exportTRAnswer().then(res => {
      downloadFile(res, '信号模拟任务.xlsx')
    })
  }

  /** 编辑按钮 */
  const handleEdit = row => {
    currentRowValue.value = row
    editDialogVisible.value = true
  }

  /** 新增按钮 */
  const handleAdd = () => {
    addTaskDialogVisible.value = true
  }

  /**
   * 新增用频保护需求
   */
  const addTask = async query => {
    await addFQTask(query)
      .then(res => {
        if (res.code === 200) {
          ElMessage.success('添加成功')
          timeSearch()
        } else {
          ElMessage.error(res.msg)
        }
      })
      .finally(() => {
        addTaskDialogVisible.value = false
        addTaskDialogRef.value.resetForm()
      })
  }

  onMounted(async () => {})
</script>
<template>
  <vxe-toolbar class="vxe-toolbar">
    <template #buttons>
      <div class="flex justify-between items-center gap-2">
        <div class="text-sm font-medium min-w-[80px]">保护频段</div>
        <vxe-input
          v-model="filterOption.taskName"
          placeholder="起始频段"
          clearable
          type="number"
          min="0"
        >
          <template #suffix> MHz </template>
        </vxe-input>
        <p class="mx-2"> 至 </p>
        <vxe-input
          v-model="filterOption.createBy"
          placeholder="结束频段"
          clearable
          type="number"
          min="0"
        >
          <template #suffix> MHz </template>
        </vxe-input>
        <div class="text-sm font-medium min-w-[80px]">ZZ任务</div>
        <vxe-input
          v-model="filterOption.taskName"
          placeholder="任务名称"
          clearable
          @clear="clearName"
        />
      </div>
      <div class="flex justify-between items-center gap-2">
        <div class="text-sm font-medium min-w-[80px]">保护时间</div>
        <vxe-input
          v-model="filterOption.startTime"
          type="datetime"
          format="YYYY-MM-DD HH:mm:ss"
          transfer
          placeholder="请选择开始时间"
          clearable
        />
        <p class="mx-2"> 至 </p>
        <vxe-input
          v-model="filterOption.endTime"
          type="datetime"
          format="YYYY-MM-DD HH:mm:ss"
          transfer
          placeholder="请选择结束时间"
          clearable
        />
      </div>
      <div>
        <cu-button content="查询" @click="timeSearch(filterOption)" />
        <cu-button content="新增" @click="handleAdd" />
        <cu-button content="导出" />
        <cu-button content="导入" />
      </div>
    </template>
  </vxe-toolbar>
  <vxe-table
    ref="xTable"
    border
    stripe
    size="medium"
    height="640"
    :data="list"
    :loading="loading"
    :column-config="{ resizable: true }"
    :row-config="{ isCurrent: true, isHover: true }"
  >
    <vxe-column type="seq" width="50" fixed="left" align="center" />
    <vxe-column
      field="taskName"
      title="ZZ任务"
      min-width="120"
      fixed="left"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column
      field="freqBand"
      title="保护频段"
      min-width="120"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column
      field="protectObject"
      title="保护对象"
      min-width="120"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column
      field="protectTime"
      title="保护时间"
      min-width="180"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column
      field="protectArea"
      title="保护区域"
      min-width="120"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column
      field="remarks"
      title="备注"
      min-width="120"
      align="center"
      show-header-overflow
      show-overflow="title"
      show-footer-overflow
    />
    <vxe-column field="createTime" title="创建时间" min-width="180" align="center" />
    <vxe-column field="操作" title="操作" min-width="100" fixed="right" align="center">
      <template #default="{ row }">
        <cu-button content="编辑" type="info" @click="handleEdit(row)" />
        <cu-button content="删除" type="info" @click="deleteCurrentLine(row)" />
      </template>
    </vxe-column>
  </vxe-table>
  <!-- 分页 -->
  <p>
    <vxe-pager
      v-model:current-page="curPage"
      v-model:page-size="size"
      class="vxe-page"
      perfect
      :total="total"
      :page-sizes="[10, 20, 50, 100, 200, 500]"
      :layouts="[
        'PrevJump',
        'PrevPage',
        'Number',
        'NextPage',
        'NextJump',
        'Sizes',
        'FullJump',
        'Total'
      ]"
    />
  </p>

  <addTaskDialog
    ref="addTaskDialogRef"
    v-if="addTaskDialogVisible"
    v-model="addTaskDialogVisible"
    @addFQTask="addTask"
  />
  <demandEdit
    v-if="editDialogVisible"
    v-model="editDialogVisible"
    :currentRowValue="currentRowValue"
    @update-list-event="timeSearch(filterOption)"
  />
</template>

<style lang="scss" scoped>
  :deep(.vxe-buttons--wrapper) {
    gap: 10px;
  }
</style>
