<template>
  <div>
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="'#00603b'"
        :text-color="'#fff'"
        :unique-opened="true"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
          :class="{ 'active-item': isActiveRoute(route.path) }"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
  import Logo from './Logo'
  import SidebarItem from './SidebarItem'
  import useAppStore from '@/store/modules/app'
  import useSettingsStore from '@/store/modules/settings'
  import usePermissionStore from '@/store/modules/permission'
  import { useRoute } from 'vue-router'

  const route = useRoute()
  const appStore = useAppStore()
  const settingsStore = useSettingsStore()
  const permissionStore = usePermissionStore()
  const showLogo = computed(() => settingsStore.sidebarLogo)
  const sidebarRouters = computed(() => permissionStore.sidebarRouters)
  const isCollapse = computed(() => !appStore.sidebar.opened)

  const activeMenu = computed(() => {
    const { meta, path } = route
    // 如果设置了 activeMenu，则高亮该路径
    if (meta.activeMenu) {
      return meta.activeMenu
    }
    return path
  })

  const isActiveRoute = path => {
    return route.path === path
  }
</script>
