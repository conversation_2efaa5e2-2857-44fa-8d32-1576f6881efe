<script setup>
  const emit = defineEmits(['addFQTask'])
  const formRef = ref(null)
  const formData = ref({
    taskName: '',
    description: '',
    remarks: ''
  })
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const formRules = ref({
    taskName: [{ required: true, message: '必须填写' }]
  })

  const resetForm = () => {
    formData.value = {
      taskName: '',
      description: '',
      remarks: ''
    }
  }

  const confirm = async () => {
    if (formRef.value) {
      const errMaps = await formRef.value.validate()
      if (!errMaps) {
        emit('addFQTask', formData.value)
      }
    }
  }

  const cancel = () => {
    dialogTableVisible.value = false
    resetForm()
  }

  defineExpose({ resetForm })
</script>
<template>
  <cu-dialog
    v-model="dialogTableVisible"
    title="用频需求编辑"
    width="460px"
    min-height="500px"
    @confirm="confirm"
    @cancel="cancel"
  >
    <vxe-form
      ref="formRef"
      :data="formData"
      :rules="formRules"
      :title-colon="true"
      :title-width="100"
      title-align="right"
    >
      <vxe-form-item title="任务名称" field="taskName" span="24" :required="true">
        <vxe-input v-model="formData.taskName" placeholder="请输入任务名称" clearable />
      </vxe-form-item>
      <vxe-form-item title="任务描述" field="description" span="24">
        <vxe-textarea
          v-model="formData.description"
          :autosize="{ minRows: 5, maxRows: 8 }"
          className="textarea-custom"
          placeholder="请输入任务描述"
          clearable
        />
      </vxe-form-item>
      <vxe-form-item title="备注" field="remarks" span="24">
        <vxe-textarea
          v-model="formData.remarks"
          :autosize="{ minRows: 3, maxRows: 5 }"
          placeholder="请输入备注"
          clearable
        />
      </vxe-form-item>
    </vxe-form>
  </cu-dialog>
</template>

<style lang="scss" scoped>
  :deep(.vxe-buttons--wrapper) {
    gap: 10px;
  }
  .textarea-custom {
    border-color: #00603b !important;
  }
</style>
