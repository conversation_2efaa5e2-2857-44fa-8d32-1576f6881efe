<script setup>
  const equipConstructInfo = defineModel('equipConstructInfo', { type: Array, default: false })

  const transmitterOptions = [
    { label: '发射机1', value: 1 },
    { label: '发射机2', value: 2 },
    { label: '发射机3', value: 3 }
  ]
  const receiverOptions = [
    { label: '接收机1', value: 1 },
    { label: '接收机2', value: 2 },
    { label: '接收机3', value: 3 }
  ]

  const antennaOptions = [
    { label: '天线1', value: 1 },
    { label: '天线2', value: 2 },
    { label: '天线3', value: 3 }
  ]

  // 选项映射对象
  const optionsMap = {
    transmitter: transmitterOptions,
    receiver: receiverOptions,
    antenna: antennaOptions
  }

  /**
   * 将值映射为标签
   * @param {Array} values - 值数组
   * @param {string} field - 字段名
   * @returns {string} - 标签字符串
   */
  const mapValuesToLabels = (values, field) => {
    const options = optionsMap[field] || []
    return values
      .map(value => options.find(option => option.value === value)?.label)
      .filter(Boolean)
      .join('，')
  }
</script>

<template>
  <div class="w-full my-4">
    <div class="my-2">装备构成信息</div>
    <vxe-table
      border
      :show-header="false"
      :data="equipConstructInfo"
      :row-config="{ isCurrent: true, isHover: true }"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
    >
      <!-- 参数名列 -->
      <vxe-column field="label" title="参数名" width="120" align="center"></vxe-column>

      <!-- 参数值列 -->
      <vxe-column field="value" title="参数值" align="center" :edit-render="{}">
        <template #default="{ row }">
          <span>{{ mapValuesToLabels(row.value, row.field) || '-' }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.value" multiple placeholder="请选择">
            <vxe-option
              v-for="item in optionsMap[row.field] || []"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></vxe-option>
          </vxe-select>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<style lang="scss" scoped>
  .vxe-table {
    border-radius: 4px;
    overflow: hidden;

    .vxe-cell {
      padding: 8px 12px;
    }

    .vxe-table--edit-cell {
      background-color: #f5f7fa;
    }
  }
</style>
