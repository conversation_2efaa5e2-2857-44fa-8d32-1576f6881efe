import request from '@/utils/request'

//信号模拟任务保存
export function saveSignalSimulationTask(data) {
  return request({
    url: '/signal-simulation-task/saveOrUpdate',
    method: 'post',
    data
  })
}

//信号模拟任务列表查询
export function allSignalSimulationTaskList(data) {
  return request({
    url: '/signal-simulation-task/page',
    method: 'post',
    data
  })
}

export function deleteSignalSimulationTask(data) {
  return request({
    url: '/signal-simulation-task/batchDel',
    method: 'post',
    data
  })
}

export function getSimulationTaskDetail(params) {
  return request({
    url: '/signal-simulation-task/selectOne',
    method: 'get',
    params
  })
}