import request from '@/utils/request'

/** 冲突分析 */
export function conflictAnalyze(data) {
  return request({
    url: '/pgmx/freqResolution/conflictAnalyze',
    method: 'post',
    data: data
  })
}

/** 冲突分析分页结果查询 */
export function analyzeResolutionResult(data) {
  return request({
    url: '/pgmx/freqResolution/analyzeResolutionResult',
    method: 'get',
    params: data
  })
}

/** 启用本用频方案 */
export function activatePlan(data) {
  return request({
    url: '/pgmx/freqResolution/activatePlan',
    method: 'post',
    data: data
  })
}

/** 冲突消解 */
export function conflictResolution(data) {
  return request({
    url: '/pgmx/freqResolution/conflictResolution',
    method: 'post',
    data: data
  })
}

/** 保存冲突消解结果 */
export function saveResolution(data) {
  return request({
    url: '/pgmx/freqResolution/saveResolution',
    method: 'get',
    params: data
  })
}

