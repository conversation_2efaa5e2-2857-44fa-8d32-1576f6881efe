// usePlayback.js
import { computed } from 'vue';
import { createPlaybackStore } from '@/store/modules/playback'; // 引入工厂函数

export function usePlayback(pollingFunction, uniqueId) {
  // 使用唯一的 ID 生成独立的 store 实例
  const usePlaybackStore = createPlaybackStore(uniqueId);
  const playbackStore = usePlaybackStore();

  // Computed properties for playback state
  const isPlaying = computed(() => playbackStore.isPlaying);
  const startOffset = computed(() => playbackStore.startOffset);
  const maxOffset = computed(() => playbackStore.maxOffset);
  const playBackTime = computed({
    get: () => playbackStore.playBackTime,
    set: (value) => {
      playbackStore.playBackTime = value;
    },
  });
  const isRequestInProgress = computed(() => playbackStore.isRequestInProgress);
  const isFetching = computed(() => playbackStore.isFetching);
  const isFinished = computed(() => playbackStore.isFinished);
  const isInfinite = computed({
    get: () => playbackStore.isInfinite,
    set: (value) => {
      playbackStore.isInfinite = value;
    },
  });

  // Actions
  const togglePlay = () => {
    playbackStore.togglePlay(pollingFunction);
  };

  const play = () => {
    playbackStore.play(pollingFunction);
  };

  const stopPlay = () => {
    playbackStore.stopPlay();
  };

  const fastBack = async () => {
    await playbackStore.fastBack(pollingFunction);
  };

  const backByStep = async () => {
    await playbackStore.backByStep(pollingFunction);
  };

  const forwardByStep = async () => {
    await playbackStore.forwardByStep(pollingFunction);
  };

  const refreshPlay = async () => {
    await playbackStore.refreshPlay(pollingFunction);
  };

  const setStartOffset = (value) => {
    playbackStore.setStartOffset(value);
  };

  const setMaxOffset = (value) => {
    playbackStore.setMaxOffset(value);
  };

  const setDragOffset = (value) => {
    playbackStore.setDragOffset(value);
  };

  const setCurrentOffset = (value) => {
    playbackStore.setCurrentOffset(value);
  };

  const clearTimer = () => {
    playbackStore.clearTimer();
  };

  const resetPlayback = () => {
    playbackStore.resetPlayback();
  };

  return {
    isPlaying,
    startOffset,
    maxOffset,
    playBackTime,
    isRequestInProgress,
    isFetching,
    isFinished,
    isInfinite,
    togglePlay,
    play,
    stopPlay,
    fastBack,
    backByStep,
    forwardByStep,
    refreshPlay,
    setStartOffset,
    setMaxOffset,
    clearTimer,
    resetPlayback,
    setDragOffset,
    setCurrentOffset,
    playbackStore
  };
}
