<script setup>
  import { ref, onMounted, nextTick, onUnmounted } from 'vue'
  import Highcharts from 'highcharts'
  import highcharts3d from 'highcharts/highcharts-3d'
  import { speResStatistics } from '@/api/pgmx/speResStatistics.js'
  import { queryTaskOption } from '@/api/pgmx/useFreqTask.js'
  import { VxeUI } from 'vxe-table'
  import { ElMessage } from 'element-plus'

  highcharts3d(Highcharts)

  const taskName = ref(null)
  const taskOptions = ref([])

  const chartDataArr = ref([]) // 这里每一项代表一个柱状图的数据
  let chartInstances = []

  // 1. 拉取任务选项
  onMounted(async () => {
    const res = await queryTaskOption()
    if (res.code === 200) {
      taskOptions.value = res.data
    }
  })

  onUnmounted(() => {
    destroyAllCharts()
  })

  // 2. 拉取统计数据并转换成柱状图格式
  const generateCharts = async () => {
    if (!taskName.value) return ElMessage.error('请选择任务')
    try {
      VxeUI.loading.open({ text: '加载中...' })
      chartDataArr.value = []
      destroyAllCharts()

      // 请求接口数据
      const res = await speResStatistics({ taskName: taskName.value })
      const data = res.data || []

      // 转换格式: 每个 frequencyUsingUnit 一组图
      chartDataArr.value = data.map(group => {
        // 提取所有装备型号和数量
        const xAxisData = group.statistics.map(item => item.equipmentModel)
        const seriesData = group.statistics.map(item => item.count)
        return {
          title: group.frequencyUsingUnit, // 用频单位
          xAxisData,
          seriesData,
          type: 'column'
        }
      })

      // 等 dom 渲染后画图
      await nextTick()
      renderAllCharts()
    } catch (e) {
      ElMessage.error('获取统计数据失败')
      console.error(e)
    } finally {
      VxeUI.loading.close()
    }
  }

  // 3. 渲染所有图表
  const renderAllCharts = () => {
    destroyAllCharts()
    chartDataArr.value.forEach((chartData, chartIndex) => {
      renderChart(chartData, chartIndex)
    })
  }

  // 4. 单图渲染
  const renderChart = (data, chartIndex) => {
    const chartDom = document.getElementById(`chart-container-${chartIndex}`)
    if (!chartDom) return
    const chart = Highcharts.chart(chartDom, {
      chart: {
        type: 'column',
        margin: [50, 50, 100, 80],
        options3d: {
          enabled: true,
          alpha: 0,
          beta: 0,
          depth: 20,
          viewDistance: 25
        }
      },
      title: {
        text: data.title || '用频资源需求统计'
      },
      xAxis: {
        categories: data.xAxisData,
        title: { text: '装备型号' },
        labels: {
          rotation: 0,
          align: 'center',
          style: { fontSize: '13px', fontFamily: 'Verdana, sans-serif' }
        }
      },
      yAxis: {
        title: { text: '装备数量' },
        min: 0
      },
      tooltip: {
        headerFormat: '<b>{point.key}</b><br>',
        pointFormat: '装备数量: {point.y}'
      },
      plotOptions: {
        column: {
          depth: 25,
          dataLabels: { enabled: true }
        }
      },
      credits: { enabled: false },
      legend: { enabled: false },
      accessibility: { enabled: false },
      series: [
        {
          name: '装备数量',
          data: data.seriesData
        }
      ]
    })

    chartInstances.push(chart)
    // 窗口大小变化时自适应
    window.addEventListener('resize', chart.reflow)
  }

  // 5. 销毁所有图表实例
  const destroyAllCharts = () => {
    chartInstances.forEach(chart => chart && chart.destroy && chart.destroy())
    chartInstances = []
  }
</script>

<template>
  <div class="flex items-center mb-4">
    <div class="w-[80px] flex items-center">ZZ任务</div>
    <vxe-select style="width: 300px" v-model="taskName">
      <vxe-option
        v-for="item in taskOptions"
        :value="item.value"
        :label="item.label"
        :key="item.value"
      ></vxe-option>
    </vxe-select>
    <cu-button type="primary" class="ml-5" content="确定" @click="generateCharts"></cu-button>
  </div>

  <!-- 图表容器 -->
  <div v-if="chartDataArr.length > 0" class="grid grid-cols-1 gap-6">
    <div v-for="(chartData, index) in chartDataArr" :key="index" class="chart-item">
      <h3 class="text-lg font-semibold mb-2">{{ chartData.title }}</h3>
      <div
        :id="`chart-container-${index}`"
        style="width: 100%; height: 400px"
        class="border rounded shadow-sm p-2"
      ></div>
    </div>
  </div>
  <!-- 空状态 -->
  <div
    v-loading="false"
    v-else
    class="h-[770px] flex items-center justify-center text-center text-gray-500"
  >
    请选择任务并点击"确定"按钮生成图表
  </div>
</template>

<style lang="scss" scoped>
  .chart-item {
    transition: all 0.3s;
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
</style>
