<script setup>
  import { parseTime } from '@/utils/utils.js'
  import CuAreaDialog from '@/components/CuAreaDialog/index.vue'

  // 对话框可见性
  const dialogTableVisible = defineModel({ type: Boolean, default: false })

  // 定义props用于接收外部数据
  const props = defineProps({
    // 初始表格数据
    initialData: {
      type: Object,
      default: () => ({})
    }
  })

  // 定义事件
  // const emit = defineEmits(['update:data', 'field-change', 'save'])

  // 表格数据，格式化为二列展示
  const tableData = ref([
    {
      param1: 'ZZ任务',
      value1: '123',
      field1: 'task',
      editType1: 'text',
      editable1: true,

      param2: '用频单位',
      value2: '123',
      field2: 'unit',
      editType2: 'text',
      editable2: true
    },
    {
      param1: '装备型号名称',
      value1: 'X123',
      field1: 'modelName',
      editType1: 'text',
      editable1: true,

      param2: '配装平台',
      value2: 'A123',
      field2: 'platform',
      editType2: 'text',
      editable2: true
    },
    {
      param1: '装备数量',
      value1: '10',
      field1: 'quantity',
      editType1: 'number',
      editable1: true,

      param2: '频段用途',
      value2: 'A123',
      field2: 'bandPurpose',
      editType2: 'text',
      editable2: true
    },
    {
      param1: '频段范围',
      value1: 'X123',
      field1: 'bandRange',
      editType1: 'text',
      editable1: true,

      param2: '频点数量',
      value2: '10',
      field2: 'frequencyPoints',
      editType2: 'number',
      editable2: true
    },
    {
      param1: '工作频率',
      value1: 'X123',
      field1: 'workFrequency',
      editType1: 'text',
      editable1: true,

      param2: '发射带宽',
      value2: 'A123',
      field2: 'transmitBandwidth',
      editType2: 'text',
      editable2: true
    },
    {
      param1: '发射功率',
      value1: 'X123',
      field1: 'transmitPower',
      editType1: 'text',
      editable1: true,

      param2: '使用区域',
      value2: 'A123',
      field2: 'applicableArea',
      editType2: 'custom',
      editable2: true
    },
    {
      param1: '使用开始时间',
      value1: '2023-10-01 18:00:00',
      field1: 'startTime',
      editType1: 'datetime',
      editable1: true,

      param2: '使用结束时间',
      value2: '2023-10-01 18:00:00',
      field2: 'endTime',
      editType2: 'datetime',
      editable2: true
    },
    {
      param1: '备注',
      value1: 'X123',
      field1: 'remarks',
      editType1: 'textarea',
      editable1: true
    }
  ])

  const areaDialog = ref(false)

  // 监听初始数据变化
  watch(
    () => props.initialData,
    newVal => {
      if (newVal) {
        initTableData()
      }
    },
    { deep: true }
  )

  // 根据props初始化表格数据
  const initTableData = () => {
    if (!props.initialData) return

    tableData.value.forEach(row => {
      // 填充value1
      if (row.field1 && props.initialData[row.field1] !== undefined) {
        row.value1 = props.initialData[row.field1]
      }

      // 填充value2
      if (row.field2 && props.initialData[row.field2] !== undefined) {
        row.value2 = props.initialData[row.field2]
      }
    })
  }

  // 组件挂载时初始化数据
  onMounted(() => {
    initTableData()
  })

  // 构造输出数据对象
  const outputData = computed(() => {
    const result = {}
    tableData.value.forEach(row => {
      // 添加field1对应的值
      if (row.field1) {
        result[row.field1] = row.value1
      }

      // 添加field2对应的值
      if (row.field2) {
        result[row.field2] = row.value2
      }
    })

    return result
  })

  // 单元格编辑配置
  const editConfig = reactive({
    trigger: 'click',
    mode: 'cell',
    showStatus: true,
    beforeEditMethod({ row, column }) {
      // value1列在editable1为true时可编辑
      if (column.property === 'value1') {
        return row.editable1
      }
      // value2列在editable2为true时可编辑
      if (column.property === 'value2') {
        return row.editable2 && row.param2 !== undefined
      }
      return false
    }
  })

  // 处理单元格值变化
  const handleCellValueChange = params => {
    // 防止null或undefined引起的错误
    if (!params || !params.row || !params.column) return

    const { row, column } = params
    const property = column.property

    let fieldName, value

    // 根据列属性确定字段名称和值
    if (property === 'value1') {
      fieldName = row.field1
      value = row.value1
    } else if (property === 'value2') {
      fieldName = row.field2
      value = row.value2
    } else {
      return
    }

    // 确保字段存在
    if (fieldName) {
      // 触发字段变更事件
      // emit('field-change', {
      //   field: fieldName,
      //   value: value,
      //   row: { ...row },
      //   property
      // })
      console.log('字段变更事件触发', {
        field: fieldName,
        value: value
      })

      // 触发整体数据更新事件
      // emit('update:data', outputData.value)
    }
  }

  // 确认按钮处理函数
  const confirm = () => {
    // 触发保存事件并传递完整数据
    // emit('save', outputData.value)
    // 关闭对话框
    dialogTableVisible.value = false
  }

  // 取消按钮处理函数
  const cancel = () => {
    // 重置为初始数据
    initTableData()
    // 关闭对话框
    dialogTableVisible.value = false
  }

  const handleEditClick = params => {
    if (!params || !params.row || !params.column) return

    const { row, column } = params
    const field = column.property // e.g. "value1" 或 "value2"
    // 从 field 推算出对应的 editType 字段名
    const editTypeKey = field.replace('value', 'editType')
    const cellType = row[editTypeKey] // e.g. row.editType1

    // 1. 如果是 custom 类型，先走自定义逻辑
    if (cellType === 'custom') {
      // 退出当前激活的编辑单元格
      params.$table.clearEdit()
      // 再打开自定义区域弹窗
      areaDialog.value = true
      return
    }

    // 2. 如果当前可编辑（beforeEditMethod 返回 true），说明已经进编辑状态，不触发 valueChange
    const isEditing = editConfig.beforeEditMethod({ row, column })
    if (isEditing) {
      return
    }
  }

  // 处理下拉选择变更
  const handleSelectChange = (value, params) => {
    if (params && params.row) {
      // 直接调用值变更处理函数
      handleCellValueChange({
        row: params.row,
        column: { property: params.column.property }
      })
    }
  }

  // 处理日期时间变更
  const handleDateChange = (value, params) => {
    if (params && params.row) {
      handleCellValueChange({
        row: params.row,
        column: { property: params.column.property }
      })
    }
  }
</script>

<template>
  <cu-dialog
    v-model="dialogTableVisible"
    title="用频计划"
    width="860"
    @confirm="confirm"
    @cancel="cancel"
  >
    <vxe-table
      border
      stripe
      :data="tableData"
      :show-header="false"
      :keep-source="true"
      :row-config="{ isCurrent: true, isHover: true }"
      :edit-config="editConfig"
      @cell-click="handleEditClick"
      @edit-actived="params => console.log('单元格激活:', params.column.property)"
      @edit-changed="handleCellValueChange"
      @edit-closed="handleCellValueChange"
    >
      <!-- 第一对参数和值 -->
      <vxe-column field="param1" width="120" align="center" class-name="param-column"></vxe-column>
      <vxe-column field="value1" align="center" :edit-render="{ name: 'default' }">
        <!-- 非编辑时的展示 -->
        <template #default="{ row }">
          <template v-if="row.editType1 === 'datetime'">
            {{ parseTime(row.value1) }}
          </template>
          <template v-else-if="row.editType1 === 'select' && row.options1">
            {{ row.options1.find(opt => opt.value === row.value1)?.label || row.value1 }}
          </template>
          <template v-else>
            {{ row.value1 }}
          </template>
        </template>

        <!-- 编辑时的输入控件 -->
        <template #edit="{ row, column }">
          <!-- 文本输入 -->
          <vxe-input
            v-if="row.editType1 === 'text'"
            v-model="row.value1"
            type="text"
            placeholder="请输入"
            @change="() => handleCellValueChange({ row, column })"
          />

          <!-- 数字输入 -->
          <vxe-input
            v-else-if="row.editType1 === 'number'"
            v-model="row.value1"
            type="number"
            placeholder="请输入数字"
            @change="() => handleCellValueChange({ row, column })"
          />

          <!-- 日期时间选择器 -->
          <vxe-input
            v-else-if="row.editType1 === 'datetime'"
            v-model="row.value1"
            type="datetime"
            transfer
            placeholder="请选择日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            popper-class="date-picker-higher-z"
            :teleported="true"
            :popper-options="{
              modifiers: [
                { name: 'computeStyles', options: { adaptive: false, gpuAcceleration: false } }
              ]
            }"
            @change="val => handleDateChange(val, { row, column })"
          />

          <!-- 文本域 -->
          <vxe-textarea
            v-else-if="row.editType1 === 'textarea'"
            v-model="row.value1"
            placeholder="请输入"
            :autosize="{ minRows: 2, maxRows: 4 }"
            @change="() => handleCellValueChange({ row, column })"
          />

          <!-- 选择框 -->
          <vxe-select
            v-else-if="row.editType1 === 'select'"
            v-model="row.value1"
            placeholder="请选择"
            transfer
            @change="val => handleSelectChange(val, { row, column })"
          >
            <vxe-option
              v-for="opt in row.options1"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </vxe-select>
        </template>
      </vxe-column>

      <!-- 第二对参数和值 -->
      <vxe-column field="param2" width="120" align="center" class-name="param-column"></vxe-column>
      <vxe-column field="value2" align="center" :edit-render="{ name: 'default' }">
        <!-- 非编辑时的展示 -->
        <template #default="{ row }">
          <template v-if="!row.param2">
            <!-- 空白单元格 -->
          </template>
          <template v-else-if="row.editType2 === 'datetime'">
            {{ parseTime(row.value2) }}
          </template>
          <template v-else-if="row.editType2 === 'select' && row.options2">
            {{ row.options2.find(opt => opt.value === row.value2)?.label || row.value2 }}
          </template>
          <template v-else>
            {{ row.value2 }}
          </template>
        </template>

        <!-- 编辑时的输入控件 -->
        <template #edit="{ row, column }">
          <!-- 文本输入 -->
          <vxe-input
            v-if="row.editType2 === 'text'"
            v-model="row.value2"
            type="text"
            placeholder="请输入"
            @change="() => handleCellValueChange({ row, column })"
          />

          <!-- 数字输入 -->
          <vxe-input
            v-else-if="row.editType2 === 'number'"
            v-model="row.value2"
            type="number"
            placeholder="请输入数字"
            @change="() => handleCellValueChange({ row, column })"
          />

          <!-- 日期时间选择器 -->
          <vxe-input
            v-else-if="row.editType2 === 'datetime'"
            v-model="row.value2"
            transfer
            type="datetime"
            placeholder="请选择日期时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            popper-class="date-picker-higher-z"
            :teleported="true"
            :popper-options="{
              modifiers: [
                { name: 'computeStyles', options: { adaptive: false, gpuAcceleration: false } }
              ]
            }"
            @change="val => handleDateChange(val, { row, column })"
          />

          <!-- 文本域 -->
          <vxe-textarea
            v-else-if="row.editType2 === 'textarea'"
            v-model="row.value2"
            placeholder="请输入"
            :autosize="{ minRows: 2, maxRows: 4 }"
            @change="() => handleCellValueChange({ row, column })"
          />

          <!-- 选择框 -->
          <vxe-select
            v-else-if="row.editType2 === 'select'"
            v-model="row.value2"
            placeholder="请选择"
            transfer
            @change="val => handleSelectChange(val, { row, column })"
          >
            <vxe-option
              v-for="opt in row.options2"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </vxe-select>
        </template>
      </vxe-column>
    </vxe-table>

    <!-- 自定义组件/默认输入框 -->
    <cu-area-dialog v-if="areaDialog" v-model="areaDialog" />
  </cu-dialog>
</template>

<style lang="scss" scoped>
  /* 修复vxe-select选项宽度问题 */
  :deep(.vxe-select-option) {
    max-width: 100% !important;
  }

  /* 底部操作栏样式 */
  .footer-actions {
    display: flex;
    justify-content: flex-start;
    padding: 8px 0;

    .info-text {
      color: #909399;
      font-size: 12px;
    }
  }

  /* 确保日期选择器和下拉框的弹出层在最顶层 */
  :deep(.vxe-table-select-option) {
    z-index: 3000 !important;
  }

  /* El日期选择器的弹出层样式 */
  :deep(.date-picker-higher-z) {
    z-index: 3100 !important;
  }

  /* 解决下拉选择面板被dialog遮挡的问题 */
  :deep(.vxe-select--panel) {
    position: fixed !important;
    z-index: 3000 !important;
  }
</style>
