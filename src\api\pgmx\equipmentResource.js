import request from '@/utils/request'

/** 查询装备部件树 */
export function readEquipmentPartTree(data) {
  return request({
    url: '/pgmx/equipmentResource/readEquipmentPartTree',
    method: 'get',
    params: data
  })
}

/** 装备类型树懒加载查询 */
export function readTree(data) {
  return request({
    url: '/pgmx/equipmentResource/readTree',
    method: 'get',
    params: data
  })
}

/** 查询装备部件详情 */
export function readEquipmentPartInfo(data) {
  return request({
    url: '/pgmx/equipmentResource/readEquipmentPartInfo',
    method: 'get',
    params: data
  })
}

/** 查询装备详情 */
export function readEquipmentInfo(data) {
  return request({
    url: '/pgmx/equipmentResource/readEquipmentInfo',
    method: 'get',
    params: data
  })
}











