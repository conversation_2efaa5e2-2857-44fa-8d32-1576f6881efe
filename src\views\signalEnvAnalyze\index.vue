<script setup>
  const tabsData = [
    {
      label: '信息设施',
      value: 'info',
      children: [
        { label: '通信设施', value: 'comm' },
        { label: '算力设施', value: 'compute' },
        { label: '新技术设施', value: 'new' },
        { label: '分析报告', value: 'report' }
      ]
    },
    {
      label: '通信能力',
      value: 'ability',
      children: [
        { label: '蜂窝', value: 'cell' },
        { label: '专网', value: 'private' }
      ]
    },
    {
      label: '网络安全',
      value: 'sec',
      children: [{ label: '等保', value: 'grade' }]
    },
    { label: '电磁环境', value: 'emc', children: [] },
    { label: '指挥信息系统', value: 'cis', children: [{ label: '态势', value: 'sit' }] },
    { label: '分析报告', value: 'ana', children: [{ label: '全部', value: 'all' }] }
  ]

  // v-model 当前选中（可从路由/后端初始化）
  const active = ref({ parent: 'info', child: 'comm' })
</script>

<template>
  <el-tabs>
    <el-tab-pane v-for="item in tabsData" :label="item.label" :name="item.value">
      <el-tabs v-for="child in item.children" :label="child.label" :name="child.value">
        <template #content="{ parent, child }">
          <div class="p-3 text-gray-600"> 当前：一级={{ parent }}，二级={{ child }} </div>
        </template>
      </el-tabs>
    </el-tab-pane>
  </el-tabs>
</template>
