<script setup>
  import { ref, computed } from 'vue'

  const tabsData = [
    {
      label: '信息设施',
      value: 'info',
      children: [
        { label: '通信设施', value: 'comm' },
        { label: '算力设施', value: 'compute' },
        { label: '新技术设施', value: 'new' },
        { label: '分析报告', value: 'report' }
      ]
    },
    {
      label: '通信能力',
      value: 'ability',
      children: [
        { label: '蜂窝', value: 'cell' },
        { label: '专网', value: 'private' }
      ]
    },
    {
      label: '网络安全',
      value: 'sec',
      children: [{ label: '等保', value: 'grade' }]
    },
    { label: '电磁环境', value: 'emc', children: [] },
    { label: '指挥信息系统', value: 'cis', children: [{ label: '态势', value: 'sit' }] },
    { label: '分析报告', value: 'ana', children: [{ label: '全部', value: 'all' }] }
  ]

  // 一级标签页激活状态
  const activeFirstLevel = ref('info')
  // 二级标签页激活状态
  const activeSecondLevel = ref('comm')

  // 当前激活的一级标签页数据
  const currentFirstLevelTab = computed(() => {
    return tabsData.find(item => item.value === activeFirstLevel.value)
  })

  // 当前二级标签页列表
  const currentSecondLevelTabs = computed(() => {
    return currentFirstLevelTab.value?.children || []
  })

  // 当一级标签页切换时，自动切换到第一个二级标签页
  const handleFirstLevelChange = value => {
    const newTab = tabsData.find(item => item.value === value)
    if (newTab && newTab.children.length > 0) {
      activeSecondLevel.value = newTab.children[0].value
    } else {
      activeSecondLevel.value = ''
    }
  }
</script>

<template>
  <div class="signal-env-analyze">
    <!-- 一级标签页 -->
    <el-tabs
      v-model="activeFirstLevel"
      type="border-card"
      class="first-level-tabs"
      @tab-change="handleFirstLevelChange"
    >
      <el-tab-pane
        v-for="item in tabsData"
        :key="item.value"
        :label="item.label"
        :name="item.value"
      >
        <!-- 二级标签页 -->
        <el-tabs
          v-if="currentSecondLevelTabs.length > 0"
          v-model="activeSecondLevel"
          type="border-card"
          class="second-level-tabs"
        >
          <el-tab-pane
            v-for="child in currentSecondLevelTabs"
            :key="child.value"
            :label="child.label"
            :name="child.value"
          >
            <div class="tab-content">
              <div class="p-4 text-gray-600 bg-gray-50 rounded">
                当前选中：一级 = {{ currentFirstLevelTab?.label }}，二级 = {{ child.label }}
              </div>
              <!-- 这里可以根据具体的标签页内容进行扩展 -->
            </div>
          </el-tab-pane>
        </el-tabs>

        <!-- 没有二级标签页时显示的内容 -->
        <div v-else class="tab-content">
          <div class="p-4 text-gray-600 bg-gray-50 rounded"> 当前选中：{{ item.label }} </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<style scoped>
  .signal-env-analyze {
    padding: 20px;
  }

  .first-level-tabs {
    margin-bottom: 0;
  }

  .second-level-tabs {
    margin-top: -1px;
    border-top: none;
  }

  .second-level-tabs :deep(.el-tabs__header) {
    margin: 0;
    border-bottom: 1px solid #dcdfe6;
  }

  .second-level-tabs :deep(.el-tabs__nav-wrap) {
    padding: 0 20px;
    background-color: #f5f7fa;
  }

  .tab-content {
    padding: 20px;
    min-height: 300px;
  }

  /* 优化标签页样式，使其更紧凑 */
  .first-level-tabs :deep(.el-tabs__item) {
    padding: 0 20px;
    height: 40px;
    line-height: 40px;
  }

  .second-level-tabs :deep(.el-tabs__item) {
    padding: 0 16px;
    height: 36px;
    line-height: 36px;
    font-size: 14px;
  }
</style>
