<script setup>
  import CuAreaDialog from '@/components/CuAreaDialog/index.vue'
  import { processDictionary } from '@/utils/index.js'
  import { getTaskById } from '@/api/pgmx/useFreqTask.js'

  // 定义props用于接收外部数据
  const props = defineProps({
    type: {
      type: String,
      default: 'add'
    },
    taskId: {
      type: String,
      default: ''
    }
  })

  // 定义事件
  const emit = defineEmits(['PATaskConfirm'])
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const formRef = ref(null)
  const validConfig = reactive({
    theme: 'normal'
  })
  const formData = ref({
    taskName: '', //任务名称
    frequencyUsingUnit: '', // 用频单位
    equipmentModel: '', //装备型号
    equippedPlatform: '', //配装平台
    equipmentQuantity: '', //装备数量
    purpose: '', //频段用途
    bandRange: '',
    frequencyBandRangeStart: '', // 频率带起始
    frequencyBandRangeEnd: '', // 频率带结束
    frequencyPointQuantity: '', //频点数量
    operatingFrequency: '', //工作频率
    emissionBandwidth: '', //发射带宽
    transmissionPower: '', //发射功率
    areaStr: '', //使用区域
    useStartTime: '', //使用开始时间
    useEndTime: '', //使用结束时间
    remarks: '', //备注
    usageAreaEntity: {
      longitude: '',
      latitude: '',
      radius: '',
      geoPointList: [],
      type: 0
    }
  })

  const formRules = reactive({
    taskName: [{ required: true, message: '必须填写' }],
    frequencyUsingUnit: [{ required: true, message: '必须填写' }],
    equipmentModel: [{ required: true, message: '必须填写' }],
    equippedPlatform: [{ required: true, message: '必须填写' }],
    equipmentQuantity: [{ required: true, message: '必须填写' }],
    purpose: [{ required: true, message: '必须填写' }],
    bandRange: [
      {
        validator() {
          const { frequencyBandRangeStart, frequencyBandRangeEnd } = formData.value

          if (!frequencyBandRangeStart || !frequencyBandRangeEnd) {
            return new Error('必须填写起始频率和结束频率')
          }

          if (frequencyBandRangeEnd <= frequencyBandRangeStart) {
            return new Error('结束频率必须大于起始频率')
          }

          return true // 校验通过
        }
      }
    ],
    frequencyPointQuantity: [{ required: true, message: '必须填写' }],
    operatingFrequency: [{ required: true, message: '必须填写' }],
    emissionBandwidth: [{ required: true, message: '必须填写' }],
    transmissionPower: [{ required: true, message: '必须填写' }],
    areaStr: [{ required: true, message: '必须填写' }],
    useStartTime: [{ required: true, message: '必须填写' }],
    useEndTime: [{ required: true, message: '必须填写' }],
    remarks: [{ required: true, message: '必须填写' }]
  })

  const areaDialog = ref(false)
  const equipmentModelOptions = ref([]) //装备类型枚举
  const unitOptions = ref([]) //用频单位枚举
  const purposeOptions = ref([]) //用途枚举
  const equipmentPlatformOptions = ref([]) //配装平台枚举

  const getDictionaryData = async () => {
    processDictionary('pgmx_equipment', equipmentModelOptions)
    processDictionary('pgmx_unit', unitOptions)
    processDictionary('pgmx_purpose', purposeOptions)
    processDictionary('pgmx_equipform', equipmentPlatformOptions)
  }

  const resetForm = () => {
    formData.value = {
      taskName: '',
      frequencyUsingUnit: '',
      equipmentModel: '',
      equippedPlatform: '',
      equipmentQuantity: '',
      purpose: '',
      bandRange: '',
      frequencyPointQuantity: '',
      operatingFrequency: '',
      emissionBandwidth: '',
      transmissionPower: '',
      areaStr: '',
      useStartTime: '',
      useEndTime: '',
      remarks: '',
      usageAreaEntity: {
        longitude: '',
        latitude: '',
        radius: '',
        geoPointList: [],
        type: 0
      }
    }
  }

  const areaDialogConfirm = ({ data, type }) => {
    if (type === 'circle') {
      formData.value.usageAreaEntity.longitude = data.longitude
      formData.value.usageAreaEntity.latitude = data.latitude
      formData.value.usageAreaEntity.radius = data.radius
      formData.value.usageAreaEntity.type = 1
      formData.value.areaStr = `[O(${data.longitude}, ${data.latitude});R(${data.radius})km]`
    } else {
      formData.value.usageAreaEntity.geoPointList = data
      formData.value.usageAreaEntity.type = 2
      formData.value.areaStr = data.map(item => `${item.longitude},${item.latitude}`).join(';')
    }
  }

  // 确认按钮处理函数
  const confirm = async () => {
    if (formRef.value) {
      const errMaps = await formRef.value.validate()

      console.log(errMaps)
      return
      if (!errMaps) {
        // 触发保存事件并传递完整数据
        emit('PATaskConfirm', formData.value)
      }
    }
  }

  // 取消按钮处理函数
  const cancel = () => {
    // 关闭对话框
    dialogTableVisible.value = false
  }

  // 组件挂载时初始化数据
  onMounted(async () => {
    await getDictionaryData()
    if (props.taskId) {
      await getTaskById({ id: props.taskId }).then(res => {
        formData.value = res.data
      })
    }
  })

  defineExpose({
    resetForm
  })
</script>

<template>
  <cu-dialog
    width="660"
    height="600"
    v-model="dialogTableVisible"
    :title="type === 'add' ? '新增用频计划' : '编辑用频计划'"
    @confirm="confirm"
    @cancel="cancel"
  >
    <vxe-form
      ref="formRef"
      :title-colon="true"
      :title-width="120"
      :data="formData"
      :rules="formRules"
      :valid-config="validConfig"
      title-align="right"
    >
      <vxe-form-item title="任务名称" field="taskName" span="24" :required="true">
        <vxe-input v-model="formData.taskName" placeholder="请输入任务名称" clearable />
      </vxe-form-item>
      <vxe-form-item title="用频单位" field="frequencyUsingUnit" span="24" :required="true">
        <vxe-select v-model="formData.frequencyUsingUnit" placeholder="请选择用频单位">
          <vxe-option
            v-for="item in unitOptions"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          >
          </vxe-option>
        </vxe-select>
      </vxe-form-item>
      <vxe-form-item title="装备型号" field="equipmentModel" span="24" :required="true">
        <vxe-select v-model="formData.equipmentModel" placeholder="请选择装备型号">
          <vxe-option
            v-for="item in equipmentModelOptions"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          >
          </vxe-option>
        </vxe-select>
      </vxe-form-item>
      <vxe-form-item title="配装平台" field="equippedPlatform" span="24" :required="true">
        <vxe-select v-model="formData.equippedPlatform" placeholder="请选择配装平台">
          <vxe-option
            v-for="item in equipmentPlatformOptions"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          >
          </vxe-option>
        </vxe-select>
      </vxe-form-item>
      <vxe-form-item title="装备数量" field="equipmentQuantity" span="24" :required="true">
        <vxe-input
          v-model="formData.equipmentQuantity"
          type="number"
          min="0"
          clearable
          placeholder="请输入装备数量"
        />
      </vxe-form-item>
      <vxe-form-item title="频段用途" field="purpose" span="24" :required="true">
        <vxe-select v-model="formData.purpose" placeholder="请选择频段用途">
          <vxe-option
            v-for="item in purposeOptions"
            :label="item.label"
            :value="item.value"
            :key="item.value"
          >
          </vxe-option>
        </vxe-select>
      </vxe-form-item>
      <vxe-form-item title="频段范围" field="bandRange" span="24" :required="true">
        <div class="flex items-center justify-between gap-x-5">
          <vxe-input
            v-model="formData.frequencyBandRangeStart"
            placeholder="请输入起始频段"
            clearable
          >
            <template #suffix> MHz </template>
          </vxe-input>
          <span>至</span>
          <vxe-input
            v-model="formData.frequencyBandRangeEnd"
            placeholder="请输入结束频段"
            clearable
          >
            <template #suffix> MHz </template>
          </vxe-input>
        </div>
      </vxe-form-item>
      <vxe-form-item title="频点数量" field="frequencyPointQuantity" span="24" :required="true">
        <vxe-input
          v-model="formData.frequencyPointQuantity"
          type="number"
          placeholder="请输入频点数量"
          clearable
        />
      </vxe-form-item>
      <vxe-form-item title="工作频率" field="operatingFrequency" span="24" :required="true">
        <vxe-input
          v-model="formData.operatingFrequency"
          type="number"
          min="0"
          placeholder="请输入工作频率"
          clearable
        >
          <template #suffix> MHz </template>
        </vxe-input>
      </vxe-form-item>
      <vxe-form-item title="发射带宽" field="emissionBandwidth" span="24" :required="true">
        <vxe-input
          v-model="formData.emissionBandwidth"
          type="number"
          min="0"
          placeholder="请输入发射带宽"
          clearable
        >
          <template #suffix> kHz </template>
        </vxe-input>
      </vxe-form-item>
      <vxe-form-item title="发射功率" field="transmissionPower" span="24" :required="true">
        <vxe-input
          v-model="formData.transmissionPower"
          type="number"
          placeholder="请输入发射功率"
          clearable
        >
          <template #suffix> dBm </template>
        </vxe-input>
      </vxe-form-item>
      <vxe-form-item title="使用区域" field="areaStr" span="24" :required="true">
        <div class="flex items-center justify-between gap-x-4">
          <cu-button content="选择区域" @click="areaDialog = true"></cu-button>
          <vxe-input
            v-model="formData.areaStr"
            :disabled="true"
            placeholder="请选择区域"
          ></vxe-input>
        </div>
      </vxe-form-item>
      <vxe-form-item title="使用开始时间" field="useStartTime" span="24" :required="true">
        <vxe-input
          v-model="formData.useStartTime"
          format="yyyy-MM-dd HH:mm:ss"
          type="datetime"
          placeholder="请选择使用开始时间"
          clearable
        />
      </vxe-form-item>
      <vxe-form-item title="使用结束时间" field="useEndTime" span="24" :required="true">
        <vxe-input
          v-model="formData.useEndTime"
          format="yyyy-MM-dd HH:mm:ss"
          type="datetime"
          placeholder="请选择使用结束时间"
          clearable
        />
      </vxe-form-item>
      <vxe-form-item title="备注" field="remarks" span="24" :required="true">
        <vxe-textarea
          v-model="formData.remarks"
          resize="horizontal"
          placeholder="请输入备注"
          clearable
        />
      </vxe-form-item>
    </vxe-form>
  </cu-dialog>

  <!-- 自定义组件/默认输入框 -->
  <cu-area-dialog
    v-if="areaDialog"
    v-model="areaDialog"
    @confirm="areaDialogConfirm"
    :usage-area-entity="formData.usageAreaEntity"
  />
</template>

<style lang="scss" scoped></style>
