<template>
  <section class="app-main">
    <router-view v-slot="{ Component, route }">
      <!-- <transition name="fade-transform" mode="out-in"> </transition> -->
      <keep-alive :include="tagsViewStore.cachedViews">
        <component :is="Component" v-if="!route.meta.link" :key="route.path" />
      </keep-alive>
    </router-view>
    <iframe-toggle />
  </section>
</template>

<script setup>
  import iframeToggle from './IframeToggle/index'
  import useTagsViewStore from '@/store/modules/tagsView'

  const tagsViewStore = useTagsViewStore()
</script>

<style lang="scss" scoped>
  @import '@/assets/styles/variables.module.scss';
  .app-main {
    /* 88= navbar  88  */
    height: calc(900px - 50px);
    // width: calc(100vw - $base-sidebar-width);
    position: relative;
    overflow: hidden;
    flex: 1;
    // margin: 1rem;
    padding: 1rem;
    overflow: auto;
    background-color: var(--el-bg-color);
  }

  .fixed-header + .app-main {
    padding-top: 50px;
  }

  .hasTagsView {
    .fixed-header + .app-main {
      padding-top: 92px;
    }
  }
</style>

<style lang="scss">
  .el-popup-parent--hidden {
    .fixed-header {
      padding-right: 17px;
    }
  }
</style>
