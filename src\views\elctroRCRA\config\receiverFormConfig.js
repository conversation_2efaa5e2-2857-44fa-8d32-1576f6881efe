
export const receiverFormConfig = [
  {
    title: '接收机名称',
    field: 'name',
    span: 12,
    itemRender: { name: 'VxeInput' },
  },
  {
    title: '接收机编码',
    field: 'code',
    span: 12,
    itemRender: { name: 'VxeInput' },
  },
  {
    title: '频率上限(MHz)',
    field: 'freqUpperLimit',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '频率下限(MHz)',
    field: 'freqLowerLimit',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '灵敏度(dBm)',
    field: 'sensitivity',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '频率可调性',
    field: 'freqAdjustability',
    span: 12,
    itemRender: {
      name: 'VxeSelect', options: [{ label: '是', value: true }, { label: '否', value: false }]
    },
  },
  {
    title: '信号间隔(MHz)',
    field: 'signalInterval',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '信道数量',
    field: 'channelNum',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '最低信道中心频率(MHz)',
    field: 'minChannelCenterFreq',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '相邻工作频点间隔(MHz)',
    field: 'adjacentFreqPointInterval',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '工作频点数量',
    field: 'workingFreqPointNum',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '镜频抑制比(dB)',
    field: 'imageRejectionRatio',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '邻道抑制比(dB)',
    field: 'adjacentChannelRejectionRatio',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '阻塞电平(dB)',
    field: 'blockingLevel',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '互调抑制比(dB)',
    field: 'intermodulationRejectionRatio',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '杂散抑制比(dB)',
    field: 'spuriousRejectionRatio',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '调制方式',
    field: 'modulationMode',
    span: 12,
    itemRender: {
      name: 'VxeSelect', options: [{ label: '是', value: true }, { label: '否', value: false }]
    },
  },
  {
    title: '调制类型',
    field: 'modulationType',
    span: 12,
    itemRender: {
      name: 'VxeSelect', options: [{ label: '是', value: true }, { label: '否', value: false }]
    },
  },
  {
    title: '射频3db带宽(MHz)',
    field: 'rfBandwidth3db',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '射频20db带宽(MHz)',
    field: 'rfBandwidth20db',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '射频60db带宽(MHz)',
    field: 'rfBandwidth60db',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '中频3db带宽(MHz)',
    field: 'ifBandwidth3db',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '中频20db带宽(MHz)',
    field: 'ifBandwidth20db',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '中频60db带宽(MHz)',
    field: 'ifBandwidth60db',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '中频频率(MHz)',
    field: 'ifFrequency',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '处理增益(dB)',
    field: 'processingGain',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '噪声系数(dB)',
    field: 'noiseFigure',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '等效噪声温蒂(K)',
    field: 'equivalentNoiseTemperature',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '干扰容限',
    field: 'interferenceMargin',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '射频保护比',
    field: 'rfProtectionRatio',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '最小可检测信噪比',
    field: 'minDetectableSignalRatio',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '误码率限值',
    field: 'bitErrorRateLimit',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
  {
    title: '干信比',
    field: 'interferenceRatio',
    span: 12,
    itemRender: { name: 'VxeNumberInput', props: { controlConfig: { layout: 'default' } } },
  },
]
