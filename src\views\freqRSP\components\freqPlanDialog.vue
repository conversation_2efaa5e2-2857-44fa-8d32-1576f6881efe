<script setup>
  import { parseTime, selectDictLabel } from '@/utils/utils.js'
  import CuAreaDialog from '@/components/CuAreaDialog/index.vue'
  import { processDictionary } from '@/utils/index.js'

  const props = defineProps({
    planObject: { type: Object, default: () => {} }
  })
  const emits = defineEmits(['confirmPlan'])
  // 对话框可见性
  const dialogTableVisible = defineModel({ type: Boolean, default: false })
  const loading = ref(false)
  const areaDialog = ref(false)
  const currentUsageAreaEntity = ref(null) // 当前使用区域实体
  const currentTaskId = ref(null) // 任务ID
  const equipmentModelOptions = ref([]) //装备类型枚举
  const unitOptions = ref([]) //用频单位枚举
  const purposeOptions = ref([]) //用途枚举

  // 表格数据 & 加载状态
  const tableData = ref(props.planObject.equipmentList)
  const formData = computed(() => ({
    frequencyUsingUnit: selectDictLabel(unitOptions.value, props.planObject.frequencyUsingUnit),
    equipmentModel: selectDictLabel(equipmentModelOptions.value, props.planObject.equipmentModel),
    freqUsageEquipment: props.planObject.freqUsageEquipment
  }))

  const getDictionaryData = async () => {
    processDictionary('pgmx_equipment', equipmentModelOptions)
    processDictionary('pgmx_unit', unitOptions)
    processDictionary('pgmx_purpose', purposeOptions)
  }

  /** 单元格点击事件 */
  const handleEditClick = params => {
    if (!params || !params.row || !params.column) return
    const { row, column } = params
    const field = column.property
    if (field === 'areaStr') {
      // 赋值当前行保护区域和ID
      currentUsageAreaEntity.value = row.usageAreaEntity
      currentTaskId.value = row.id
      params.$table.clearEdit()
      // 再打开自定义区域弹窗
      areaDialog.value = true
      return
    }
  }

  // 删除多边形点
  const removeEquipmentList = index => {
    tableData.value.splice(index, 1)
  }

  // 新增一行
  const addTask = () => {
    tableData.value.push({
      id: tableData.length + 1,
      purpose: '',
      operatingFrequency: '',
      emissionBandwidth: '',
      transmissionPower: '',
      areaStr: '',
      useStartTime: '',
      useEndTime: '',
      createTime: parseTime(new Date())
    })
  }

  /**
   * 处理区域弹窗确认事件，用于处理区域选择后的数据并回显到表格中
   */
  const areaDialogConfirm = ({ data, type }) => {
    const targetIndex = tableData.value.findIndex(item => item.id === currentTaskId.value)
    if (targetIndex === -1) return
    if (type === 'circle') {
      tableData.value[targetIndex].usageAreaEntity.longitude = data.longitude
      tableData.value[targetIndex].usageAreaEntity.latitude = data.latitude
      tableData.value[targetIndex].usageAreaEntity.radius = data.radius
      tableData.value[targetIndex].usageAreaEntity.type = 1
      tableData.value[
        targetIndex
      ].areaStr = `[O(${data.longitude}, ${data.latitude});R(${data.radius})km]`
    } else {
      tableData.value[targetIndex].usageAreaEntity.geoPointList = data.map(item => ({
        longitude: item.longitude,
        latitude: item.latitude
      }))
      tableData.value[targetIndex].usageAreaEntity.type = 2
      tableData.value[targetIndex].areaStr = data
        .map(item => `${item.longitude},${item.latitude}`)
        .join(';')
    }
  }

  // 确认、取消
  const confirm = () => {
    const queryData = {
      ...props.planObject,
      equipmentList: tableData.value,
      frequencyUsingUnit: formData.value.frequencyUsingUnit,
      equipmentModel: formData.value.equipmentModel,
      freqUsageEquipment: formData.value.freqUsageEquipment
    }

    emits('confirmPlan', queryData)
    console.log('Save table data:', queryData)
  }
  const cancel = () => {
    dialogTableVisible.value = false
  }

  // 挂载时加载初始数据
  onMounted(async () => {
    await getDictionaryData()
  })
</script>

<template>
  <cu-dialog
    v-model="dialogTableVisible"
    title="装备用频计划"
    width="880"
    @confirm="confirm"
    @cancel="cancel"
  >
    <div class="mb-4">
      <vxe-form
        :data="formData"
        :title-colon="true"
        :label-config="{ width: '120px', align: 'left' }"
      >
        <vxe-form-item field="unit" title="用频单位" span="24">
          <vxe-input v-model="formData.frequencyUsingUnit" placeholder="请输入用频单位" disabled />
        </vxe-form-item>

        <vxe-form-item field="type" title="装备型号" span="24">
          <vxe-input v-model="formData.equipmentModel" placeholder="请输入装备型号" disabled />
        </vxe-form-item>

        <vxe-form-item field="equipment" title="用频装备" span="24">
          <vxe-input v-model="formData.freqUsageEquipment" placeholder="请输入用频装备" disabled />
        </vxe-form-item>
      </vxe-form>
    </div>

    <div class="font-bold text-[20px] mr-4 mb-2">用频计划</div>
    <vxe-toolbar class="mb-2">
      <template #buttons>
        <cu-button content="新增" @click="addTask" />
        <cu-button content="导出" />
      </template>
    </vxe-toolbar>

    <vxe-table
      ref="xTable"
      border
      stripe
      height="400"
      size="medium"
      :data="tableData"
      :loading="loading"
      :row-config="{ isCurrent: true, isHover: true }"
      :edit-config="{ mode: 'cell', trigger: 'click' }"
      :keep-source="true"
      @cell-click="handleEditClick"
    >
      <vxe-column type="seq" width="80" fixed="left" align="center" />
      <vxe-column
        field="purpose"
        title="用途"
        min-width="160"
        align="center"
        :edit-render="{ defaultSlot: true }"
      >
        <template #default="{ row }">
          {{ selectDictLabel(purposeOptions, row.purpose) }}
        </template>
        <template #edit="{ row }">
          <vxe-select v-model="row.purpose" placeholder="请选择用途">
            <vxe-option
              v-for="item in purposeOptions"
              :label="item.label"
              :value="item.value"
              :key="item.value"
            >
            </vxe-option>
          </vxe-select>
        </template>
      </vxe-column>

      <vxe-column
        field="operatingFrequency"
        title="工作频率"
        min-width="120"
        align="center"
        :edit-render="{ defaultSlot: true }"
      >
        <template #default="{ row }">
          {{ row.operatingFrequency }}
        </template>
        <template #edit="{ row }">
          <vxe-input v-model="row.operatingFrequency" type="number" placeholder="MHz" />
        </template>
      </vxe-column>

      <vxe-column
        field="emissionBandwidth"
        title="发射带宽"
        min-width="120"
        align="center"
        :edit-render="{ defaultSlot: true }"
      >
        <template #default="{ row }">
          {{ row.emissionBandwidth }}
        </template>
        <template #edit="{ row }">
          <vxe-input v-model="row.emissionBandwidth" type="number" placeholder="MHz" />
        </template>
      </vxe-column>

      <vxe-column
        field="transmissionPower"
        title="发射功率"
        min-width="120"
        align="center"
        :edit-render="{ defaultSlot: true }"
      >
        <template #default="{ row }">
          {{ row.transmissionPower }}
        </template>
        <template #edit="{ row }">
          <vxe-input v-model="row.transmissionPower" type="number" placeholder="W" />
        </template>
      </vxe-column>

      <vxe-column
        field="areaStr"
        title="使用区域"
        min-width="200"
        align="center"
        :edit-render="{ defaultSlot: true }"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
        <template #default="{ row }">
          <div class="cursor-pointer" :class="{ 'text-[#00603b] underline': !row.areaStr }">
            {{ row.areaStr || '设置区域' }}
          </div>
        </template>
      </vxe-column>

      <vxe-column
        field="useStartTime"
        title="用频起始时间"
        min-width="180"
        align="center"
        :edit-render="{ defaultSlot: true }"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
        <template #default="{ row }">
          {{ row.useStartTime }}
        </template>
        <template #edit="{ row }">
          <vxe-input
            v-model="row.useStartTime"
            transfer
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择起始时间"
          />
        </template>
      </vxe-column>

      <vxe-column
        field="useEndTime"
        title="用频截止时间"
        min-width="180"
        align="center"
        :edit-render="{ defaultSlot: true }"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      >
        <template #default="{ row }">
          {{ row.useEndTime }}
        </template>
        <template #edit="{ row }">
          <vxe-input
            v-model="row.useEndTime"
            transfer
            type="datetime"
            format="yyyy-MM-dd HH:mm:ss"
            placeholder="请选择截止时间"
          />
        </template>
      </vxe-column>

      <vxe-column
        field="createTime"
        title="创建时间"
        min-width="180"
        align="center"
        show-header-overflow
        show-overflow="title"
        show-footer-overflow
      />

      <vxe-column title="操作" fixed="right" align="center" width="80">
        <template #default="{ rowIndex }">
          <cu-button content="删除" type="info" @click="removeEquipmentList(rowIndex)"> </cu-button>
        </template>
      </vxe-column>
    </vxe-table>
  </cu-dialog>

  <!-- 自定义组件/默认输入框 -->
  <cu-area-dialog
    v-if="areaDialog"
    v-model="areaDialog"
    :usage-area-entity="currentUsageAreaEntity"
    @confirm="areaDialogConfirm"
  />
</template>

<style scoped lang="scss"></style>
