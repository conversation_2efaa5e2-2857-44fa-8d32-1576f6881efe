<script setup>
  // 在这里编写逻辑
  const props = defineProps({
    title: {
      type: String,
      default: ''
    },
    list: {
      type: Array,
      default: () => []
    },
    height: {
      type: Number,
      default: 180
    }
  })
</script>

<template>
  <div>
    <div class="text-lg font-bold mb-2">{{ title }} </div>
    <div class="border border-gray-200 overflow-y-auto" :style="{ height: height + 'px' }">
      <div class="m-2 cursor-pointer" v-for="(item, index) in list" :key="index">
        {{ item }}
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
