import request from '@/utils/request'


/** 获取文件信号列表（用于特征提取页面表格展示） */
export const readFileSignalResult = (params) => {
  return request({
    url: '/graph/signalResult/readFileSignalResult',
    method: 'get',
    params
  })
}

/** 获取 通信/雷达 信号文件列表 */
export const readAllSignalResult = (data) => {
  return request({
    url: '/graph/signalResult/readAllSignalResult',
    method: 'post',
    data
  })
}

/** 删除 通信/雷达 信号文件 */
export const deleteSignalResult = (data) => {
  return request({
    url: '/graph/signalResult/deleteSignalResult',
    method: 'post',
    data
  })
}


/** 获取 信号指纹构建 列表 */
export const readAllFingerp = (data) => {
  return request({
    url: '/graph/signalFingerp/readAllFingerp',
    method: 'post',
    data
  })
}

/** 获取 信号指纹构建  ONE */
export const readOneFingerp = (params) => {
  return request({
    url: '/graph/signalFingerp/readOneFingerp',
    method: 'get',
    params
  })
}

/** 新增 信号指纹构建  */
export const insertAllFingerp = (data) => {
  return request({
    url: '/graph/signalFingerp/insert',
    method: 'post',
    data
  })
}

/** 更新 信号指纹构建  */
export const updateAllFingerp = (data) => {
  return request({
    url: '/graph/signalFingerp/update',
    method: 'post',
    data
  })
}

/** 删除 信号指纹构建  */
export const deleteAllFingerp = (data) => {
  return request({
    url: '/graph/signalFingerp/delete',
    method: 'post',
    data
  })
}
/** 删除 信号指纹管理  */
export const deleteByFingerpIds = (data) => {
  return request({
    url: '/graph/signalFingerp/deleteByFingerpIds',
    method: 'post',
    data
  })
}


/** 获取 指纹管理 列表 */
export const readAllFingerpManage = (data) => {
  return request({
    url: '/graph/signalFingerp/readAllFingerpManage',
    method: 'post',
    data
  })
}




/** 获取 权重方案 列表 */
export const readAllWeight = (data) => {
  return request({
    url: '/graph/signalFingerpWeight/readAllWeight',
    method: 'post',
    data
  })
}

/** 保存 权重方案 列表 */
export const saveAllWeight = (data) => {
  return request({
    url: '/graph/signalFingerpWeight/save',
    method: 'post',
    data
  })
}

/** 删除 权重方案 */
export const deleteAllWeight = (data) => {
  return request({
    url: '/graph/signalFingerpWeight/delete',
    method: 'post',
    data
  })
}


/**  平台频谱特征模块  */
/** 查询平台分类树 */
export const readPlatformEquipTree = (params) => {
  return request({
    url: '/knowledge/graph/platform/readPlatformEquipTree',
    method: 'get',
    params
  })
}
/** 查询平台基本信息（包含频谱特征） */
export const readPlatformInfo = (params) => {
  return request({
    url: '/knowledge/graph/platform/readPlatformInfo',
    method: 'get',
    params
  })
}

/** 查询平台装备IQ特征 */
export const readFreqUsingEquip = (params) => {
  return request({
    url: '/knowledge/graph/platform/readFreqUsingEquip',
    method: 'get',
    params
  })
}

/** 查询装备用频参数 */
export const readFreqUsingParam = (params) => {
  return request({
    url: '/knowledge/graph/platform/readFreqUsingParam',
    method: 'get',
    params
  })
}

/** 查询平台类型 */
export const readPlatformClass = (params) => {
  return request({
    url: '/knowledge/graph/platform/readPlatformClass',
    method: 'get',
    params
  })
}

/** 平台信息修改：（包含平台类型、全频段特征（典型场景发射)、全频段特征） */
export const updatePlatform = (data) => {
  return request({
    url: '/knowledge/graph/platform/updatePlatform',
    method: 'post',
    data
  })
}

/** 修改装备文件：重新选择装备IQ文件保存接口 */
export const updateEquipFile = (data) => {
  return request({
    url: '/knowledge/graph/platform/updateEquipFile',
    method: 'post',
    data
  })
}

/** 修改用频参数 */
export const updateFreqUsingParam = (data) => {
  return request({
    url: '/knowledge/graph/platform/updateFreqUsingParam',
    method: 'post',
    data
  })
}

/** 装备频谱特征模块    */
/** 查询装备列表 */
export const readAllEquip = (data) => {
  return request({
    url: '/knowledge/graph/equip/readAllEquip',
    method: 'post',
    data: data,
  })
}
/** 装备修改 */
export const updateEquip = (data) => {
  return request({
    url: '/knowledge/graph/equip/updateEquip',
    method: 'post',
    data: data,
  })
}
/** 装备删除 */
export const deleteEquip = (data) => {
  return request({
    url: '/knowledge/graph/equip/deleteEquip',
    method: 'post',
    data: data,
  })
}


/** 模型匹配模块 */
/** 查询匹配记录 */
export const readAllMatchRecord = (data) => {
  return request({
    url: '/knowledge/graph/model/readAllMatchRecord',
    method: 'post',
    data
  })
}

/** 查询匹配记录——详情 */
export const readMatchInfo = (params) => {
  return request({
    url: '/knowledge/graph/model/readMatchInfo',
    method: 'get',
    params
  })
}

/** 模型匹配（ID） */
export const modelMatch = (params) => {
  return request({
    url: '/knowledge/graph/model/match',
    method: 'get',
    params
  })
}

/**
 * 模型匹配标定
 */
export const modelMatchCalibration = (data) => {
  return request({
    url: '/knowledge/graph/model/insertResult',
    method: 'post',
    data
  })
}

/**
 * 删除匹配记录
 */
export const deleteMatchRecord = (data) => {
  return request({
    url: '/knowledge/graph/model/delete',
    method: 'post',
    data
  })
}


/** 装备识别模块 */
/** 查询识别记录 */
export const readAllDiscernRecord = (data) => {
  return request({
    url: '/knowledge/graph/equip/discern/readAllDiscernRecord',
    method: 'post',
    data
  })
}

/** 查询识别记录——详情 */
export const readDiscernInfo = (params) => {
  return request({
    url: '/knowledge/graph/equip/discern/readDiscernInfo',
    method: 'get',
    params
  })
}

/** 装备识别（ID） */
export const discern = (params) => {
  return request({
    url: '/knowledge/graph/equip/discern/discern',
    method: 'get',
    params
  })
}

/**
 * 装备识别标定
 */
export const equipmentIdentificationCalibration = (data) => {
  return request({
    url: '/knowledge/graph/equip/discern/insertResult',
    method: 'post',
    data
  })
}

/**
 * 涉频行动
 */
export const readFreqAction = (params) => {
  return request({
    url: '/knowledge/graph/equip/discern/readFreqAction',
    method: 'get',
    params
  })
}

/**
 * 删除匹配记录
 */
export const deleteIDentificationRecord = (data) => {
  return request({
    url: 'knowledge/graph/equip/discern/delete',
    method: 'post',
    data
  })
}