<template>
  <vxe-button :class="buttonClasses" :disabled="disabled" :style="buttonStyles">
    {{ content }}
    <slot></slot>
  </vxe-button>
</template>

<script setup>
  import { computed } from 'vue'

  const props = defineProps({
    type: {
      type: String,
      default: 'primary'
    },
    content: {
      type: String,
      default: ''
    },
    width: {
      type: String,
      default: 'auto'
    },
    disabled: {
      type: Boolean,
      default: false
    },
    size: {
      type: String,
      default: 'normal'
    }
  })

  const buttonClasses = computed(() => {
    return [`vxe-button`, props.type, props.disabled ? 'is--disabled' : '', `size-${props.size}`]
  })

  const buttonStyles = computed(() => {
    return {
      width: props.width,
      minWidth: '72px' // 添加最小宽度样式
    }
  })
</script>

<style scoped lang="scss">
  /* 1. 定义“中文”font-face，只在 CJK 范围使用 YaHei */
  @font-face {
    font-family: 'FontChinese';
    src: local('Microsoft YaHei');
    unicode-range: U+4E00-9FFF, /* 常用汉字 */ U+3400-4DBF, /* 扩展 A 区 */ U+20000-2A6DF,
      /* 扩展 B 区 */ U+2A700-2B73F, /* 扩展 C 区 */ U+2B740-2B81F, /* 扩展 D 区 */ U+2B820-2CEAF; /* 扩展 E 区 */
  }

  /* 2. 定义“英文”font-face，只在 Basic Latin 范围使用 Arial */
  @font-face {
    font-family: 'FontLatin';
    src: local('Arial');
    unicode-range: U+0000-00FF; /* 基本拉丁字母+标点 */
  }
  .vxe-button {
    font-size: 16px !important;
    height: 32px !important;
    padding: 0 16px !important;
    font-family: 'FontLatin', 'FontChinese', sans-serif !important;
    transition: background-color 0.3s ease;
    border-radius: 0 !important;

    // 主要按钮样式
    &.primary {
      color: #ffffff !important;
      border: none !important;
      background-color: #00603b !important;
      &:active {
        background-color: #8fccb0 !important;
      }
      &:hover {
        background-color: #00492a !important;
      }
      &.is--disabled {
        background-color: #8fccb0 !important;
      }
    }

    // 次要按钮样式
    &.minor {
      color: #00603b !important;
      background-color: #ffffff !important;
      border: 1px solid #00603b;
      &:hover {
        color: #00492a !important;
        border: 1px solid #00492a !important;
      }
      &:active {
        color: #002817 !important;
        border: 1px solid #002817 !important;
      }
      &.is--disabled {
        color: #8fccb0 !important;
        border: 1px solid #8fccb0 !important;
      }
    }

    // 文本按钮样式
    &.info {
      color: #00603b !important;
      border: none !important;
      background-color: transparent !important; // 确保背景透明
      min-width: auto !important;
      padding: 0 !important;
      &:hover {
        color: #00492a !important;
        background-color: transparent !important; // hover 时保持背景透明
      }

      &:active {
        color: #002817 !important;
        background-color: transparent !important; // active 时保持背景透明
      }

      &.is--disabled {
        color: #8fccb0 !important;
        background-color: transparent !important; // disabled 时保持背景透明
      }
    }

    &.size-mini {
      font-size: 14px !important;
      height: 30px !important;
      min-width: 40px !important;
    }

    &.size-large {
      font-size: 18px !important;
      height: 42px !important;
    }
  }
</style>
