<script setup>
  const occupancyThreshold = ref(0) // 占用率阈值
  const signalPowerThreshold = ref(0) // 信号功率阈值

  const emit = defineEmits(['start'])
  const startAnalysis = () => {
    emit('start', {
      occupancyThreshold: occupancyThreshold.value,
      signalPowerThreshold: signalPowerThreshold.value
    })
  }
</script>

<template>
  <cu-title class="mt-4" title="电磁环境分析" />
  <div class="flex items-center mb-4">
    <div class="w-24 mr-8">占用度门限</div>
    <div class="flex items-center">
      <vxe-input v-model="occupancyThreshold" style="width: 120px" type="number" />
      <span class="ml-2">dBμV/m</span>
    </div>
  </div>

  <div class="flex items-center mb-4">
    <div class="w-24 mr-8">信号功率门限</div>
    <div class="flex items-center">
      <vxe-input v-model="signalPowerThreshold" style="width: 120px" type="number" />
      <span class="ml-2">dBμV/m</span>
    </div>
    <cu-button class="ml-auto" content="启动分析" @click="startAnalysis"></cu-button>
  </div>
</template>

<style scoped></style>
