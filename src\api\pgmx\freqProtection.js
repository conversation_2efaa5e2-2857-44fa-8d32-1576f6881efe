import request from '@/utils/request'

/** 新增用频保护需求任务 */
export function addFQTask(data) {
  return request({
    url: '/pgmx/freqProtection/addTask',
    method: 'post',
    data: data
  })
}

/** 读取所有的用频保护任务 */
export function readAllFreqProtection(data) {
  return request({
    url: '/pgmx/freqProtection/readAllFreqProtection',
    method: 'post',
    data: data
  })
}
/** 修改用频保护任务 */
export function updateTaskAndFreqProtection(data) {
  return request({
    url: '/pgmx/freqProtection/updateTaskAndFreqProtection',
    method: 'post',
    data: data
  })
}


/** 删除频率保护任务*/
export function deleteTask(data) {
  return request({
    url: '/pgmx/freqProtection/batchDel',
    method: 'delete',
    data: data
  })
}

