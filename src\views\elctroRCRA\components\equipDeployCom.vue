<script setup>
  const equipDeployInfo = defineModel('equipDeployInfo', { type: Array, default: false })
</script>

<template>
  <div class="w-full my-4">
    <div class="text-lg font-medium mb-2">装备构成信息</div>
    <vxe-table
      border
      :show-header="false"
      :data="equipDeployInfo"
      :row-config="{ isCurrent: true, isHover: true }"
      :edit-config="{ trigger: 'click', mode: 'cell' }"
    >
      <!-- 参数名列 -->
      <vxe-column field="label" title="参数名" width="120" align="center"></vxe-column>

      <!-- 参数值列 -->
      <vxe-column field="value" title="参数值" align="center" :edit-render="{}">
        <template #default="{ row }">
          <span>{{ row.value || '-' }}</span>
        </template>
        <template #edit="{ row }">
          <vxe-input v-model="row.value" type="float"></vxe-input>
        </template>
      </vxe-column>
    </vxe-table>
  </div>
</template>

<style lang="scss" scoped>
  .vxe-table {
    border-radius: 4px;
    overflow: hidden;

    .vxe-cell {
      padding: 8px 12px;
    }

    .vxe-table--edit-cell {
      background-color: #f5f7fa;
    }
  }
</style>
