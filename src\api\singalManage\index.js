import request from '@/utils/request'

//信号文件列表查询
export function allSignalListGet(data) {
  return request({
    url: '/file/selectfile',
    method: 'post',
    data: data
  })
}
//单个文件列表查询
export function signalListGet(data) {
  return request({
    url: '/file',
    method: 'get',
    params: data
  })
}
//信号文件新增
export function signalListAdd(data) {
  return request({
    url: '/file/add',
    method: 'post',
    data: data
  })
}
//信号文件修改
export function signalListEdit(data) {
  return request({
    url: '/file/edit',
    method: 'put',
    params: data
  })
}

//信号文件导入
export function signalFileExport(data, params) {
  return request({
    url: '/file/importData',
    method: 'post',
    data,
    params,
    transformRequest: [
      function (data) {
        // 将请求数据转换成功 formdata 接收格式
        const formdata = new FormData()
        formdata.append('file', data)
        return formdata
      }
    ],
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded'
    }
  })
}

//信号文件下载
export function signalFileLoad(data) {
  return request({
    url: '/file/export',
    method: 'get',
    params: data,
    headers: {
      'Content-Type': 'application/octet-stream'
    },
    responseType: 'arraybuffer'
  })
}
//获取字典数据
export function signalDataGet(query) {
  return request({
    url: '/file/dataType',
    method: 'get',
    params: query
  })
}

//信号文件删除
export function signalDataDelete(ids) {
  return request({
    url: '/file/' + ids,
    method: 'delete'
  })
}


//检查文件路径是否存在
export function checkFileExist(query) {
  return request({
    url: '/file/checkFileExist',
    method: 'get',
    params: query
  })
}



