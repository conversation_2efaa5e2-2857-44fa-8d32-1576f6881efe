<script setup>
  import { ref, onMounted, watch } from 'vue'
  import Highcharts from 'highcharts'
  import Data from 'highcharts/modules/data'

  // 初始化模块
  Data(Highcharts)

  const isPower = ref(false) // 可选：用来切换「数量」/「功率」
  // Highcharts 实例
  let chart = null

  // 时间段分类
  const categories = [
    '23:00-05:00',
    '05:00-07:00',
    '07:00-09:00',
    '09:00-12:00',
    '12:00-17:00',
    '17:00-20:00',
    '20:00-23:00'
  ]

  const countData = [62, 94, 245, 422, 1770, 790, 108] // 数量数据
  const powerData = [68, 105, 180, 713, 708, 232, 172]

  // 渲染或刷新图表
  function renderChart() {
    const container = document.getElementById('chartContainer')
    if (!container) return

    // 如果已经存在实例，就先销毁
    if (chart) {
      chart.destroy()
    }

    chart = Highcharts.chart(container, {
      chart: {
        type: 'line',
        margin: [50, 50, 120, 80],
        backgroundColor: '#00aaff'
      },
      title: {
        text: isPower.value ? '电磁辐射时间功率分布' : '电磁辐射时间数量分布'
      },
      xAxis: {
        categories, // 时间段分类
        crosshair: true, // 启用十字准线
        gridLineWidth: 1
      },
      yAxis: {
        title: { text: isPower.value ? '功率' : '数量' },
        min: 0,
        gridLineDashStyle: 'Dash'
      },
      tooltip: {
        shared: true,
        headerFormat: '<b>{point.key}</b><br>',
        pointFormat: `${isPower.value ? '功率' : '数量'}：{point.y}<br>`
      },
      plotOptions: {
        line: {
          dataLabels: {
            enabled: true,
            formatter() {
              return this.y
            }
          },
          enableMouseTracking: true
        }
      },
      series: [
        {
          name: '数量',
          data: isPower.value ? powerData : countData,
          color: '#ffffff',
          marker: {
            fillColor: '#ffffff',
            lineWidth: 2,
            lineColor: null
          }
        }
      ],
      legend: {
        enabled: false
      },
      accessibility: { enabled: false },
      credits: { enabled: false }
    })
  }

  onMounted(renderChart)

  // 如果想切换功率/数量，只需 watch isPower 并重新 renderChart 即可
  watch(isPower, renderChart)

  // 窗口尺寸变更时重绘
  window.addEventListener('resize', () => {
    chart?.reflow()
  })
</script>

<template>
  <div>
    <div class="flex justify-between items-center mb-2">
      <div>电磁辐射时间数量分布</div>
      <cu-button :content="isPower ? '数量统计' : '功率统计'" @click="isPower = !isPower" />
    </div>
    <div id="chartContainer" style="width: 100%; height: 400px"></div>
  </div>
</template>

<style scoped></style>
