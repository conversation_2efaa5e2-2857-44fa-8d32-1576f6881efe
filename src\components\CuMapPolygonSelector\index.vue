<template>
  <div style="display: flex; gap: 12px; margin-bottom: 10px">
    <vxe-select v-model="type" style="width: 140px">
      <vxe-option
        v-for="item in tools"
        :key="item.value"
        :value="item.value"
        :label="item.label"
      ></vxe-option>
    </vxe-select>
    <cu-button @click="resetDraw" type="info" content="重置区域"></cu-button>
  </div>
  <div id="openLayers"></div>
</template>

<script setup>
  import 'ol/ol.css'
  import Map from 'ol/Map'
  import View from 'ol/View'
  import TileLayer from 'ol/layer/Tile'
  import XYZ from 'ol/source/XYZ'
  import FullScreen from 'ol/control/FullScreen' // 导入全屏控件
  import VectorLayer from 'ol/layer/Vector'
  import VectorSource from 'ol/source/Vector'
  import Draw from 'ol/interaction/Draw'
  import Style from 'ol/style/Style'
  import Stroke from 'ol/style/Stroke'
  import Fill from 'ol/style/Fill'
  import CircleStyle from 'ol/style/Circle'
  import gcj02Mecator from '@/utils/gcj02Mecator.js'

  const emits = defineEmits(['change'])
  // 工具列表
  const tools = [
    { value: 'Polygon', label: '多边形' },
    { value: 'Circle', label: '圆' }
  ]
  const type = ref('Polygon')

  let map = null
  let draw = null

  // 用 ref 存放 VectorSource，避免热更新丢失
  const source = new VectorSource({ wrapX: false })

  // 初始化地图
  const initMap = () => {
    // 创建高德地图图层
    const tileLayer = new TileLayer({
      source: new XYZ({
        projection: gcj02Mecator,
        url: 'http://wprd0{1-4}.is.autonavi.com/appmaptile?lang=zh_cn&size=1&style=7&x={x}&y={y}&z={z}'
      })
    })
    const vector = new VectorLayer({
      source,
      style: new Style({
        fill: new Fill({ color: 'rgba(0,0,0,0)' }),
        stroke: new Stroke({ width: 2, color: '#00603b' }),
        image: new CircleStyle({
          radius: 5,
          fill: new Fill({ color: 'rgba(0,0,0,0)' })
        })
      })
    })

    map = new Map({
      target: 'openLayers',
      layers: [tileLayer, vector],
      view: new View({
        projection: 'EPSG:4326',
        center: [118.815588, 32.032081],
        zoom: 14,
        maxZoom: 18
      }),
      // 添加全屏控件
      controls: [
        new FullScreen() // 启用全屏按钮
      ]
    })

    addInteraction()
  }

  // 添加绘制交互
  const addInteraction = () => {
    if (draw) {
      map.removeInteraction(draw)
      draw = null
    }
    draw = new Draw({
      source,
      type: type.value,
      style: new Style({
        fill: new Fill({ color: 'rgba(0,0,0,0)' }), // 透明！
        stroke: new Stroke({ width: 2, color: '#f00' }),
        image: new CircleStyle({
          radius: 5,
          fill: new Fill({ color: 'rgba(0,0,0,0)' })
        })
      })
    })
    draw.on('drawend', e => {
      resetDraw()
      let coords = []
      const geom = e.feature.getGeometry()
      if (type.value === 'Polygon') {
        coords = geom.getCoordinates()[0]
      } else if (type.value === 'Circle') {
        coords = {
          center: geom.getCenter(),
          radius: geom.getRadius()
        }
      }
      emits('change', { type: type.value, coords })
    })
    map.addInteraction(draw)
  }

  const resetDraw = () => {
    source.clear()
    emits('change', [])
  }

  // 监听类型切换
  watch(type, () => {
    addInteraction()
  })

  // 组件挂载时初始化地图
  onMounted(() => {
    initMap()
  })

  // 销毁地图资源
  onBeforeUnmount(() => {
    if (map) map.setTarget(null)
  })
</script>

<style scoped>
  #openLayers {
    width: 100%;
    height: 400px;
    margin: 0 auto;
    border: 1px solid #42b983;
    position: relative;
  }
</style>
