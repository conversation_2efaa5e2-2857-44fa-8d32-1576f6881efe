<script setup>
  import { ref, onMounted, watch } from 'vue'
  import Highcharts from 'highcharts'
  import { round } from 'lodash'

  const props = defineProps({
    // 表格数据
    list: {
      type: Array,
      default: () => []
    },
    // 图表数据，期望结构：
    // { xAxisData: ['30','40',…], seriesData: [12, 34, …] }
    chartData: {
      type: Object,
      default: () => ({ xAxisData: [], seriesData: [] })
    }
  })
  const emit = defineEmits(['start'])
  const freqOR = ref(0)
  let chartInstance = null

  const monitorStatistics = () => {
    emit('start', freqOR.value)
  }

  const renderChart = () => {
    chartInstance = Highcharts.chart('container', {
      chart: {
        type: 'column',
        margin: [50, 50, 70, 80]
      },
      title: {
        text: '监测大信号'
      },
      xAxis: {
        categories: props.chartData.xAxisData,
        title: { text: '频率 (MHz)' },
        labels: { rotation: 0, style: { fontSize: '12px' } }
      },
      yAxis: {
        title: { text: '功率 (dBμV/m)' },
        tickInterval: 10,
        min: 0
      },
      tooltip: {
        formatter() {
          return `<b>${this.x} MHz</b><br/>功率: ${this.y} dBμV/m`
        }
      },
      plotOptions: {
        line: {
          marker: { enabled: true, radius: 4 },
          dataLabels: {
            enabled: true,
            formatter() {
              return this.y
            }
          }
        }
      },
      credits: { enabled: false },
      legend: { enabled: false },
      accessibility: {
        enabled: false
      },
      series: [
        {
          name: '信号功率',
          data: props.chartData.seriesData
        }
      ]
    })
    window.addEventListener('resize', () => chartInstance.reflow())
  }

  onMounted(() => {})

  // 监听 chartData，动态更新图表
  watch(
    () => props.chartData,
    data => {
      if (!chartInstance) {
        renderChart()
      }
      // 更新横坐标
      chartInstance.xAxis[0].setCategories(data.xAxisData || [], false)
      // 更新数据
      chartInstance.series[0].setData(data.seriesData || [], true)
    },
    { deep: true }
  )
</script>

<template>
  <cu-title class="mt-4" title="监测信号统计" />
  <div class="flex items-center mb-4">
    <div class="mr-8">频率占用可信度</div>
    <div class="flex items-center">
      <vxe-input v-model="freqOR" style="width: 80px" type="number" />
      <span class="ml-2">dBμV/m</span>
    </div>
    <cu-button class="ml-12" type="primary" content="监测信号统计" @click="monitorStatistics" />
  </div>
  <div v-show="list.length" id="container" style="width: 100%; height: 360px; margin-bottom: 6px">
  </div>

  <div v-if="list.length">
    <div class="flex justify-between items-center mb-2">
      <div>监测信号清单</div>
      <cu-button content="导出"></cu-button>
    </div>
    <vxe-table
      :data="list"
      border
      stripe
      size="medium"
      min-height="160"
      :row-config="{ isCurrent: true, isHover: true }"
    >
      <vxe-column type="seq" width="60" align="center" />
      <vxe-column field="signalFrequency" title="信号频率 (MHz)" align="center">
        <template #default="{ row }">
          {{ round(row.signalFrequency / 1e6, 4) }}
        </template>
      </vxe-column>
      <vxe-column field="signalBandwidth" title="信号带宽 (kHz)" align="center">
        <template #default="{ row }">
          {{ round(row.signalBandwidth / 1e3, 3) }}
        </template>
      </vxe-column>
      <vxe-column field="signalPower" title="信号功率 (dBμV/m)" width="160" align="center" />
    </vxe-table>
  </div>
</template>

<style scoped lang="scss">
  #container {
    /* 高度可按需调整 */
    height: 360px;
  }
</style>
