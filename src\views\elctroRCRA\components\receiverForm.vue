<script setup>
  // 在这里编写逻辑
  import { receiverFormConfig } from '../config/receiverFormConfig.js'

  const props = defineProps({
    receiverFormData: {
      type: Object,
      default: () => {}
    }
  })

  const formOptions = computed(() => {
    return {
      titleColon: true,
      // verticalAlign: 'right',
      data: props.receiverFormData,
      rules: {
        name: [{ required: true, message: '请输入接收机名称' }],
        code: [{ required: true, message: '请输入接收机编码' }],
        freqCap: [{ required: true, message: '请输入频率上限' }],
        freqLow: [{ required: true, message: '请输入频率下限' }],
        sensitivity: [{ required: true, message: '请输入灵敏度' }]
      },
      items: receiverFormConfig
    }
  })
</script>

<template>
  <vxe-form v-bind="formOptions"></vxe-form>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
