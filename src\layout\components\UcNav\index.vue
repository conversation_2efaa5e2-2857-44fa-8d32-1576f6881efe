<template>
  <div class="uc-nav">
    <div class="uc-title">
      {{ title }}
    </div>
    <uc-box :style="{ width: getWidth(3) }" :height="72" class="ml-4">
      <PageNav />
    </uc-box>
    <uc-box :style="{ width: getWidth(1) }" class="ml-2">
      <div class="uc-nav-right">
        <div class="ucnav-item mr-5">
          <x-icon
            :icon="fullScreenIcon"
            source="cus"
            :size="22"
            color="#00BFBF"
            @click="fullScreen"
          />
        </div>
        <ThemeToggle v-show="false" />
        <div class="ucnav-item" style="width: 80px">
          <PersonCenter />
        </div>
      </div>
    </uc-box>
  </div>
</template>

<script setup>
  import UcBox from './Box.vue'
  import PageNav from './PageNav.vue'
  import ThemeToggle from '../themeToggle'
  import PersonCenter from '../PersonCenter'
  import { computed } from 'vue'
  // const title = ref(import.meta.env.VITE_GLOB_APP_TITLE)
  const title = ref(null)
  const isFullScreen = ref(false)
  const getWidth = (num = 3) => {
    const winWidth = window.innerWidth
    return ((winWidth - 300) * num) / 4 + 'px'
  }
  const fullScreenIcon = computed(() => {
    if (isFullScreen.value) {
      return 'fullscreen'
    } else {
      return 'exit-fullscreen'
    }
  })
  const fullScreen = () => {
    if (isFullScreen.value) {
      isFullScreen.value = false
      exitFullScreen()
      return
    }
    isFullScreen.value = true
    const element = document.documentElement
    if (element.requestFullscreen) {
      element.requestFullscreen()
    } else if (element.mozRequestFullScreen) {
      // 兼容火狐
      element.mozRequestFullScreen()
    } else if (element.webkitRequestFullscreen) {
      // 兼容谷歌
      element.webkitRequestFullscreen()
    } else if (element.msRequestFullscreen) {
      // 兼容IE
      element.msRequestFullscreen()
    }
  }
  const exitFullScreen = () => {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen()
    } else if (document.webkitCancelFullScreen) {
      document.webkitCancelFullScreen()
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen()
    }
  }

  onMounted(() => {
    fetch('/config.json')
      .then(response => response.json())
      .then(data => {
        // 根据配置文件中的title字段更新页面标题
        title.value = data.title
      })
      .catch(error => {
        console.error('读取配置文件出错:', error)
      })
  })
</script>

<style lang="scss">
  .uc-nav {
    height: 90px;
    padding: 8px;
    display: flex;

    .uc-title {
      background: url(@/assets/images/sidebarDialog.svg) no-repeat;
      background-size: cover;
      line-height: 48px;
      height: 80px;
      color: #fff;
      text-indent: 18px;
      font-size: 20px;
      width: 235px;
      font-weight: bold;
      font-style: italic;
    }

    .uc-nav-right {
      display: flex;
      justify-content: right;
      height: 100%;

      .ucnav-item {
        height: 100%;
        width: 50px;
        color: #fff;
        display: flex;
        align-items: center;
      }
    }
  }
</style>
