import {
  uploadFile,
  mergeChunks,
  uploadFilePre,
  uploadRadarFile,
  mergeRadarChunks,
  uploadRadarFilePre,
} from './upload'
import { getFileChunk, DefaultChunkSize } from './chunk'
import { fileChunkList, fileHash, setFileHash, clearFileHash } from './file'

/**
 * 上传文件
 * @param file 要上传的文件对象
 * @param updateProcess 更新上传进度的回调函数
 * @param params 上传所需的额外参数
 */
export const upload = async (file, updateProcess, params) => {
  let chunkList = null
  console.log(fileHash, 'fileHash');

  // 如果是首次上传
  if (!fileHash) {
    setFileHash(await getFileChunk(file, params))
    chunkList = fileChunkList.value
  }
  // 如果是已经上传过部分文件（由于网络不稳定等原因导致上传中断）
  else {
    const rsp = await uploadFilePre({ md5: fileHash })
    if (rsp.data && rsp.data.length > 0) {
      const existArr = rsp.data.map(item => parseInt(item))
      const indexSet = new Set(existArr)
      // 过滤掉已经上传过的分块
      chunkList = fileChunkList.value.filter((_, index) => !indexSet.has(index))
    }
  }

  // 顺序上传每个分块
  for (let i = 0; i < chunkList.length; i++) {
    const item = chunkList[i]
    const formData = new FormData()
    formData.append('file', item.chunk)
    formData.append('num', item.num)
    formData.append('sum', fileChunkList.value.length)
    formData.append('md5', fileHash)
    formData.append('fileParams', JSON.stringify({ ...item }))

    // 等待当前分块上传完成后再进行下一个
    await uploadFile(formData, updateProcess(i))
  }

  // 合并分块
  const formData = new FormData()
  formData.append('fileName', file.name)
  formData.append('md5', fileHash)
  await mergeChunks(formData, params)

  // 清理状态
  fileChunkList.value = []
  clearFileHash(true)
}
export const uploadRadar = async (file, updateProcess, params) => {
  let chunkList = null
  if (!fileHash) {
    setFileHash(await getFileChunk(file, params))
    chunkList = fileChunkList.value
  } else {
    const rsp = await uploadRadarFilePre({ md5: fileHash })
    if (rsp.data && rsp.data.length > 0) {
      const existArr = rsp.data.map(item => parseInt(item))
      const indexSet = new Set(existArr)
      chunkList = fileChunkList.value.filter((_, index) => !indexSet.has(index))
    }
  }
  const requests = chunkList.map((item, index) => {
    const formData = new FormData()
    formData.append('file', item.chunk)
    formData.append('num', item.num)
    formData.append('sum', fileChunkList.value.length)
    formData.append('md5', fileHash)
    return uploadRadarFile(formData, updateProcess(item))
  })
  await Promise.all(requests)
  const formData = new FormData()
  formData.append('fileName', file.name)
  formData.append('md5', fileHash)
  await mergeRadarChunks(formData, params)
  fileChunkList.value = []
  clearFileHash(true)
}