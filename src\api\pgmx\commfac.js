import request from '@/utils/request'

/** 查询通信设施层级树 */
export function queryCommFacTypeTree(data) {
  return request({
    url: '/pgmx/comm-fac/queryCommFacTypeTree',
    method: 'post',
    data: data
  })
}

/** 通过通信设备类型查询通信设备 */
export function queryCommByType(data) {
  return request({
    url: '/pgmx/comm-fac/queryCommByType',
    method: 'get',
    params: data
  })
}

/** 删除通信设备 */
export function deleteCMFac(data) {
  return request({
    url: '/pgmx/comm-fac/delete',
    method: 'delete',
    data: data
  })
}


/** 添加/修改 通信设备 */
export function saveOrUpdateCMFac(data) {
  return request({
    url: '/pgmx/comm-fac/saveOrUpdate',
    method: 'post',
    data: data
  })
}


/** 通信设施能力查询 */
export function commCap(data) {
  return request({
    url: '/pgmx/comm-fac/commCap',
    method: 'post',
    data: data
  })
}

/** 信息设施分布查询 */
export function commFacilities(data) {
  return request({
    url: '/pgmx/comm-fac/commFacilities',
    method: 'post',
    data: data
  })
}








