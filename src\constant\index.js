export const EYE_DOT = {
  dotsNumLine: 33, // 眼图一条线几个点
  baseDotsNum: 3, // 几个点来自于后台/作为基准点
  midDotsNum: 15 // 几个点作为中间点
}

export const BINARY2_16 = {
  '0000': '0',
  '0001': '1',
  '0010': '2',
  '0011': '3',
  '0100': '4',
  '0101': '5',
  '0110': '6',
  '0111': '7',
  1000: '8',
  1001: '9',
  1010: 'A',
  1011: 'B',
  1100: 'C',
  1101: 'D',
  1110: 'E',
  1111: 'F'
}

// 0 代表频率 1 代表时间 2 代表幅度 dBm
export const RATE_UNIT = { key: 0, default: 'MHz' }
export const TIME_UNIT = { key: 1, default: 'us' }
export const AM_UNIT = { key: 2, default: 'dBm' }

export const lineTypes = [
  { label: '实时', value: 'current' },
  { label: '平均', value: 'average' },
  { label: '最大', value: 'max' },
  { label: '最小', value: 'min' },
  { label: '门限', value: 'limit' }
]
