<script setup>
  import electroEquipInfoCom from '../components/electroEquipInfoCom.vue'
  import partsInfoCom from '../components/partsInfoCom.vue'
  // 在这里编写逻辑
  const activeName = ref('electroEquipInfo')
</script>

<template>
  <!-- 在这里编写模板 -->
  <div class="flex justify-between">
    <cu-title title="电磁频谱资源管理" />
    <cu-button content="信息导入"></cu-button>
  </div>
  <el-tabs v-model="activeName" type="border-card" class="freq-tabs">
    <el-tab-pane label="电磁装备信息" name="electroEquipInfo">
      <electroEquipInfoCom></electroEquipInfoCom>
    </el-tab-pane>
    <el-tab-pane label="部件信息" name="partsInfo">
      <partsInfoCom></partsInfoCom>
    </el-tab-pane>
  </el-tabs>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
  .freq-tabs {
    :deep(.el-tabs__nav) {
      display: flex;
      width: 100%;
    }

    :deep(.el-tabs__item) {
      flex: 1;
      text-align: center;
      padding: 0 16px;
      line-height: 40px;
    }
  }
</style>
