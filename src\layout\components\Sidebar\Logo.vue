<template>
  <div
    class="sidebar-logo-container"
    :class="{ collapse: collapse }"
    :style="{
      backgroundColor:
        sideTheme === 'theme-dark' ? variables.menuBackground : variables.menuLightBackground
    }"
  >
    <transition name="sidebarLogoFade">
      <router-link v-if="collapse" key="collapse" class="sidebar-logo-link" to="/">
        <img v-if="logo" :src="logo" class="sidebar-logo" />
      </router-link>
      <router-link v-else key="expand" class="sidebar-logo-link" to="/">
        <h1
          class="sidebar-title"
          :style="{
            color:
              sideTheme === 'theme-dark' ? variables.logoTitleColor : variables.logoLightTitleColor
          }"
        >
          {{ title }}
        </h1>
      </router-link>
    </transition>
  </div>
</template>

<script setup>
  import variables from '@/assets/styles/variables.module.scss'
  import logo from '@/assets/logo/logo.png'
  import useSettingsStore from '@/store/modules/settings'

  defineProps({
    collapse: {
      type: Boolean,
      required: true
    }
  })

  const title = ref(null)
  const settingsStore = useSettingsStore()
  const sideTheme = computed(() => settingsStore.sideTheme)

  onMounted(() => {
    fetch('/config.json')
      .then(response => response.json())
      .then(data => {
        // 根据配置文件中的title字段更新页面标题
        title.value = data.title
      })
      .catch(error => {
        console.error('读取配置文件出错:', error)
      })
  })
</script>

<style lang="scss" scoped>
  .sidebarLogoFade-enter-active {
    transition: opacity 1.5s;
  }

  .sidebarLogoFade-enter,
  .sidebarLogoFade-leave-to {
    opacity: 0;
  }

  .sidebar-logo-container {
    position: relative;
    width: 100%;
    height: 50px;
    line-height: 50px;
    background: #00603b;
    text-align: center;
    overflow: hidden;

    & .sidebar-logo-link {
      height: 100%;
      width: 100%;

      & .sidebar-logo {
        width: 32px;
        height: 32px;
        vertical-align: middle;
        margin-right: 12px;
      }

      & .sidebar-title {
        display: inline-block;
        margin: 0;
        color: #fff;
        font-weight: 600;
        line-height: 50px;
        font-size: 16px;
        vertical-align: middle;
      }
    }

    &.collapse {
      .sidebar-logo {
        margin-right: 0px;
      }
    }
  }
</style>
