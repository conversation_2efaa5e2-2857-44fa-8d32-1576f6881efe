# 页面标题
VITE_GLOB_APP_TITLE = 信息环境分析平台
VITE_PUBLIC_PATH = ./

# 生产环境配置
VITE_APP_ENV = 'production'
# 预览地址（使用相对路径，Nginx 会将该请求代理到后端）
VITE_GLOB_APP_PREVIEW = '/dev-api'

# 管理系统/生产环境接口地址（使用相对路径）
VITE_GLOB_APP_BASE_API = '/dev-api'

# 是否在打包时开启压缩，支持 gzip 和 brotli
VITE_BUILD_COMPRESS = gzip

# websocket 地址（使用相对路径，Nginx 配置单独的 WebSocket 转发）
VITE_GLOB_WS = '/ws'

# 对外暴露的主机与端口（与 Nginx 配置保持一致）
VITE_GLOB_HOST = '*************'
VITE_GLOB_PORT = '4444'

# 上位机模式
VITE_GLOB_UPPER_COMPUTER_MODE = 0

# 试用环境配置
VITE_APP_TRAIL = 1
