<script setup>
  import editDialog from '../components/editDialog.vue'
  import {
    queryCommFacTypeTree,
    queryCommByType,
    saveOrUpdateCMFac,
    deleteCMFac
  } from '@/api/pgmx/commfac.js'
  import { ElMessage } from 'element-plus'

  const tableRef = ref(null)
  const name = ref('') // 通信设施名称
  const loading = ref(false)
  const dialogTableVisible = ref(false)
  const treeList = ref([]) // 树形列表
  const planData = ref([]) // 计划数据
  const currentNodeId = ref('') //当前节点对应的设备类型id
  const currentRowId = ref('')
  const currentRowValue = ref({})
  const openType = ref('add')

  // 节点点击回调
  const handleNodeClick = ({ node, event }) => {
    if (node.hasChild === 0) return
    loading.value = true
    currentNodeId.value = node.id
    queryCommByType({
      facType: node.id
    }).then(res => {
      loading.value = false
      planData.value = res.data
    })
  }

  const handleQuery = () => {
    queryCommByType({
      facType: currentNodeId.value,
      facName: name.value
    })
      .then(res => {
        planData.value = res.data
      })
      .catch(err => {
        console.log('查询失败：', err)
      })
  }

  const handleAdd = () => {
    openType.value = 'add'
    dialogTableVisible.value = true
  }

  const handleImport = () => {
    console.log('导入')
  }

  const handleExport = () => {
    console.log('导出')
  }

  const handleEdit = row => {
    openType.value = 'edit'
    currentRowId.value = row.id
    currentRowValue.value = row
    dialogTableVisible.value = true
  }

  const handleDelete = row => {
    console.log('删除')
    tableRef.value.remove(row)
    deleteCMFac([row.id]).then(res => {
      if (res.code === 200) {
        ElMessage({ type: 'success', message: res.msg })
        queryCommByType({
          facType: currentNodeId.value
        }).then(res => {
          loading.value = false
          planData.value = res.data
        })
      } else ElMessage({ type: 'error', message: res.msg })
    })
  }

  const updateFac = queryData => {
    if (openType.value === 'add') {
      queryData.id = null
    } else {
      queryData.id = currentRowId.value
    }
    queryData.facType = currentNodeId.value
    saveOrUpdateCMFac(queryData).then(res => {
      if (res.code === 200) {
        dialogTableVisible.value = false
        ElMessage({ type: 'success', message: res.msg })
        queryCommByType({
          facType: currentNodeId.value
        }).then(res => {
          loading.value = false
          planData.value = res.data
        })
      } else ElMessage({ type: 'error', message: res.msg })
    })
  }

  onMounted(() => {
    queryCommFacTypeTree().then(res => {
      if (res.code === 200) treeList.value = res.data
    })
  })
</script>

<template>
  <el-row :gutter="10">
    <el-col :span="5">
      <vxe-tree :is-current="true" :is-hover="true" :data="treeList" @node-click="handleNodeClick">
        <template #icon="{ isExpand }">
          <vxe-icon v-if="isExpand" status="success" name="square-minus" />
          <vxe-icon v-else status="success" name="square-plus" />
        </template>
      </vxe-tree>
    </el-col>
    <el-col v-if="currentNodeId" :span="19">
      <vxe-toolbar class="mb-2">
        <template #buttons>
          <vxe-input v-model="name" placeholder="通信设施名称"></vxe-input>
          <cu-button content="筛选" @click="handleQuery" />
          <cu-button content="新增" @click="handleAdd" />
          <cu-button content="导入" @click="handleImport" />
          <cu-button content="导出" @click="handleExport" />
        </template>
      </vxe-toolbar>
      <vxe-table
        :data="planData"
        :row-config="{ isCurrent: true, isHover: true }"
        border
        stripe
        min-width="300"
      >
        <vxe-column type="seq" width="60" align="center" />
        <vxe-column field="facName" min-width="100" title="设施名称" align="center" />
        <vxe-column field="status" min-width="100" title="设施状态" align="center" />
        <vxe-column field="unit" min-width="120" title="归属单位" align="center" />
        <vxe-column
          field="facType"
          min-width="120"
          title="产品型号"
          align="center"
          show-header-overflow
          show-overflow="title"
          show-footer-overflow
        />
        <vxe-column title="操作" width="120" fixed="right" align="center">
          <template #default="{ row }">
            <cu-button type="info" content="编辑" @click="handleEdit(row)"></cu-button>
            <cu-button type="info" content="删除" @click="handleDelete(row)"></cu-button>
          </template>
        </vxe-column>
      </vxe-table>
    </el-col>
  </el-row>

  <edit-dialog
    v-if="dialogTableVisible"
    v-model="dialogTableVisible"
    :type="openType"
    :current-row="currentRowValue"
    @confirm="updateFac"
  ></edit-dialog>
</template>

<style scoped lang="scss"></style>
