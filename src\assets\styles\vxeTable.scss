// /* VXE-Table 全局样式配置 */
// .vxe-table {
//   // 表格行高30px
//   // .vxe-header--column,
//   // .vxe-cell {
//   //   height: 30px !important;
//   //   min-height: 30px !important;
//   // }
//   // --vxe-ui-table-border-color: #333333 !important;

//   // 内部单元格边框设置为白色
//   // .vxe-table--body-wrapper .vxe-body--column,
//   // .vxe-table--header-wrapper .vxe-header--column,
//   // .vxe-table--footer-wrapper .vxe-footer--column {
//   // --vxe-ui-table-border-color: #ccc !important;
//   // }

//   // 表头样式
//   .vxe-table--header {
//     color: #ffffff;
//     background-color: #00603b;
//   }

//   // 表格主体样式
//   .vxe-table--body {
//     color: var(#333333);
//     // 斑马纹效果
//     .vxe-body--row {
//       //  奇数行背景色
//       &:nth-child(odd) {
//         background-color: #ffffff !important;
//         > .vxe-body--column {
//           background-color: #ffffff !important;
//         }
//       }
//       // 偶数行背景色
//       &:nth-child(even) {
//         background-color: #d3eadd !important;
//         > .vxe-body--column {
//           background-color: #d3eadd !important;
//         }
//       }

//       // 悬停效果
//       &.row--hover {
//         background-color: #d3eadd !important;
//         > .vxe-body--column {
//           background-color: #d3eadd !important;
//         }
//       }

//       // todo-颜色不确定:当前行效果
//       &.row--current {
//         background-color: #d3eadd !important;
//         > .vxe-body--column {
//           background-color: #d3eadd !important;
//         }
//       }
//     }
//   }

//   // 复选框样式
//   .vxe-cell--checkbox {
//     &.hover {
//       border-color: $color-graph-style !important;
//     }
//   }
// }

// /** 弹出框样式 */
// .vxe-modal--box {
//   border: 0 !important;
// }

// /* 工具栏和分页样式 */
// .vxe-toolbar,
// .vxe-page,
// .vxe-pager--goto,
// .vxe-pager--num-btn,
// .vxe-pager--next-btn,
// .vxe-pager--jump-next,
// .vxe-pager--jump-prev,
// .vxe-pager--prev-btn {
//   background-color: var(--background-color) !important;
//   color: var(--chart-text-color) !important;
// }

// /* 筛选和下拉框样式 */
// .vxe-table--filter-wrapper,
// .vxe-select-option--wrapper {
//   background-color: var(--background-color) !important;
//   border-color: #000 !important; // 外部边框黑色
//   color: var(--chart-text-color) !important;

//   .vxe-table--filter-body > li:hover,
//   .vxe-table--filter-header > li:hover {
//     background-color: var(--scrollbar-color) !important;
//     color: var(--chart-text-color) !important;
//   }
// }

// /* 输入框样式 */
// .vxe-input {
//   border-radius: 0 !important;
//   height: var(--cu-select-height) !important;
//   line-height: var(--cu-select-height) !important;
//   .vxe-input--inner {
//     background-color: var(--background-color) !important;
//     color: var(--chart-text-color) !important;
//     border-color: #fff !important; // 内部边框白色
//   }
// }
// /* 输入框激活状态 */
// .vxe-input.is--active {
//   border: 1px solid #00603b !important;
// }

// // input可删除鼠标激活（被按下）时
// .vxe-input--clear-icon:active,
// .vxe-input--clear-icon.is--active {
//   color: #00603b !important; // 你想要的高亮绿色
// }

// // input可删除激活时样式
// .vxe-select.is--active:not(.is--filter) > .vxe-input {
//   border-color: #00603b !important;
// }

// /*  vxe-textarea 的基本样式 */
// .vxe-textarea {
//   .vxe-textarea--inner {
//     border-radius: 0 !important;
//     background-color: var(--background-color) !important;
//     color: var(--chart-text-color) !important;
//     // border-color: #fff !important; // 默认边框颜色
//     &:focus {
//       border-color: #00603b !important; // 聚焦时边框颜色
//       background-color: var(--background-color) !important;
//       color: var(--chart-text-color) !important;
//     }
//   }
// }

// /* 下拉选项样式 */
// .vxe-select--panel {
//   .vxe-optgroup--title,
//   .vxe-select-option {
//     height: var(--cu-select-height) !important;
//     line-height: var(--cu-select-height) !important;
//     border-color: #fff !important; // 内部边框白色
//   }

//   .vxe-select-option {
//     max-width: 100% !important;
//     /* 下拉框鼠标悬浮样式 */
//     &:not(.is--disabled).is--hover {
//       background-color: #cceddc !important;
//       color: #333 !important;
//     }

//     /* 下拉框选中样式*/
//     &.is--selected {
//       background-color: $color-graph-style !important;
//       color: #fff !important;
//       &.is--hover {
//         background-color: $color-graph-style !important;
//         color: #fff !important;
//       }
//     }
//   }
// }

// /* 复选框选中状态 */
// .is--checked,
// .is--indeterminate {
//   &.vxe-checkbox,
//   &.vxe-cell--checkbox,
//   &.vxe-custom--option,
//   &.vxe-export--panel-column-option,
//   &.vxe-table--filter-option,
//   .vxe-checkbox--icon {
//     color: #00603b !important;
//     &:hover {
//       color: #00603b !important;
//     }
//   }
// }
