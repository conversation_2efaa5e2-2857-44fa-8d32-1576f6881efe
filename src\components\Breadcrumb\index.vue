<template>
  <el-breadcrumb class="app-breadcrumb" separator="/">
    <transition-group name="breadcrumb" tag="div">
      <el-breadcrumb-item v-for="(item, index) in levelList" :key="item.path">
        <!-- 根据 meta.breadcrumbClickable 和是否为最后一项决定是否为链接 -->
        <a
          v-if="index < levelList.length - 1 && item.meta.redirect === undefined"
          @click.prevent="handleLink(item)"
        >
          {{ item.meta.title }}
        </a>
        <span v-else class="no-redirect">
          {{ item.meta.title }}
        </span>
      </el-breadcrumb-item>
    </transition-group>
  </el-breadcrumb>
</template>

<script setup>
  import { ref, watch } from 'vue'
  import { useRoute, useRouter } from 'vue-router'

  // 获取当前路由和路由实例
  const route = useRoute()
  const router = useRouter()
  // 面包屑项列表
  const levelList = ref([])

  /** * 获取面包屑数据 */
  const getBreadcrumb = () => {
    // 获取匹配的路由记录，确保所有层级都有 meta.title
    let matched = route.matched.filter(item => item.meta && item.meta.title)
    // 始终在最前面添加首页
    const homeRoute = { path: '/index', meta: { title: '首页', breadcrumbClickable: true } }
    if (!matched.length || matched[0].path !== '/index') {
      matched = [homeRoute, ...matched]
    }

    // 过滤掉 meta.breadcrumb 为 false 的路由
    levelList.value = matched.filter(
      item => item.meta && item.meta.title && item.meta.breadcrumb !== false
    )
  }

  /**
   * 处理面包屑项的点击事件
   * @param {Object} item - 面包屑项
   */
  const handleLink = item => {
    if (item.children && item.children.length > 0) {
      router.push({ path: item.children[0].path })
      return
    }
    if (item.path) {
      router.push({ path: item.path })
    }
  }

  // 监听路由变化，更新面包屑
  watch(
    () => route.fullPath,
    () => {
      // 如果是重定向页面，则不更新面包屑
      if (route.path.startsWith('/redirect/')) {
        return
      }
      getBreadcrumb()
    },
    { immediate: true }
  )

  // 初始化面包屑
  getBreadcrumb()
</script>

<style lang="scss" scoped>
  .app-breadcrumb.el-breadcrumb {
    display: inline-block;
    font-size: 14px;
    line-height: 52px;
    margin-left: 8px;
    .no-redirect {
      color: #123123;
      cursor: default;
    }
    a {
      cursor: pointer;
      text-decoration: none;

      &:hover {
        text-decoration: underline;
        color: #00603b;
      }
    }
  }
</style>
