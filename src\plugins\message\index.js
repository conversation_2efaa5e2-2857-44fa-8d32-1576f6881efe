// src/plugins/message/index.js
import { createVNode, render } from 'vue'
import MessageComponent from '../../components/CuMessage/index.vue'

// 用于存储消息DOM容器
let messageContainer = null

// 创建DOM容器
const createContainer = () => {
  const container = document.createElement('div')
  container.className = 'message-container'
  document.body.appendChild(container)
  return container
}

// 创建Message实例
const createMessage = (options) => {
  // 如果容器不存在则创建
  if (!messageContainer) {
    messageContainer = createContainer()
  }

  // 处理选项
  if (typeof options === 'string') {
    options = {
      message: options
    }
  }

  // 创建包装div
  const div = document.createElement('div')

  // 创建vnode
  const vnode = createVNode(MessageComponent, {
    ...options,
    onDestroy: () => {
      // 当消息关闭时删除DOM
      render(null, div)
      if (div.parentNode) {
        div.parentNode.removeChild(div)
      }
    }
  })

  // 渲染vnode到div
  render(vnode, div)

  // 将div添加到容器中
  messageContainer.appendChild(div)

  // 获取组件实例
  const vm = vnode.component.proxy

  return {
    close: () => vm.close()
  }
}

// 定义各种消息类型的快捷方法
const Message = {
  info(options) {
    return createMessage(typeof options === 'string' ? { message: options, type: 'info' } : { ...options, type: 'info' })
  },
  success(options) {
    return createMessage(typeof options === 'string' ? { message: options, type: 'success' } : { ...options, type: 'success' })
  },
  warning(options) {
    return createMessage(typeof options === 'string' ? { message: options, type: 'warning' } : { ...options, type: 'warning' })
  },
  error(options) {
    return createMessage(typeof options === 'string' ? { message: options, type: 'error' } : { ...options, type: 'error' })
  }
}

// 创建Vue插件
const MessagePlugin = {
  install(app) {
    // 将Message方法挂载到app.config.globalProperties上
    app.config.globalProperties.$message = Message

    // 同时也挂载到全局window对象，方便在非组件中使用
    window.$message = Message
  }
}

// 为Composition API提供的辅助函数
export function useMessage() {
  return Message;
}

// 导出
export default MessagePlugin
// 导出单独的方法
export { Message }