<script setup>
  // 在这里编写逻辑
  import { transmitterFormConfig } from '../config/transmitterFormConfig.js'

  const props = defineProps({
    transmitterFormData: {
      type: Object,
      default: () => {}
    }
  })

  const formOptions = computed(() => {
    return {
      titleColon: true,
      // verticalAlign: 'right',
      data: props.transmitterFormData,
      rules: {
        name: [{ required: true, message: '请输入发射机名称' }],
        code: [{ required: true, message: '请输入发射机编码' }],
        freqCap: [{ required: true, message: '请输入发射机频率上限' }],
        freqLow: [{ required: true, message: '请输入发射机频率下限' }],
        peakPower: [{ required: true, message: '请输入峰值功率' }]
      },
      items: transmitterFormConfig
    }
  })
</script>

<template>
  <vxe-form v-bind="formOptions"></vxe-form>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
