<script setup>
  // 对话框可见性
  const dialogTableVisible = defineModel({ type: Boolean, default: false })

  const emits = defineEmits(['confirm'])
  const form = reactive({
    planName: ''
  })

  const confirm = () => {
    dialogTableVisible.value = false
    // 这里编写保存方案的逻辑
    emits('confirm', form.planName)
  }

  const cancel = () => {
    form.planName = ''
    dialogTableVisible.value = false
  }
</script>

<template>
  <cu-dialog
    v-model="dialogTableVisible"
    title="保存用频方案"
    width="400"
    @confirm="confirm"
    @cancel="cancel"
  >
    <div class="mb-4">
      <vxe-form :data="form" :title-colon="true" :label-config="{ width: '120px', align: 'left' }">
        <vxe-form-item field="unit" title="方案名称" span="24">
          <vxe-input v-model="form.planName" placeholder="请输入方案名称" />
        </vxe-form-item>
      </vxe-form>
    </div>
  </cu-dialog>
</template>

<style scoped lang="scss"></style>
