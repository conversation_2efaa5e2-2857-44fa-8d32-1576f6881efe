import request from '@/utils/request'

//训练任务列表查询
export function allTMList(data) {
  return request({
    url: '/train-task/pageList',
    method: 'post',
    data
  })
}

//训练任务新增或修改
export function addOrUpdateTM(data) {
  return request({
    url: '/train-task/saveOrUpdate',
    method: 'post',
    data
  })
}

//训练任务删除
export function deleteTM(data) {
  return request({
    url: '/train-task/batchDel',
    method: 'post',
    data
  })
}

//训练任务导出
export function exportTrainTask(data) {
  return request({
    url: '/train-task/export',
    method: 'get',
    params: data,
    responseType: 'blob'
  })
}

//训练任务状态修改 启动/结束任务
export function changeTMStatus(data) {
  return request({
    url: '/train-task/taskControl',
    method: 'post',
    data
  })
}

// 查询训练任务、答案、步骤 by is
export function getTM(params) {
  return request({
    url: '/train-task/selectOneAll',
    method: 'get',
    params
  })
}

// 保存训练任务、答案、步骤 by is
export function saveTaskAll(data) {
  return request({
    url: '/train-task/saveTaskAll',
    method: 'post',
    data
  })
}

// 提交识别结果
export function saveTaskAnswer(data) {
  return request({
    url: '/train-result/saveOrUpdate',
    method: 'post',
    data
  })
}
// 提交识别结果
export function taskAnswerDetail(params) {
  return request({
    url: '/train/trainDetailByTask',
    method: 'get',
    params
  })
}


//获取任务剩余时间
export function getTaskTime(params) {
  return request({
    url: '/train-task/taskTime',
    method: 'get',
    params
  })
}
// 提前交卷
export function submitTrainAnswer(params) {
  return request({
    url: '/train-result/submitTrain',
    method: 'get',
    params
  })
}











