<script setup>
  // 在这里编写逻辑
  import { antennaFormConfig } from '../config/antennaFormConfig.js'

  const props = defineProps({
    antennaFormData: {
      type: Object,
      default: () => {}
    }
  })

  const formOptions = computed(() => {
    return {
      titleColon: true,
      // verticalAlign: 'right',
      data: props.antennaFormData,
      rules: {
        name: [{ required: true, message: '请输入天线名称' }],
        code: [{ required: true, message: '请输入天线编码' }],
        type: [{ required: true, message: '请选择天线类型' }],
        gain: [{ required: true, message: '请输入增益' }],
        freqCap: [{ required: true, message: '请输入频段上限' }],
        freqLow: [{ required: true, message: '请输入频段下限' }]
      },
      items: antennaFormConfig
    }
  })
</script>

<template>
  <vxe-form v-bind="formOptions"></vxe-form>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
