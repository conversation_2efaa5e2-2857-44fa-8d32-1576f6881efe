<template>
  <div class="navbar">
    <hamburger
      id="hamburger-container"
      :is-active="appStore.sidebar.opened"
      class="hamburger-container"
      @toggleClick="toggleSideBar"
    />
    <breadcrumb
      v-if="!settingsStore.topNav"
      id="breadcrumb-container"
      class="breadcrumb-container"
    />
    <top-nav v-if="settingsStore.topNav" id="topmenu-container" class="topmenu-container" />
    <div class="right-menu">
      <template v-if="appStore.device !== 'mobile'">
        <!-- <header-search id="header-search" class="right-menu-item" />

        <screenfull id="screenfull" class="right-menu-item hover-effect" /> -->

        <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip> -->
      </template>
      <PersonCenter />
    </div>
    <!-- <ThemeToggle /> -->
  </div>
</template>

<script setup>
  // import { ElMessageBox } from 'element-plus'
  import ThemeToggle from './themeToggle'
  import Breadcrumb from '@/components/Breadcrumb'
  import TopNav from '@/components/TopNav'
  import Hamburger from '@/components/Hamburger'
  // import Screenfull from '@/components/Screenfull'
  // import SizeSelect from '@/components/SizeSelect'
  // import HeaderSearch from '@/components/HeaderSearch'
  import useAppStore from '@/store/modules/app'
  import useSettingsStore from '@/store/modules/settings'
  import { useRoute } from 'vue-router'
  import PersonCenter from './PersonCenter'

  defineComponent([ThemeToggle])
  const appStore = useAppStore()
  const settingsStore = useSettingsStore()
  const route = useRoute()
  const toggleSideBar = () => {
    appStore.toggleSideBar()
  }
</script>

<style lang="scss" scoped>
  .navbar {
    height: 50px;
    overflow: hidden;
    position: relative;
    background: var(--el-bg-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

    .hamburger-container {
      line-height: 46px;
      height: 100%;
      float: left;
      cursor: pointer;
      transition: background 0.3s;
      -webkit-tap-highlight-color: transparent;

      &:hover {
        background: rgba(0, 0, 0, 0.025);
      }
    }

    .breadcrumb-container {
      float: left;
    }

    .topmenu-container {
      position: absolute;
      left: 50px;
    }

    .errLog-container {
      display: inline-block;
      vertical-align: top;
    }

    .right-menu {
      float: right;
      height: 100%;
      line-height: 50px;
      display: flex;

      &:focus {
        outline: none;
      }
    }
  }
</style>
