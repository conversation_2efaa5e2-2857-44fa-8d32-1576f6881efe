<script setup>
  // 在这里编写逻辑
  import transmitterForm from './transmitterForm.vue'
  import receiverForm from './receiverForm.vue'
  import antennaForm from './antennaForm.vue'
  import { readEquipmentPartTree, readEquipmentPartInfo } from '@/api/pgmx/equipmentResource.js'
  import { VxeUI } from 'vxe-table'

  const treeList = ref([])

  const formType = ref(null) // 表单类型(1、发射机 2、接收机 3、天线)
  const transmitterFormData = ref(null)
  const receiverFormData = ref(null)
  const antennaFormData = ref(null)

  // 树的懒加载方法
  const getNodeListApi = async ({ node }) => {
    // 如果节点已标记为不包含子节点，则不需要继续请求
    if (node.hasChild === false) return
    // 构建请求参数
    const params = {
      id: node.id
    }
    try {
      const res = await readEquipmentPartTree(params)
      if (res.code === 200) {
        // 更新节点的子节点数据
        return res.data
      }
    } catch (error) {
      console.error('加载子节点失败', error)
    }
  }

  // 节点点击回调
  const handleNodeClick = async ({ node, event }) => {
    console.log('点击了节点：', node)
    formType.value = node.type
    // 如果是叶子节点，加载用频计划和实际用频数据
    if (!node.hasChild) {
      VxeUI.loading.open({
        text: '加载中...'
      })
      try {
        // 这里应该调用获取用频数据的API
        const res = await readEquipmentPartInfo({
          id: node.id,
          type: node.type
        })
        if (res.code === 200) {
          switch (node.type) {
            case '1': // 发射机
              transmitterFormData.value = res.data.transmitter
              break
            case '2': // 接收机
              receiverFormData.value = res.data.receiver
              break
            case '3': // 天线
              antennaFormData.value = res.data.antenna
              break
            default:
              console.error('未知的设备类型')
          }
        }
      } catch (error) {
        console.error(error)
      } finally {
        VxeUI.loading.close()
      }
    }
  }

  onMounted(async () => {
    await readEquipmentPartTree().then(res => {
      treeList.value = res.data
    })
  })
</script>

<template>
  <el-row :gutter="20">
    <el-col :span="5">
      <vxe-tree
        lazy
        :is-current="true"
        :is-hover="true"
        :data="treeList"
        :load-method="getNodeListApi"
        @node-click="handleNodeClick"
      >
        <template #icon="{ isExpand }">
          <vxe-icon v-if="isExpand" status="success" name="square-minus" />
          <vxe-icon v-else status="success" name="square-plus" />
        </template>
      </vxe-tree>
    </el-col>
    <el-col :span="19">
      <transmitterForm
        v-if="formType === '1'"
        :transmitterFormData="transmitterFormData"
      ></transmitterForm>
      <receiverForm
        v-else-if="formType === '2'"
        :receiverFormData="receiverFormData"
      ></receiverForm>
      <antennaForm v-else-if="formType === '3'" :antennaFormData="antennaFormData"></antennaForm>
    </el-col>
  </el-row>
</template>

<style lang="scss" scoped>
  /* 在这里编写样式 */
</style>
