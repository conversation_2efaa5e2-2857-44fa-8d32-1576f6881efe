<script setup>
  import Highcharts from 'highcharts'

  const isTable = ref(false) // 控制图表和表格的显示
  let chartInstance = null // Highcharts 实例

  // 表格数据
  const radiationTypeArr = ref([
    { radiationType: '2GALE', count: 66 },
    { radiationType: '3GALE', count: 10 },
    { radiationType: 'LINK11_CLEW', count: 93 },
    { radiationType: 'LINK11_SLEW', count: 77 },
    { radiationType: 'LINK4A', count: 80 },
    { radiationType: 'LINK11_CLEW_UHF', count: 93 },
    { radiationType: 'LINK11_SLEW_UHF', count: 31 },
    { radiationType: 'LINK16', count: 39 },
    { radiationType: 'AIS', count: 31 },
    { radiationType: 'ADS_B', count: 7 },
    { radiationType: '敌我识别', count: 9 },
    { radiationType: '塔康', count: 27 },
    { radiationType: '导航', count: 34 },
    { radiationType: '移动通信', count: 74 },
    { radiationType: '民用广播', count: 44 },
    { radiationType: '2g', count: 90 },
    { radiationType: '3g', count: 25 },
    { radiationType: '4g', count: 62 },
    { radiationType: '5g', count: 100 },
    { radiationType: 'gps', count: 82 },
    { radiationType: '北斗', count: 96 }
  ])

  // 构造 Highcharts 饼图配置
  function makeChartOptions() {
    // 对数据按照数量降序排序，以便在图例中显示最重要的项目在前面
    const sortedData = [...radiationTypeArr.value].sort((a, b) => b.count - a.count)

    return {
      chart: {
        type: 'pie',
        backgroundColor: null,
        spacing: [10, 10, 30, 10], // 减小底部间距
        events: {
          load: function () {
            this.legend.update()
          }
        }
      },
      title: {
        text: '电磁辐射种类统计',
        style: { fontSize: '16px' }
      },
      tooltip: {
        pointFormat: '{series.name}: <b>{point.y} ({point.percentage:.1f}%)</b>'
      },
      plotOptions: {
        pie: {
          allowPointSelect: true,
          cursor: 'pointer',
          dataLabels: {
            enabled: true,
            distance: -30,
            format: '{point.percentage:.1f}%',
            filter: {
              property: 'percentage',
              operator: '>',
              value: 3
            },
            style: {
              fontWeight: 'normal',
              textOutline: '1px contrast',
              color: '#000'
            }
          },
          showInLegend: true,
          size: '85%', // 增大饼图尺寸，占据更多空间
          center: ['50%', '45%'] // 微调饼图位置
        }
      },
      legend: {
        enabled: true,
        layout: 'horizontal',
        align: 'center',
        verticalAlign: 'bottom',
        y: -15, // 向上移动图例（负值），但不要太近
        itemMarginTop: 5, // 减小图例项之间的垂直间距
        itemMarginBottom: 5,
        itemDistance: 10, // 减小图例项之间的水平间距
        itemStyle: {
          fontSize: '12px', // 稍微减小字体大小
          fontWeight: 'normal',
          textOverflow: 'ellipsis'
        },
        maxHeight: 160, // 减小最大高度
        symbolWidth: 12, // 减小图例符号尺寸
        symbolHeight: 12,
        symbolRadius: 5,
        navigation: {
          activeColor: '#1E88E5',
          inactiveColor: '#CBD5E0',
          animation: true,
          arrowSize: 10
        },
        itemWidth: 120, // 减小图例项宽度
        width: '100%',
        useHTML: true,
        labelFormatter: function () {
          return `<span title="${this.name}: ${this.y}">${this.name}</span>`
        }
      },
      series: [
        {
          name: '数量',
          colorByPoint: true,
          data: sortedData.map(item => ({
            name: item.radiationType,
            y: item.count
          }))
        }
      ],
      credits: { enabled: false },
      accessibility: { enabled: false },
      responsive: {
        rules: [
          {
            condition: {
              maxWidth: 500
            },
            chartOptions: {
              legend: {
                layout: 'vertical', // 小屏幕使用垂直布局
                align: 'right',
                verticalAlign: 'middle',
                y: 0,
                itemWidth: 120
              },
              plotOptions: {
                pie: {
                  size: '70%',
                  center: ['40%', '50%']
                }
              }
            }
          }
        ]
      }
    }
  }

  // 初始化图表
  function initChart() {
    const container = document.getElementById('radiationTypeContainer')
    if (!container) return

    // 设置图表容器的最小高度，确保有足够空间显示图例
    container.style.minHeight = '460px'

    chartInstance = Highcharts.chart(container, makeChartOptions())

    // 确保图例完全显示
    setTimeout(() => {
      if (chartInstance && chartInstance.legend) {
        chartInstance.legend.update({}, true)
      }
    }, 100)
  }

  // 当切换到"图表"模式时，延迟初始化或刷新
  watch(
    isTable,
    async val => {
      if (!val) {
        await nextTick()
        initChart()
      }
    },
    { immediate: true }
  )

  // 若 radiationTypeArr 变化，也自动刷新
  watch(radiationTypeArr, () => {
    if (chartInstance && !isTable.value) {
      // 获取排序后的数据
      const sortedData = [...radiationTypeArr.value].sort((a, b) => b.count - a.count)

      chartInstance.series[0].setData(
        sortedData.map(item => ({ name: item.radiationType, y: item.count })),
        true
      )
    }
  })

  // 窗口大小变化时重绘图表
  onMounted(() => {
    window.addEventListener('resize', () => {
      if (chartInstance && !isTable.value) {
        chartInstance.reflow()
      }
    })
  })
</script>

<template>
  <div class="flex justify-between items-center mb-2">
    <div>电磁辐射种类统计</div>
    <cu-button :content="isTable ? '图表' : '表格'" @click="isTable = !isTable"></cu-button>
  </div>

  <!-- 表格模式 -->
  <vxe-table
    v-if="isTable"
    :data="radiationTypeArr"
    border
    stripe
    size="medium"
    min-height="160"
    :row-config="{ isCurrent: true, isHover: true }"
  >
    <vxe-column field="radiationType" title="电磁辐射类型" align="center" />
    <vxe-column field="count" title="数量" align="center" />
  </vxe-table>

  <!-- 图表模式 -->
  <div v-else class="chart-container">
    <!-- 增加高度以容纳所有图例 -->
    <div id="radiationTypeContainer" style="width: 100%; height: 520px"></div>
  </div>
</template>

<style lang="scss" scoped>
  .chart-container {
    position: relative;
    margin-top: 12px;
  }

  /* 优化图例样式 */
  :deep(.highcharts-legend-item) {
    margin: 0 5px;
  }

  /* 确保图例区域有足够的空间 */
  #radiationTypeContainer {
    margin-bottom: 20px;
    padding: 10px; /* Added padding for better spacing */
  }
</style>
